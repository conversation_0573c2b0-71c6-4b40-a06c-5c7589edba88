<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('karyawan');

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Ambil data lokasi karyawan
$lokasi = null;
if ($karyawan['lokasi_id']) {
    $lokasi = getLokasiById($karyawan['lokasi_id']);
}

// Ambil data jam kerja berdasarkan bidang karyawan dan hari ini
$jam_kerja = null;
$hari_names = ['', 'Senin', 'Se<PERSON>a', '<PERSON><PERSON>', 'Ka<PERSON>', 'Jumat', 'Sabtu', 'Minggu'];
$hari_num = date('N'); // 1 (Senin) sampai 7 (Minggu)
$hari_name = $hari_names[$hari_num];

if ($karyawan['bidang_id']) {
    $query = "SELECT jk.*
              FROM jam_kerja_bidang jkb
              JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
              WHERE jkb.bidang_id = '{$karyawan['bidang_id']}'
              AND jkb.hari = '$hari_name'";
    $result = mysqli_query($conn, $query);
    if ($result && mysqli_num_rows($result) > 0) {
        $jam_kerja = mysqli_fetch_assoc($result);
    }
}

// Ambil data presensi hari ini
$today = date('Y-m-d');
$query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result = mysqli_query($conn, $query);
$presensi_hari_ini = mysqli_fetch_assoc($result);

// Cek apakah hari ini hari libur
$query = "SELECT * FROM hari_libur WHERE tanggal = '$today'";
$result = mysqli_query($conn, $query);
$hari_libur = mysqli_fetch_assoc($result);

// Cek apakah hari ini adalah hari kerja berdasarkan bidang karyawan
$is_hari_kerja = false;
if ($karyawan['bidang_id']) {
    $query = "SELECT * FROM hari_kerja WHERE bidang_id = '{$karyawan['bidang_id']}' AND hari = '$hari_name'";
    $result = mysqli_query($conn, $query);
    if ($result && mysqli_num_rows($result) > 0) {
        $hari_kerja_data = mysqli_fetch_assoc($result);
        $is_hari_kerja = (bool)$hari_kerja_data['status'];
    }
}

$is_weekend = !$is_hari_kerja;

// Proses absensi masuk
if (isset($_POST['absen_masuk'])) {
    // Cek apakah jam kerja sudah diatur
    if (!$jam_kerja) {
        setMessage('danger', 'Jam kerja belum diatur untuk bidang Anda. Silakan hubungi administrator.');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah sudah melewati jam akhir masuk
    $current_time = date('H:i:s');
    if ($current_time > $jam_kerja['akhir_jam_masuk']) {
        setMessage('danger', 'Anda tidak dapat melakukan absensi masuk karena sudah melewati jam akhir masuk (' . $jam_kerja['akhir_jam_masuk'] . ').');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah sudah melewati jam awal masuk
    if ($current_time < $jam_kerja['awal_jam_masuk']) {
        setMessage('warning', 'Anda belum dapat melakukan absensi masuk karena belum memasuki jam awal masuk (' . $jam_kerja['awal_jam_masuk'] . ').');
        redirect('karyawan/presensi.php');
    }

    // Kode untuk proses absensi masuk akan diimplementasikan nanti
    setMessage('info', 'Fitur absensi masuk dengan foto selfie dan GPS akan segera tersedia.');
    redirect('karyawan/presensi.php');
}

// Proses absensi pulang
if (isset($_POST['absen_pulang'])) {
    // Cek apakah sudah absen masuk
    if (empty($presensi_hari_ini)) {
        setMessage('danger', 'Anda belum melakukan absensi masuk!');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah jam kerja sudah diatur
    if (!$jam_kerja) {
        setMessage('danger', 'Jam kerja belum diatur untuk bidang Anda. Silakan hubungi administrator.');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah sudah melewati jam akhir pulang
    $current_time = date('H:i:s');
    if ($current_time > $jam_kerja['akhir_jam_pulang']) {
        setMessage('danger', 'Anda tidak dapat melakukan absensi pulang karena sudah melewati jam akhir pulang (' . $jam_kerja['akhir_jam_pulang'] . ').');
        redirect('karyawan/presensi.php');
    }

    // Kode untuk proses absensi pulang akan diimplementasikan nanti
    setMessage('info', 'Fitur absensi pulang dengan foto selfie dan GPS akan segera tersedia.');
    redirect('karyawan/presensi.php');
}

// Include header
include_once '../includes/header.php';

// Variabel untuk menentukan tampilan mobile
$is_mobile_presensi = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' && basename($_SERVER['PHP_SELF']) == 'presensi.php';
?>

<?php if ($is_mobile_presensi): ?>
<div class="mobile-presensi-container">
    <!-- Header Section -->
    <div class="presensi-header">
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                <div class="user-position"><?php echo $karyawan['bidang'] ?? 'Karyawan'; ?></div>
            </div>
        </div>
        <div class="date-info">
            <div><i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?></div>
            <div><i class="fas fa-map-marker-alt"></i> <?php echo $lokasi ? $lokasi['nama_lokasi'] : 'Belum diatur'; ?></div>
        </div>
    </div>

    <!-- Clock Section -->
    <div class="clock-container">
        <div class="clock" id="jam-sekarang"><?php echo date('H:i:s'); ?></div>
        <div class="date"><?php echo date('d F Y'); ?></div>
    </div>
<?php else: ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Presensi</h1>
        <div>
            <span class="badge bg-primary">Tanggal: <?php echo date('d-m-Y'); ?></span>
            <span class="badge bg-secondary">Jam: <span id="jam-sekarang"><?php echo date('H:i:s'); ?></span></span>
        </div>
    </div>
<?php endif; ?>

    <?php if ($hari_libur): ?>
        <?php if ($is_mobile_presensi): ?>
            <div class="status-card">
                <div class="status-title"><i class="fas fa-calendar-times"></i> Status Hari Ini</div>
                <div class="status-content">
                    <div class="status-badge warning">Hari Libur</div>
                    <p>Hari ini adalah hari libur: <?php echo $hari_libur['nama_libur']; ?>. Tidak perlu melakukan absensi.</p>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-warning">
                <h5 class="alert-heading">Hari Libur!</h5>
                <p>Hari ini adalah hari libur: <?php echo $hari_libur['nama_libur']; ?>. Tidak perlu melakukan absensi.</p>
            </div>
        <?php endif; ?>
    <?php elseif ($is_weekend): ?>
        <?php if ($is_mobile_presensi): ?>
            <div class="status-card">
                <div class="status-title"><i class="fas fa-calendar-times"></i> Status Hari Ini</div>
                <div class="status-content">
                    <div class="status-badge warning">Bukan Hari Kerja</div>
                    <p>Hari ini (<?php echo $hari_name; ?>) bukan merupakan hari kerja untuk bidang Anda. Tidak perlu melakukan absensi.</p>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-warning">
                <h5 class="alert-heading">Bukan Hari Kerja!</h5>
                <p>Hari ini (<?php echo $hari_name; ?>) bukan merupakan hari kerja untuk bidang Anda. Tidak perlu melakukan absensi.</p>
            </div>
        <?php endif; ?>
    <?php else: ?>
        <?php if ($is_mobile_presensi): ?>
            <!-- Mobile Presensi View -->
            <div class="container px-3">
                <!-- Status Absensi -->
                <div class="status-card">
                    <div class="status-title"><i class="fas fa-clipboard-check"></i> Status Absensi</div>
                    <div class="status-content">
                        <?php if (empty($presensi_hari_ini)): ?>
                            <div class="status-badge warning">Belum Absen</div>
                            <p>Anda belum melakukan absensi hari ini.</p>
                        <?php elseif (empty($presensi_hari_ini['jam_pulang'])): ?>
                            <div class="status-badge info">Sudah Absen Masuk</div>
                            <p>Jam Masuk: <?php echo $presensi_hari_ini['jam_masuk']; ?></p>
                            <p>Status: <?php echo $presensi_hari_ini['status']; ?></p>
                        <?php else: ?>
                            <div class="status-badge success">Absensi Lengkap</div>
                            <p>Jam Masuk: <?php echo $presensi_hari_ini['jam_masuk']; ?></p>
                            <p>Jam Pulang: <?php echo $presensi_hari_ini['jam_pulang']; ?></p>
                            <p>Status: <?php echo $presensi_hari_ini['status']; ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Camera Preview -->
                <?php if (empty($presensi_hari_ini)): ?>
                    <!-- Absen Masuk -->
                    <div class="camera-preview">
                        <i class="fas fa-camera"></i>
                        <div class="camera-overlay"></div>
                        <div class="camera-message">Kamera akan aktif saat Anda menekan tombol absen</div>
                    </div>

                    <form method="post" action="">
                        <button type="submit" name="absen_masuk" class="attendance-btn check-in">
                            <i class="fas fa-sign-in-alt"></i> Absen Masuk Sekarang
                        </button>
                    </form>

                    <div class="info-section">
                        <div class="info-title"><i class="fas fa-info-circle"></i> Informasi Jam Kerja</div>
                        <div class="info-content">
                            <ul>
                                <li>Awal Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['awal_jam_masuk'] : '-'; ?></li>
                                <li>Batas Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['jam_masuk'] : '-'; ?></li>
                                <li>Akhir Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['akhir_jam_masuk'] : '-'; ?></li>
                            </ul>
                        </div>
                    </div>
                <?php elseif (empty($presensi_hari_ini['jam_pulang'])): ?>
                    <!-- Absen Pulang -->
                    <div class="camera-preview">
                        <i class="fas fa-camera"></i>
                        <div class="camera-overlay"></div>
                        <div class="camera-message">Kamera akan aktif saat Anda menekan tombol absen</div>
                    </div>

                    <form method="post" action="">
                        <button type="submit" name="absen_pulang" class="attendance-btn check-out">
                            <i class="fas fa-sign-out-alt"></i> Absen Pulang Sekarang
                        </button>
                    </form>

                    <div class="info-section">
                        <div class="info-title"><i class="fas fa-info-circle"></i> Informasi Jam Kerja</div>
                        <div class="info-content">
                            <ul>
                                <li>Jam Pulang: <?php echo $jam_kerja ? $jam_kerja['jam_pulang'] : '-'; ?></li>
                                <li>Akhir Jam Pulang: <?php echo $jam_kerja ? $jam_kerja['akhir_jam_pulang'] : '-'; ?></li>
                            </ul>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Absensi Lengkap -->
                    <div class="info-section">
                        <div class="info-title"><i class="fas fa-check-circle"></i> Absensi Hari Ini Selesai</div>
                        <div class="info-content">
                            <p>Anda telah menyelesaikan absensi hari ini. Terima kasih atas kehadiran Anda.</p>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Lokasi Info -->
                <div class="info-section">
                    <div class="info-title"><i class="fas fa-map-marker-alt"></i> Informasi Lokasi</div>
                    <div class="info-content">
                        <?php if ($lokasi): ?>
                            <p>Nama Lokasi: <?php echo $lokasi['nama_lokasi']; ?></p>
                            <p>Koordinat: <?php echo $lokasi['latitude']; ?>, <?php echo $lokasi['longitude']; ?></p>
                            <p>Radius: <?php echo $lokasi['radius']; ?> meter</p>
                            <p class="text-warning">Pastikan Anda berada dalam radius lokasi yang ditentukan saat melakukan absensi.</p>
                        <?php else: ?>
                            <p class="text-danger">Lokasi absensi Anda belum diatur. Silakan hubungi administrator.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Desktop Presensi View (Part 1) -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold">Absensi Masuk</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($presensi_hari_ini)): ?>
                                <div class="text-center mb-4">
                                    <div id="camera-masuk" class="mb-3">
                                        <div class="alert alert-info">
                                            Kamera akan aktif saat fitur absensi dengan foto selfie tersedia.
                                        </div>
                                    </div>

                                    <form method="post" action="">
                                        <button type="submit" name="absen_masuk" class="btn btn-primary btn-lg">
                                            <i class="fas fa-camera"></i> Ambil Foto & Absen Masuk
                                        </button>
                                    </form>
                                </div>

                                <div class="alert alert-primary">
                                    <h6 class="alert-heading">Informasi Jam Kerja:</h6>
                                    <ul>
                                        <li>Awal Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['awal_jam_masuk'] : '-'; ?></li>
                                        <li>Batas Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['jam_masuk'] : '-'; ?></li>
                                        <li>Akhir Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['akhir_jam_masuk'] : '-'; ?></li>
                                    </ul>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-success">
                                    <h6 class="alert-heading">Anda sudah melakukan absensi masuk!</h6>
                                    <p>Jam Masuk: <?php echo $presensi_hari_ini['jam_masuk']; ?></p>
                                    <p>Status: <?php echo $presensi_hari_ini['status']; ?></p>
                                </div>

                                <?php if (!empty($presensi_hari_ini['foto_masuk'])): ?>
                                    <div class="text-center">
                                        <img src="<?php echo BASE_URL . 'uploads/' . $presensi_hari_ini['foto_masuk']; ?>" class="img-thumbnail" style="max-height: 200px;">
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold">Absensi Pulang</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($presensi_hari_ini) || empty($presensi_hari_ini['jam_pulang'])): ?>
                                <?php if (empty($presensi_hari_ini)): ?>
                                    <div class="alert alert-warning">
                                        <h6 class="alert-heading">Anda belum melakukan absensi masuk!</h6>
                                        <p>Silakan lakukan absensi masuk terlebih dahulu.</p>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center mb-4">
                                        <div id="camera-pulang" class="mb-3">
                                            <div class="alert alert-info">
                                                Kamera akan aktif saat fitur absensi dengan foto selfie tersedia.
                                            </div>
                                        </div>

                                        <form method="post" action="">
                                            <button type="submit" name="absen_pulang" class="btn btn-primary btn-lg">
                                                <i class="fas fa-camera"></i> Ambil Foto & Absen Pulang
                                            </button>
                                        </form>
                                    </div>

                                    <div class="alert alert-primary">
                                        <h6 class="alert-heading">Informasi Jam Kerja:</h6>
                                        <ul>
                                            <li>Jam Pulang: <?php echo $jam_kerja ? $jam_kerja['jam_pulang'] : '-'; ?></li>
                                            <li>Akhir Jam Pulang: <?php echo $jam_kerja ? $jam_kerja['akhir_jam_pulang'] : '-'; ?></li>
                                        </ul>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="alert alert-success">
                                    <h6 class="alert-heading">Anda sudah melakukan absensi pulang!</h6>
                                    <p>Jam Pulang: <?php echo $presensi_hari_ini['jam_pulang']; ?></p>
                                </div>

                                <?php if (!empty($presensi_hari_ini['foto_pulang'])): ?>
                                    <div class="text-center">
                                        <img src="<?php echo BASE_URL . 'uploads/' . $presensi_hari_ini['foto_pulang']; ?>" class="img-thumbnail" style="max-height: 200px;">
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informasi Lokasi</h6>
                </div>
                <div class="card-body">
                    <?php if ($lokasi): ?>
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Lokasi Absensi Anda:</h6>
                            <p>Nama Lokasi: <?php echo $lokasi['nama_lokasi']; ?></p>
                            <p>Koordinat: <?php echo $lokasi['latitude']; ?>, <?php echo $lokasi['longitude']; ?></p>
                            <p>Radius: <?php echo $lokasi['radius']; ?> meter</p>
                        </div>

                        <div class="alert alert-warning">
                            <p>Pastikan Anda berada dalam radius lokasi yang ditentukan saat melakukan absensi.</p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <h6 class="alert-heading">Lokasi Belum Diatur!</h6>
                            <p>Lokasi absensi Anda belum diatur. Silakan hubungi administrator.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<script>
    // Update jam secara real-time
    function updateJam() {
        var now = new Date();
        var hours = now.getHours().toString().padStart(2, '0');
        var minutes = now.getMinutes().toString().padStart(2, '0');
        var seconds = now.getSeconds().toString().padStart(2, '0');

        // Update semua elemen dengan id jam-sekarang
        var elements = document.querySelectorAll('#jam-sekarang');
        elements.forEach(function(element) {
            element.textContent = hours + ':' + minutes + ':' + seconds;
        });
    }

    // Update jam setiap detik
    setInterval(updateJam, 1000);
    updateJam();
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>