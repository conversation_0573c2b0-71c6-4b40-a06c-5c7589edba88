# AbsensiKu - Aplikasi Absensi Berbasis Foto Selfie dan GPS

AbsensiKu adalah aplikasi absensi karyawan berbasis web yang menggunakan foto selfie dan GPS untuk memverifikasi kehadiran karyawan. Aplikasi ini dibangun menggunakan PHP native dan MySQL.

## Fitur

### Halaman Admin
1. **Login** - Autentikasi admin
2. **Dashboard** - Ringkasan data absensi
3. **Monitoring Presensi** - Melihat data presensi karyawan (NIK, nama, tanggal, masuk, foto, lokasi, pulang, foto, lokasi, status, keterangan)
4. **Karyawan** - Manajemen data karyawan (NIK, nama, bidang, jabatan, lokasi, foto profil, password)
5. **Lokasi** - Manajemen lokasi absensi (nama lokasi, longitude, latitude, radius)
6. **Konfigurasi Jam Kerja** - Pengaturan jam kerja (bidang, nama jam kerja, awal jam masuk, jam masuk, jam pulang)
7. **Konfigurasi <PERSON>da** - Pengaturan denda (denda masuk, denda pulang, denda tidak absen masuk dan pulang)
8. **Pendaftaran Deteksi Wajah** - Manajemen data wajah karyawan untuk verifikasi
9. **Hari Libur** - Manajemen hari libur (nama libur, tanggal)
10. **Laporan Absensi Karyawan** - Laporan absensi (NIK, nama karyawan, hari efektif, jumlah kehadiran, denda keterlambatan)

### Halaman Karyawan (akan dikerjakan nanti)
1. **Login** - Autentikasi karyawan
2. **Dashboard** - Ringkasan data absensi karyawan
3. **Presensi** - Melakukan absensi masuk dan pulang dengan foto selfie dan GPS
4. **Riwayat Presensi** - Melihat riwayat absensi

## Teknologi yang Digunakan

- PHP 7.4+
- MySQL 5.7+
- Bootstrap 5
- jQuery
- DataTables
- Font Awesome
- HTML5 Geolocation API
- HTML5 Camera API

## Instalasi

1. Clone repositori ini ke direktori web server Anda (misalnya: `htdocs` untuk XAMPP)
   ```
   git clone https://github.com/username/absensiku.git
   ```

2. Buat database MySQL baru dengan nama `absensiku`

3. Import file `database.sql` ke database yang telah dibuat

4. Konfigurasi koneksi database di file `config/database.php` jika diperlukan

5. Buat folder `uploads` di root direktori dan pastikan folder tersebut memiliki izin tulis (writeable)

6. Akses aplikasi melalui browser
   ```
   http://localhost/absensiku
   ```

## Login Default

### Admin
- NIK: admin
- Password: password

## Struktur Folder

```
absensiku/
├── admin/             # Halaman admin
├── assets/            # File CSS, JavaScript, dan gambar
├── config/            # Konfigurasi aplikasi
├── includes/          # File PHP yang digunakan di beberapa halaman
├── karyawan/          # Halaman karyawan
├── uploads/           # Tempat menyimpan foto
├── index.php          # Halaman login
├── logout.php         # Proses logout
├── database.sql       # File SQL untuk membuat database
└── README.md          # Dokumentasi
```

## Pengembangan Selanjutnya

- Implementasi halaman karyawan
- Integrasi dengan API pengenalan wajah
- Notifikasi email/SMS untuk admin
- Laporan absensi dalam bentuk grafik
- Aplikasi mobile untuk karyawan

## Kontribusi

Kontribusi selalu diterima. Untuk perubahan besar, silakan buka issue terlebih dahulu untuk mendiskusikan apa yang ingin Anda ubah.

## Lisensi

[MIT](https://choosealicense.com/licenses/mit/)
