# Fitur Jam Kerja Fleksibel

## Deskripsi
Fitur jam kerja fleksibel memungkinkan admin untuk memberikan izin kepada karyawan tertentu untuk memilih jam kerja mereka sendiri dari daftar jam kerja yang tersedia di sistem.

## Cara Kerja

### 1. Akt<PERSON>si Fitur (Admin)
1. <PERSON>gin sebagai admin
2. Masuk ke menu **Data Karyawan**
3. <PERSON>lik tombol **"Tambah Kolom Jam Fleksibel"** untuk menambahkan kolom database (hanya perlu dilakukan sekali)
4. <PERSON><PERSON> tabel karyawan, lihat kolom **"Jam Kerja Fleksibel"**
5. Klik tombol hijau (✓) untuk mengaktifkan izin jam kerja fleksibel untuk karyawan tertentu
6. Klik tombol merah (✗) untuk menonaktifkan izin

### 2. Penggunaan Fitur (Karyawan)
1. <PERSON>gin sebagai karyawan yang telah diberi izin jam kerja fleksibel
2. <PERSON><PERSON><PERSON> ke halaman **Presensi**
3. <PERSON><PERSON> belum memilih jam kerja untuk hari ini, akan muncul pop-up otomatis untuk memilih jam kerja
4. <PERSON>lih jam kerja yang sesuai dari dropdown
5. Klik **"Pilih"** untuk menyimpan pilihan
6. Setelah memilih, dapat melakukan absensi normal

### 3. Informasi Jam Kerja
- Jam kerja yang dipilih akan ditampilkan di halaman presensi
- Informasi jam kerja juga muncul di halaman scan barcode
- Pilihan jam kerja berlaku untuk hari tersebut saja
- Karyawan perlu memilih ulang jam kerja setiap hari

## Database Schema

### Tabel `users`
Ditambahkan kolom:
- `allow_flexible_schedule` (TINYINT(1), DEFAULT 0) - Izin jam kerja fleksibel

### Tabel `user_schedule_choices`
Tabel baru untuk menyimpan pilihan jam kerja:
- `id` (INT, AUTO_INCREMENT, PRIMARY KEY)
- `user_id` (INT, FOREIGN KEY ke users.id)
- `tanggal` (DATE) - Tanggal pilihan jam kerja
- `jam_kerja_id` (INT, FOREIGN KEY ke jam_kerja.id)
- `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)

## File yang Dimodifikasi

### Admin Panel
- `admin/karyawan.php` - Tambah kolom dan tombol toggle
- `admin/toggle_permission.php` - Handler untuk mengubah izin
- `admin/add_flexible_schedule_permission.php` - Script setup database

### Karyawan Interface
- `karyawan/presensi.php` - Pop-up pemilihan jam kerja dan logika
- `karyawan/scan_barcode.php` - Validasi dan informasi jam kerja

### Database
- `sql/flexible_schedule.sql` - Script SQL untuk setup

## Keamanan
- Validasi jam kerja yang dipilih harus sesuai dengan bidang karyawan
- Unique constraint untuk mencegah duplikasi pilihan per hari
- Foreign key constraints untuk integritas data

## Catatan Penting
1. Karyawan hanya bisa memilih dari jam kerja yang tersedia untuk bidang mereka
2. Pilihan jam kerja harus dilakukan sebelum absensi
3. Jika karyawan belum memilih jam kerja, akan diarahkan untuk memilih terlebih dahulu
4. Fitur ini tidak mengganggu karyawan yang tidak diberi izin jam kerja fleksibel
