<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('karyawan');

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Include header
include_once '../includes/header.php';
?>

<div class="mobile-app-container">
    <!-- Header Section -->
    <div class="mobile-app-header">
        <div class="user-info">
            <div class="user-avatar">
                <?php if (!empty($karyawan['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $karyawan['foto_profil']; ?>" alt="Foto Profil">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                <div class="user-position"><?php echo $karyawan['bidang'] ?? 'Karyawan'; ?></div>
            </div>
        </div>
        <div class="date-info">
            <div><i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?></div>
        </div>

    </div>

    <div class="container px-0">
        <div class="card shadow mb-4">
            <div class="card-body p-3 p-md-4">
                <div class="text-center mb-3">
                    <div class="alert alert-info py-2">
                        <i class="fas fa-info-circle me-2"></i> Arahkan kamera ke barcode rapat untuk melakukan absensi
                    </div>
                </div>

                <!-- Container scanner dengan rasio aspek yang dipertahankan -->
                <div id="scanner-container" class="mb-3">
                    <div id="preview" class="scanner-preview"></div>
                </div>

                <div class="text-center mb-3">
                    <button id="toggleButton" class="btn btn-primary">
                        <i class="fas fa-camera"></i> Mulai Scan
                    </button>
                </div>

                <div id="scanResult" style="display: none;">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Hasil Scan</h5>
                            <div id="resultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Responsif container untuk scanner */
#scanner-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    overflow: hidden;
}

/* Preview kamera yang responsif */
.scanner-preview {
    width: 100%;
    height: 0;
    padding-bottom: 75%; /* Rasio aspek 4:3 */
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    background-color: #f8f9fa;
}

/* Tombol toggle kamera */
#toggleButton {
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
    display: block;
}

/* Hasil scan */
#scanResult {
    width: 100%;
    max-width: 600px;
    margin: 15px auto 0;
}

/* Styling untuk header */
.mobile-app-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.mobile-app-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.mobile-app-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    overflow: hidden;
    position: relative;
}

.mobile-app-header .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.mobile-app-header .user-details {
    flex: 1;
}

.mobile-app-header .user-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.mobile-app-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.mobile-app-header .date-info {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 15px;
    font-size: 14px;
    margin-bottom: 15px;
}

.mobile-app-header .date-info i {
    margin-right: 5px;
}

.mobile-app-header .back-link {
    position: absolute;
    top: 20px;
    right: 20px;
}

.mobile-app-header .back-link a {
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.mobile-app-header .back-link a:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Responsivitas untuk berbagai ukuran layar */
@media (min-width: 768px) {
    .scanner-preview {
        padding-bottom: 60%; /* Rasio aspek lebih lebar untuk layar besar */
    }

    .mobile-app-header {
        padding: 25px;
    }

    .mobile-app-header .user-avatar {
        width: 70px;
        height: 70px;
    }
}

@media (max-width: 576px) {
    .scanner-preview {
        padding-bottom: 100%; /* Rasio aspek 1:1 untuk layar kecil */
    }

    .mobile-app-header {
        padding: 15px;
    }

    .mobile-app-header .user-avatar {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .mobile-app-header .user-name {
        font-size: 16px;
    }

    .mobile-app-header .user-position {
        font-size: 12px;
    }

    .mobile-app-header .date-info {
        padding: 8px 12px;
        font-size: 12px;
    }

    .mobile-app-header .back-link {
        top: 15px;
        right: 15px;
    }

    .card {
        border-radius: 8px;
    }
}
</style>

<!-- Memuat library HTML5-QR-Code dari beberapa sumber untuk keandalan -->
<script>
    // Fungsi untuk memeriksa apakah library HTML5QrCode sudah dimuat
    function isHtml5QrCodeLoaded() {
        return typeof Html5Qrcode !== 'undefined';
    }

    // Fungsi untuk memuat script dari URL
    function loadScript(url, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load script from:', url);
            if (callback) callback(new Error('Failed to load script'));
        };
        document.head.appendChild(script);
    }

    // Daftar sumber library HTML5-QR-Code
    var scriptSources = [
        '../assets/js/html5-qrcode.min.js', // Prioritaskan file lokal
        'https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js',
        'https://cdn.jsdelivr.net/npm/html5-qrcode@2.3.8/html5-qrcode.min.js'
    ];

    // Fungsi untuk mencoba memuat script dari sumber berikutnya
    function tryLoadingFromNextSource(index) {
        if (isHtml5QrCodeLoaded()) {
            console.log('HTML5QrCode library already loaded');
            return;
        }

        if (index >= scriptSources.length) {
            console.error('Failed to load HTML5QrCode library from all sources');
            alert('Gagal memuat library pemindai barcode. Silakan muat ulang halaman atau coba browser lain.');
            return;
        }

        loadScript(scriptSources[index], function(error) {
            if (!error && isHtml5QrCodeLoaded()) {
                console.log('HTML5QrCode library loaded successfully from:', scriptSources[index]);
            } else {
                console.warn('Trying next source...');
                tryLoadingFromNextSource(index + 1);
            }
        });
    }

    // Mulai memuat library
    tryLoadingFromNextSource(0);
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let html5QrCode;
    let isScanning = false;
    const toggleButton = document.getElementById('toggleButton');
    const scanResult = document.getElementById('scanResult');
    const resultContent = document.getElementById('resultContent');
    let currentPosition = null;

    // Fungsi untuk mendapatkan lokasi
    function getLocation() {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                reject(new Error('Geolocation tidak didukung oleh browser Anda'));
                return;
            }

            navigator.geolocation.getCurrentPosition(
                position => {
                    currentPosition = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy
                    };
                    resolve(currentPosition);
                },
                error => {
                    reject(error);
                },
                { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
            );
        });
    }

    // Panggil getLocation saat halaman dimuat
    getLocation().catch(error => {
        console.error('Error getting location:', error);
        resultContent.innerHTML = `
            <div class="alert alert-danger">
                <p><strong>Error:</strong> Tidak dapat mengakses lokasi Anda.</p>
                <p>Pastikan GPS aktif dan izin lokasi diberikan.</p>
            </div>
        `;
        scanResult.style.display = 'block';
    });

    // Toggle scanner
    toggleButton.addEventListener('click', function() {
        if (isScanning) {
            stopScanner();
            toggleButton.innerHTML = '<i class="fas fa-camera"></i> Mulai Scan';
        } else {
            startScanner();
            toggleButton.innerHTML = '<i class="fas fa-stop"></i> Berhenti Scan';
        }
    });

    // Fungsi untuk memulai scanner
    function startScanner() {
        if (html5QrCode && html5QrCode.isScanning) {
            return;
        }

        // Periksa apakah library HTML5-QR-Code sudah dimuat
        if (!isHtml5QrCodeLoaded()) {
            console.error("HTML5QrCode library not loaded yet");
            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <p><strong>Error:</strong> Library pemindai barcode belum dimuat.</p>
                    <p>Silakan tunggu beberapa saat atau muat ulang halaman.</p>
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo"></i> Muat Ulang Halaman
                    </button>
                </div>
            `;
            scanResult.style.display = 'block';
            return;
        }

        try {
            // Pastikan elemen preview ada
            const previewElement = document.getElementById("preview");
            if (!previewElement) {
                throw new Error("Elemen preview tidak ditemukan");
            }

            // Bersihkan elemen preview terlebih dahulu
            previewElement.innerHTML = '';

            // Tambahkan kelas untuk styling
            previewElement.classList.add('preview-container');

            // Buat instance baru dengan konfigurasi yang lebih baik
            html5QrCode = new Html5Qrcode("preview");

            // Konfigurasi scanner yang responsif
            const scannerWidth = document.getElementById('preview').offsetWidth;
            const scannerHeight = document.getElementById('preview').offsetHeight;

            // Hitung ukuran qrbox yang responsif (70% dari dimensi terkecil)
            const minDimension = Math.min(scannerWidth, scannerHeight);
            const qrboxSize = Math.floor(minDimension * 0.7);

            const config = {
                fps: 10,
                qrbox: qrboxSize,
                aspectRatio: 1.0,
                formatsToSupport: ['CODE_128', 'CODE_39', 'QR_CODE', 'EAN_13']
            };

            // Konfigurasi kamera - gunakan pendekatan yang lebih sederhana
            const cameraConfig = {
                facingMode: "environment"
            };

            console.log("Starting camera with config:", config);

            // Coba dapatkan daftar kamera terlebih dahulu
            Html5Qrcode.getCameras().then(devices => {
                console.log("Kamera tersedia:", devices);

                // Gunakan kamera belakang jika tersedia
                let cameraId = null;
                if (devices && devices.length) {
                    // Prioritaskan kamera belakang
                    for (const device of devices) {
                        if (device.label.toLowerCase().includes("back") ||
                            device.label.toLowerCase().includes("belakang")) {
                            cameraId = device.id;
                            break;
                        }
                    }
                    // Jika tidak ada kamera belakang, gunakan kamera pertama
                    if (!cameraId) {
                        cameraId = devices[0].id;
                    }
                }

                // Mulai scanner dengan kamera yang dipilih atau konfigurasi default
                return html5QrCode.start(
                    cameraId || cameraConfig,
                    config,
                    onScanSuccess,
                    onScanFailure
                );
            }).then(() => {
                console.log("Camera started successfully");
                isScanning = true;
            }).catch(err => {
                console.error("Error starting camera:", err);

                // Coba fallback ke metode lain jika gagal dengan getCameras
                if (err.toString().includes("TypeError") || err.toString().includes("undefined")) {
                    console.log("Mencoba metode fallback...");
                    try {
                        // Recalculate qrbox size for fallback method
                        const scannerWidth = document.getElementById('preview').offsetWidth;
                        const scannerHeight = document.getElementById('preview').offsetHeight;
                        const minDimension = Math.min(scannerWidth, scannerHeight);
                        const qrboxSize = Math.floor(minDimension * 0.7);

                        const fallbackConfig = {
                            fps: 10,
                            qrbox: qrboxSize,
                            aspectRatio: 1.0,
                            formatsToSupport: ['CODE_128', 'CODE_39', 'QR_CODE', 'EAN_13']
                        };

                        // Coba langsung dengan facingMode tanpa getCameras
                        html5QrCode.start(
                            { facingMode: "environment" },
                            fallbackConfig,
                            onScanSuccess,
                            onScanFailure
                        ).then(() => {
                            console.log("Camera started with fallback method");
                            isScanning = true;

                            return;
                        }).catch(fallbackErr => {
                            console.error("Fallback method failed:", fallbackErr);
                            showCameraError(fallbackErr);
                        });
                    } catch (fallbackErr) {
                        console.error("Error in fallback method:", fallbackErr);
                        showCameraError(fallbackErr);
                    }
                } else {
                    showCameraError(err);
                }
            });
        } catch (err) {
            console.error("Error in startScanner:", err);
            showCameraError(err);
        }
    }

    // Fungsi untuk menampilkan error kamera
    function showCameraError(err) {
        resultContent.innerHTML = `
            <div class="alert alert-danger">
                <p><strong>Error:</strong> Tidak dapat mengakses kamera.</p>
                <p>Detail: ${err.message || err}</p>
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-redo"></i> Muat Ulang Halaman
                </button>
            </div>
        `;
        scanResult.style.display = 'block';
    }

    // Fungsi untuk menghentikan scanner
    function stopScanner() {
        if (html5QrCode && html5QrCode.isScanning) {
            html5QrCode.stop().then(() => {
                console.log('Scanner stopped');
                isScanning = false;

                // Hapus guide untuk wajah
                const faceGuide = document.querySelector('.face-guide');
                if (faceGuide) {
                    faceGuide.remove();
                }
            }).catch(err => {
                console.error('Error stopping scanner:', err);
            });
        }
    }

    // On scan success
    function onScanSuccess(decodedText, decodedResult) {
        // Stop scanning
        stopScanner();
        toggleButton.style.display = 'none';

        // Show result
        scanResult.style.display = 'block';
        resultContent.innerHTML = `
            <div class="alert alert-info">
                <p><strong>Barcode terdeteksi!</strong></p>
                <p>Memproses absensi rapat...</p>
            </div>
        `;

        // Check if location is available
        if (!currentPosition) {
            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <p><strong>Error:</strong> Lokasi tidak tersedia.</p>
                    <p>Pastikan GPS aktif dan izin lokasi diberikan.</p>
                </div>
            `;
            return;
        }

        // Process barcode
        processBarcode(decodedText);
    }

    // On scan failure
    function onScanFailure(error) {
        // console.warn(`Scan error: ${error}`);
    }

    // Process barcode
    function processBarcode(barcodeValue) {
        // Prepare data
        const data = {
            barcode_value: barcodeValue,
            latitude: currentPosition.latitude,
            longitude: currentPosition.longitude,
            accuracy: currentPosition.accuracy
        };

        // Show loading indicator
        resultContent.innerHTML = `
            <div class="alert alert-info">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>Memproses absensi rapat...</div>
                </div>
            </div>
        `;

        // Send data to server
        fetch('process_rapat.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Simpan pesan sukses ke session storage agar bisa diakses di halaman index
                sessionStorage.setItem('rapatSuccess', 'true');
                sessionStorage.setItem('rapatMessage', data.message);

                // Redirect ke halaman index
                window.location.href = 'index.php?rapat_status=success';
            } else {
                resultContent.innerHTML = `
                    <div class="alert alert-danger">
                        <p><strong>Error:</strong></p>
                        <p>${data.message}</p>
                    </div>
                    <div class="text-center mt-3">
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-redo"></i> Coba Lagi
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <p><strong>Error:</strong> Terjadi kesalahan saat memproses barcode.</p>
                    <p>Detail: ${error.message || error}</p>
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo"></i> Coba Lagi
                    </button>
                </div>
            `;
        });
    }

    // Fungsi untuk menangani perubahan ukuran layar
    function handleResize() {
        if (html5QrCode && html5QrCode.isScanning) {
            // Hentikan scanner
            stopScanner();

            // Mulai ulang scanner setelah jeda singkat
            setTimeout(() => {
                startScanner();
            }, 500);
        }
    }

    // Tambahkan event listener untuk perubahan ukuran layar
    // Gunakan debounce untuk menghindari terlalu banyak pemanggilan
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleResize, 500);
    });

    // Mulai scanner secara otomatis saat halaman dimuat
    // Tunggu library dimuat sebelum memulai scanner
    if (isHtml5QrCodeLoaded()) {
        // Library sudah dimuat, mulai scanner
        startScanner();
    } else {
        // Tunggu library dimuat
        let libraryCheckInterval = setInterval(function() {
            if (isHtml5QrCodeLoaded()) {
                clearInterval(libraryCheckInterval);
                startScanner();
            }
        }, 500);

        // Timeout setelah 10 detik jika library tidak dimuat
        setTimeout(function() {
            clearInterval(libraryCheckInterval);
            if (!isHtml5QrCodeLoaded()) {
                resultContent.innerHTML = `
                    <div class="alert alert-danger">
                        <p><strong>Error:</strong> Gagal memuat library pemindai barcode dalam waktu yang ditentukan.</p>
                        <p>Silakan coba muat ulang halaman.</p>
                    </div>
                    <div class="text-center mt-3">
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-redo"></i> Muat Ulang Halaman
                        </button>
                    </div>
                `;
                scanResult.style.display = 'block';
                toggleButton.disabled = true;
            }
        }, 10000);
    }
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
