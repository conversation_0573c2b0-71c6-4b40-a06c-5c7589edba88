<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/pesan_functions.php';

// Cek akses
checkAccess('karyawan');

// Proses pilihan jam kerja fleksibel
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['pilih_jam_kerja'])) {
    $user_id = $_SESSION['user_id'];
    $jam_kerja_id = clean($_POST['jam_kerja_id']);
    $today = date('Y-m-d');

    // Ambil data user
    $user = getKaryawanById($user_id);

    // Validasi jam kerja ID
    $query = "SELECT jk.*
              FROM jam_kerja jk
              JOIN jam_kerja_bidang jkb ON jk.id = jkb.jam_kerja_id
              WHERE jk.id = '$jam_kerja_id' AND jkb.bidang_id = '{$user['bidang_id']}'";
    $result = mysqli_query($conn, $query);

    if (!$result || mysqli_num_rows($result) == 0) {
        $_SESSION['error_message'] = 'Jam kerja tidak valid atau tidak tersedia untuk bidang Anda!';
        redirect('index.php');
    }

    $jam_kerja_data = mysqli_fetch_assoc($result);

    // Cek apakah sudah ada pilihan untuk hari ini
    $query = "SELECT * FROM user_schedule_choices WHERE user_id = '$user_id' AND tanggal = '$today'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        // Update pilihan yang sudah ada
        $query = "UPDATE user_schedule_choices
                  SET jam_kerja_id = '$jam_kerja_id', updated_at = NOW()
                  WHERE user_id = '$user_id' AND tanggal = '$today'";
    } else {
        // Insert pilihan baru
        $query = "INSERT INTO user_schedule_choices (user_id, jam_kerja_id, tanggal, created_at)
                  VALUES ('$user_id', '$jam_kerja_id', '$today', NOW())";
    }

    if (mysqli_query($conn, $query)) {
        $_SESSION['success_message'] = 'Jam kerja berhasil dipilih: ' . $jam_kerja_data['nama_jam_kerja'];
        redirect('index.php');
    } else {
        $_SESSION['error_message'] = 'Gagal menyimpan pilihan jam kerja: ' . mysqli_error($conn);
        redirect('index.php');
    }
}

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Cek perizinan absensi
$query = "SELECT allow_barcode, allow_face, allow_flexible_schedule FROM users WHERE id = '$user_id'";
$result = mysqli_query($conn, $query);
$permissions = mysqli_fetch_assoc($result);

$allow_barcode = isset($permissions['allow_barcode']) && $permissions['allow_barcode'] == 1;
$allow_face = isset($permissions['allow_face']) && $permissions['allow_face'] == 1;
$allow_flexible_schedule = isset($permissions['allow_flexible_schedule']) && $permissions['allow_flexible_schedule'] == 1;

// Ambil data presensi hari ini
$today = date('Y-m-d');
$query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result = mysqli_query($conn, $query);
$presensi_hari_ini = mysqli_fetch_assoc($result);

// Logika untuk jam kerja fleksibel
$selected_schedule = null;
$jam_kerja_options = [];
$jam_kerja = null;

if ($allow_flexible_schedule) {
    // Cek apakah karyawan sudah memilih jam kerja untuk hari ini
    $query = "SELECT usc.*, jk.*
              FROM user_schedule_choices usc
              JOIN jam_kerja jk ON usc.jam_kerja_id = jk.id
              WHERE usc.user_id = '$user_id' AND usc.tanggal = '$today'";
    $result = mysqli_query($conn, $query);
    if ($result && mysqli_num_rows($result) > 0) {
        $selected_schedule = mysqli_fetch_assoc($result);
        $jam_kerja = $selected_schedule; // Set jam kerja dari jadwal yang dipilih
    }

    // Ambil semua jam kerja yang tersedia untuk bidang ini
    if ($karyawan['bidang_id']) {
        $query = "SELECT DISTINCT jk.*
                  FROM jam_kerja jk
                  JOIN jam_kerja_bidang jkb ON jk.id = jkb.jam_kerja_id
                  WHERE jkb.bidang_id = '{$karyawan['bidang_id']}'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $jam_kerja_options[] = $row;
            }
        }
    }
}

// Cek apakah hari ini adalah hari libur
$hari_libur = cekHariLibur($today);

// Ambil data presensi bulan ini
$bulan = date('m');
$tahun = date('Y');
$query = "SELECT COUNT(*) as total FROM presensi
          WHERE user_id = '$user_id'
          AND MONTH(tanggal) = '$bulan'
          AND YEAR(tanggal) = '$tahun'";
$result = mysqli_query($conn, $query);
$row = mysqli_fetch_assoc($result);
$total_presensi_bulan_ini = $row['total'];

// Ambil data keterlambatan bulan ini
$query = "SELECT COUNT(*) as total FROM presensi
          WHERE user_id = '$user_id'
          AND status = 'Terlambat'
          AND MONTH(tanggal) = '$bulan'
          AND YEAR(tanggal) = '$tahun'";
$result = mysqli_query($conn, $query);
$row = mysqli_fetch_assoc($result);
$total_terlambat_bulan_ini = $row['total'];

// Ambil data jam kerja
$hari_names = ['', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
$hari_num = date('N'); // 1 (Senin) sampai 7 (Minggu)
$hari_name = $hari_names[$hari_num];

if ($karyawan['bidang_id']) {
    // Jika karyawan diizinkan memilih jam kerja fleksibel dan sudah memilih jadwal
    if ($allow_flexible_schedule && $selected_schedule) {
        $jam_kerja = $selected_schedule;
    } else if (!$allow_flexible_schedule) {
        // Gunakan jam kerja default berdasarkan hari
        $query = "SELECT jk.*
                  FROM jam_kerja_bidang jkb
                  JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
                  WHERE jkb.bidang_id = '{$karyawan['bidang_id']}'
                  AND jkb.hari = '$hari_name'";
        $result = mysqli_query($conn, $query);
        if ($result && mysqli_num_rows($result) > 0) {
            $jam_kerja = mysqli_fetch_assoc($result);
        }
    }
}

// Ambil data izin dinas yang aktif
$today = date('Y-m-d');
$query = "SELECT * FROM izin_dinas
          WHERE user_id = '$user_id'
          AND status = 'Approved'
          AND tanggal_mulai <= '$today'
          AND tanggal_selesai >= '$today'";
$result = mysqli_query($conn, $query);
$izin_dinas_aktif = mysqli_fetch_assoc($result);

// Cek apakah karyawan memiliki jadwal rapat hari ini
$has_rapat_today = hasRapatToday($user_id);
$rapat_list_today = getRapatTodayForUser($user_id);

// Cek pesan peringatan aktif untuk karyawan
$pesan_peringatan_aktif = getPesanPeringatanAktif($user_id);
$jumlah_pesan_aktif = count($pesan_peringatan_aktif);

// Include header
include_once '../includes/header.php';
?>

<div class="mobile-app-container">
    <!-- Header Section -->
    <div class="mobile-app-header">
        <div class="header-top">
            <div class="user-info">
                <div class="user-avatar">
                     <?php if (!empty($karyawan['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $karyawan['foto_profil']; ?>" alt="Foto Profil" style="width: 100px; height: 100px; ">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
                </div>
                <div class="user-details">
                    <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                    <div class="user-position"><?php echo $karyawan['bidang'] ?? 'Karyawan'; ?></div>
                </div>
            </div>
        </div>

        <div class="header-bottom">
            <div class="work-schedule-info">
                <?php if ($allow_flexible_schedule && $selected_schedule): ?>
                    <div class="schedule-info flexible">
                        <i class="fas fa-clock"></i>
                        <span><?php echo $selected_schedule['nama_jam_kerja']; ?> (<?php echo $jam_kerja['jam_masuk']; ?> - <?php echo $jam_kerja['jam_pulang']; ?>)</span>
                        <i class="fas fa-check-circle status-icon"></i>
                    </div>
                <?php elseif ($allow_flexible_schedule && !$selected_schedule): ?>
                    <div class="schedule-info pending">
                        <i class="fas fa-clock"></i>
                        <span>Jam Kerja Fleksibel - Belum dipilih</span>
                        <i class="fas fa-exclamation-circle status-icon"></i>
                    </div>
                <?php elseif ($jam_kerja): ?>
                    <div class="schedule-info default">
                        <i class="fas fa-clock"></i>
                        <span><?php echo $jam_kerja['nama_jam_kerja'] ?? 'Jam Kerja'; ?> (<?php echo $jam_kerja['jam_masuk']; ?> - <?php echo $jam_kerja['jam_pulang']; ?>)</span>
                    </div>
                <?php else: ?>
                    <div class="schedule-info no-schedule">
                        <i class="fas fa-clock"></i>
                        <span>Jam Kerja - Belum diatur</span>
                    </div>
                <?php endif; ?>
            </div>
        </div>
                <a href="<?php echo BASE_URL; ?>logout.php" class="logout-button" title="Logout" style="position: absolute; top: 20px; right: 20px;">
            <i class="fas fa-sign-out-alt"></i>
        </a>
    </div>

    <!-- Status Section -->
    <div class="container px-0">
        <?php if ($izin_dinas_aktif): ?>
        <div class="alert alert-info mb-3">
            <i class="fas fa-plane me-2"></i> Anda sedang dalam perjalanan dinas ke <strong><?php echo $izin_dinas_aktif['tujuan']; ?></strong> sampai tanggal <?php echo date('d/m/Y', strtotime($izin_dinas_aktif['tanggal_selesai'])); ?>
        </div>
        <?php endif; ?>

        <!-- Status Card -->
        <div class="status-card">
            <div class="status-title">Status Absensi Hari Ini</div>
            <div class="status-content">
                <div class="status-value">
                    <?php if ($hari_libur): ?>
                        <span class="status-badge danger">Hari Libur</span>
                        <div class="holiday-name"><?php echo $hari_libur['nama_libur']; ?></div>
                    <?php elseif (empty($presensi_hari_ini)): ?>
                        <span class="status-badge warning">Belum Absen</span>
                    <?php elseif (empty($presensi_hari_ini['jam_pulang'])): ?>
                        <span class="status-badge info">Sudah Absen Masuk</span>
                    <?php else: ?>
                        <span class="status-badge success">Sudah Absen Lengkap</span>
                    <?php endif; ?>
                </div>
                <div class="status-icon <?php echo $hari_libur ? 'danger' : 'primary'; ?>">
                    <i class="<?php echo $hari_libur ? 'fas fa-calendar-times' : 'fas fa-calendar-check'; ?>"></i>
                </div>
            </div>
        </div>


        <!-- Quick Actions -->
        <div class="section-header">
            <h6 class="mb-3">Menu Cepat</h6>
            <div class="d-flex align-items-center">
                <span class="me-2 text-primary" id="menuLainnyaText" style="font-size: 14px; font-weight: 500; cursor: pointer; user-select: none;">Menu Lainnya</span>
                <button type="button" class="toggle-arrow-button" id="toggleAdditionalMenu" style="background: none; border: none; color: #4e73df; font-size: 16px; cursor: pointer; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; background-color: rgba(78, 115, 223, 0.1);">
                    <i class="fas fa-chevron-down" id="toggleIcon" style="transition: transform 0.3s ease;"></i>
                </button>
            </div>
        </div>
        <div class="quick-actions">
            <?php if ($izin_dinas_aktif): ?>
            <a href="javascript:void(0)" class="action-button" onclick="showDinasAlert()">
                <div class="action-icon" style="background-color: #adb5bd;">
                    <i class="fas fa-camera"></i>
                </div>
                <div class="action-text" style="color: #6c757d;">Absensi</div>
            </a>
            <?php elseif ($hari_libur): ?>
            <a href="javascript:void(0)" class="action-button" onclick="showHolidayAlert()">
                <div class="action-icon danger">
                    <i class="fas fa-calendar-times"></i>
                </div>
                <div class="action-text" style="color: #721c24;">Absensi</div>
            </a>
            <?php elseif (!$allow_face && !$allow_barcode): ?>
            <a href="javascript:void(0)" class="action-button" onclick="showNoPermissionAlert()">
                <div class="action-icon" style="background-color: #adb5bd;">
                    <i class="fas fa-ban"></i>
                </div>
                <div class="action-text" style="color: #6c757d;">Absensi</div>
            </a>
            <?php else: ?>
            <?php if ($allow_face && $allow_barcode): ?>
            <!-- Jika diizinkan keduanya, arahkan ke halaman presensi -->
            <a href="presensi.php" class="action-button">
                <div class="action-icon primary">
                    <i class="fas fa-camera"></i>
                </div>
                <div class="action-text">Absensi</div>
            </a>
            <?php elseif ($allow_face): ?>
            <!-- Jika hanya diizinkan wajah, arahkan ke halaman presensi -->
            <a href="presensi.php" class="action-button">
                <div class="action-icon primary">
                    <i class="fas fa-camera"></i>
                </div>
                <div class="action-text">Absensi Wajah</div>
            </a>
            <?php elseif ($allow_barcode): ?>
            <!-- Jika hanya diizinkan barcode, arahkan langsung ke halaman scan barcode -->
            <a href="scan_barcode.php?type=<?php echo empty($presensi_hari_ini) ? 'masuk' : (empty($presensi_hari_ini['jam_pulang']) ? 'pulang' : 'masuk'); ?>" class="action-button">
                <div class="action-icon primary">
                    <i class="fas fa-qrcode"></i>
                </div>
                <div class="action-text">Scan Barcode</div>
            </a>
            <?php endif; ?>
            <?php endif; ?>
            <a href="riwayat.php" class="action-button">
                <div class="action-icon success">
                    <i class="fas fa-history"></i>
                </div>
                <div class="action-text">Riwayat</div>
            </a>
            <?php if ($has_rapat_today): ?>
            <a href="scan_rapat.php" class="action-button">
                <div class="action-icon primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="action-text">Scan Rapat</div>
            </a>
            <?php else: ?>
            <a href="javascript:void(0)" class="action-button" onclick="showNoRapatAlert()">
                <div class="action-icon" style="background-color: #adb5bd;">
                    <i class="fas fa-users"></i>
                </div>
                <div class="action-text" style="color: #6c757d;">Scan Rapat</div>
            </a>
            <?php endif; ?>
            <a href="izin_dinas.php" class="action-button">
                <div class="action-icon warning">
                    <i class="fas fa-plane"></i>
                </div>
                <div class="action-text">Izin Dinas</div>
            </a>
        </div>

        <!-- Additional Quick Actions -->
        <div id="additionalMenu" class="additional-menu" style="background-color: rgba(246, 246, 247, 0.95); border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); margin-bottom: 20px; overflow: hidden; transition: all 0.3s ease; max-height: 0; opacity: 0;">
            <div class="quick-actions">
                <a href="gangguan_absensi.php" class="action-button">
                    <div class="action-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="action-text">Gangguan Absensi</div>
                </a>
                <a href="<?php echo BASE_URL; ?>help.php" class="action-button">
                    <div class="action-icon primary">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="action-text">Bantuan</div>
                </a>
                <a href="<?php echo BASE_URL; ?>change_password.php" class="action-button">
                    <div class="action-icon success">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="action-text">Ubah Password</div>
                </a>
                <?php if ($allow_flexible_schedule && !empty($jam_kerja_options)): ?>
                <a href="#" class="action-button" onclick="showScheduleSelectionModal()">
                    <div class="action-icon primary">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="action-text">Pilih Jam Kerja</div>
                </a>
                <?php else: ?>
                <a href="#" class="action-button" onclick="window.location.reload()">
                    <div class="action-icon warning">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="action-text">Refresh</div>
                </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Modal Pilih Jam Kerja -->
        <div class="modal fade" id="scheduleSelectionModal" tabindex="-1" aria-labelledby="scheduleSelectionModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="scheduleSelectionModalLabel">Pilih Jam Kerja</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="jam_kerja_id" class="form-label">Pilih Jadwal Kerja Hari Ini:</label>
                                <select class="form-select" id="jam_kerja_id" name="jam_kerja_id" required>
                                    <?php foreach ($jam_kerja_options as $option): ?>
                                    <option value="<?php echo $option['id']; ?>">
                                        <?php echo $option['nama_jam_kerja']; ?> (<?php echo $option['jam_masuk']; ?> - <?php echo $option['jam_pulang']; ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="d-grid">
                                <button type="submit" name="pilih_jam_kerja" class="btn btn-primary">Simpan Pilihan</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Section -->
        <div class="info-section">
            <div class="info-title">
                <i class="fas fa-info-circle"></i> Informasi Absensi
            </div>
            <div class="info-content">
                <ol>
                    <li>Absensi masuk dapat dilakukan mulai pukul <?php echo $jam_kerja ? $jam_kerja['awal_jam_masuk'] : '07:30'; ?></li>
                    <li>Batas waktu absensi masuk adalah pukul <?php echo $jam_kerja ? $jam_kerja['jam_masuk'] : '08:00'; ?></li>
                    <li>Absensi pulang dapat dilakukan mulai pukul <?php echo $jam_kerja ? $jam_kerja['jam_pulang'] : '17:00'; ?></li>
                    <li>Absensi harus dilakukan di lokasi yang telah ditentukan</li>
                    <li>Pastikan kamera dan GPS perangkat Anda aktif saat melakukan absensi</li>
                </ol>
            </div>
        </div>

        <?php if (!empty($rapat_list_today)): ?>
        <!-- Jadwal Rapat Section -->
        <div class="info-section mt-3">
            <div class="info-title">
                <i class="fas fa-users"></i> Jadwal Rapat Hari Ini
                <?php
                $belum_hadir = 0;
                foreach ($rapat_list_today as $rapat) {
                    if ($rapat['status'] != 'hadir') {
                        $belum_hadir++;
                    }
                }
                if ($belum_hadir > 0):
                ?>
                <span class="badge bg-warning text-dark" style="font-size: 10px; vertical-align: middle;"><?php echo $belum_hadir; ?> belum hadir</span>
                <?php endif; ?>
            </div>
            <div class="info-content p-0">
                <div class="list-group list-group-flush">
                    <?php foreach ($rapat_list_today as $rapat): ?>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1"><?php echo $rapat['judul']; ?></h6>
                                <p class="mb-1 small"><i class="fas fa-map-marker-alt"></i> <?php echo $rapat['lokasi']; ?></p>
                                <p class="mb-1 small"><i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($rapat['waktu_mulai'])) . ' - ' . date('H:i', strtotime($rapat['waktu_selesai'])); ?></p>
                            </div>
                            <div>
                                <?php if ($rapat['status'] == 'hadir'): ?>
                                <span class="badge bg-success">Sudah Hadir</span>
                                <?php else: ?>
                                <span class="badge bg-warning text-dark">Belum Hadir</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Floating Attendance Button -->
        <?php if ($izin_dinas_aktif): ?>
        <a href="javascript:void(0)" class="attendance-button" style="background: linear-gradient(135deg, #adb5bd 0%, #6c757d 100%);" onclick="showDinasAlert()" title="Anda sedang dalam perjalanan dinas">
            <i class="fas fa-camera"></i>
        </a>
        <?php elseif ($hari_libur): ?>
        <a href="javascript:void(0)" class="attendance-button" style="background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);" onclick="showHolidayAlert()" title="Hari ini adalah hari libur">
            <i class="fas fa-calendar-times"></i>
        </a>
        <?php elseif (!$allow_face && !$allow_barcode): ?>
        <a href="javascript:void(0)" class="attendance-button" style="background: linear-gradient(135deg, #adb5bd 0%, #6c757d 100%);" onclick="showNoPermissionAlert()" title="Anda tidak memiliki izin untuk melakukan absensi">
            <i class="fas fa-ban"></i>
        </a>
        <?php else: ?>
            <?php if ($allow_face && $allow_barcode): ?>
            <!-- Jika diizinkan keduanya, arahkan ke halaman presensi -->
            <a href="presensi.php" class="attendance-button" title="Mulai Absensi">
                <i class="fas fa-camera"></i>
            </a>
            <?php elseif ($allow_face): ?>
            <!-- Jika hanya diizinkan wajah, arahkan ke halaman presensi -->
            <a href="presensi.php" class="attendance-button" title="Mulai Absensi Wajah">
                <i class="fas fa-camera"></i>
            </a>
            <?php elseif ($allow_barcode): ?>
            <!-- Jika hanya diizinkan barcode, arahkan langsung ke halaman scan barcode -->
            <a href="scan_barcode.php?type=<?php echo empty($presensi_hari_ini) ? 'masuk' : (empty($presensi_hari_ini['jam_pulang']) ? 'pulang' : 'masuk'); ?>" class="attendance-button" title="Scan Barcode">
                <i class="fas fa-qrcode"></i>
            </a>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Modal Pesan Peringatan -->
<?php if ($jumlah_pesan_aktif > 0): ?>
<div class="modal fade" id="pesanPeringatanModal" tabindex="-1" aria-labelledby="pesanPeringatanModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="pesanPeringatanModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Pesan Peringatan
                </h5>
                <span class="badge bg-light text-danger ms-2"><?php echo $jumlah_pesan_aktif; ?> Pesan</span>
            </div>
            <div class="modal-body p-0">
                <div class="pesan-container" style="max-height: 60vh; overflow-y: auto;">
                    <?php foreach ($pesan_peringatan_aktif as $index => $pesan): ?>
                        <?php $format = formatTingkatPeringatan($pesan['tingkat_peringatan']); ?>
                        <div class="pesan-item border-bottom" data-pesan-id="<?php echo $pesan['id']; ?>">
                            <div class="p-4">
                                <div class="d-flex align-items-start mb-3">
                                    <div class="pesan-icon me-3">
                                        <i class="<?php echo $format['icon']; ?> fa-2x <?php echo $format['text_class']; ?>"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0 fw-bold"><?php echo htmlspecialchars($pesan['judul']); ?></h6>
                                            <span class="badge <?php echo $format['bg_class']; ?> text-white">
                                                <?php echo $format['label']; ?>
                                            </span>
                                        </div>
                                        <div class="pesan-meta mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo date('d/m/Y H:i', strtotime($pesan['tanggal_dibuat'])); ?>
                                                <span class="mx-2">•</span>
                                                <i class="fas fa-user me-1"></i>
                                                <?php echo htmlspecialchars($pesan['admin_nama']); ?>
                                                <span class="mx-2">•</span>
                                                <i class="fas fa-tag me-1"></i>
                                                <?php echo formatJenisPelanggaran($pesan['jenis_pelanggaran']); ?>
                                            </small>
                                        </div>
                                        <div class="pesan-content">
                                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($pesan['pesan'])); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <button type="button" class="btn btn-sm btn-outline-primary tandai-dibaca-btn"
                                            data-pesan-id="<?php echo $pesan['id']; ?>">
                                        <i class="fas fa-check me-1"></i>
                                        Tandai Sudah Dibaca
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div class="text-muted">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            Harap baca dan pahami semua pesan peringatan dengan baik
                        </small>
                    </div>
                    <button type="button" class="btn btn-secondary" id="tutupModalBtn" disabled>
                        <i class="fas fa-times me-1"></i>
                        Tutup
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<!-- Script untuk Modal Pesan Peringatan -->
<?php if ($jumlah_pesan_aktif > 0): ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tampilkan modal pesan peringatan saat halaman dimuat
    const pesanModal = new bootstrap.Modal(document.getElementById('pesanPeringatanModal'));
    pesanModal.show();

    let pesanDibaca = 0;
    const totalPesan = <?php echo $jumlah_pesan_aktif; ?>;
    const tutupBtn = document.getElementById('tutupModalBtn');

    // Handle tandai dibaca buttons
    document.querySelectorAll('.tandai-dibaca-btn').forEach(button => {
        button.addEventListener('click', function() {
            const pesanId = this.getAttribute('data-pesan-id');
            const pesanItem = this.closest('.pesan-item');

            // Disable button dan ubah text
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Memproses...';

            // Kirim request ke API
            fetch('../api/tandai_pesan_dibaca.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    pesan_id: pesanId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update button
                    this.innerHTML = '<i class="fas fa-check me-1"></i> Sudah Dibaca';
                    this.classList.remove('btn-outline-primary');
                    this.classList.add('btn-success');

                    // Add visual feedback
                    pesanItem.style.opacity = '0.6';
                    pesanItem.style.background = '#f8f9fa';

                    // Update counter
                    pesanDibaca++;

                    // Enable tutup button jika semua pesan sudah dibaca
                    if (pesanDibaca >= totalPesan) {
                        tutupBtn.disabled = false;
                        tutupBtn.classList.remove('btn-secondary');
                        tutupBtn.classList.add('btn-primary');
                        tutupBtn.innerHTML = '<i class="fas fa-check me-1"></i> Semua Sudah Dibaca - Tutup';

                        // Auto close setelah 2 detik
                        setTimeout(() => {
                            pesanModal.hide();
                        }, 2000);
                    }
                } else {
                    // Error handling
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i> Gagal';
                    this.classList.add('btn-danger');

                    // Show error message
                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal',
                            text: data.message || 'Gagal menandai pesan sebagai dibaca',
                            timer: 3000
                        });
                    } else {
                        alert('Gagal menandai pesan sebagai dibaca: ' + (data.message || 'Unknown error'));
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i> Error';
                this.classList.add('btn-danger');

                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Terjadi kesalahan saat memproses permintaan',
                        timer: 3000
                    });
                } else {
                    alert('Terjadi kesalahan saat memproses permintaan');
                }
            });
        });
    });

    // Handle tutup modal button
    tutupBtn.addEventListener('click', function() {
        if (pesanDibaca < totalPesan) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'warning',
                    title: 'Peringatan',
                    text: 'Anda harus membaca semua pesan peringatan terlebih dahulu sebelum menutup modal ini.',
                    confirmButtonText: 'Saya Mengerti'
                });
            } else {
                alert('Anda harus membaca semua pesan peringatan terlebih dahulu sebelum menutup modal ini.');
            }
        } else {
            pesanModal.hide();
        }
    });
});
</script>

<style>
.pesan-item {
    transition: all 0.3s ease;
}

.pesan-item:hover {
    background-color: #f8f9fa;
}

.pesan-icon {
    min-width: 60px;
}

.pesan-meta {
    font-size: 0.85em;
}

.pesan-content {
    line-height: 1.6;
}

.tandai-dibaca-btn {
    transition: all 0.3s ease;
}

.modal-header.bg-danger {
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.modal-footer.bg-light {
    border-top: 1px solid #dee2e6;
}

@media (max-width: 768px) {
    .modal-dialog {
        margin: 10px;
    }

    .pesan-container {
        max-height: 50vh !important;
    }

    .pesan-item .p-4 {
        padding: 1rem !important;
    }

    .pesan-icon {
        min-width: 50px;
    }

    .pesan-icon i {
        font-size: 1.5rem !important;
    }
}
</style>
<?php endif; ?>

<!-- Script untuk menampilkan SweetAlert ketika absen berhasil -->
<?php if (isset($_GET['absen_status']) && $_GET['absen_status'] == 'success'): ?>
<script>
    // Fungsi untuk memastikan SweetAlert2 dimuat
    function ensureSweetAlertLoaded(callback, maxAttempts = 10, interval = 300) {
        let attempts = 0;

        function checkSwal() {
            attempts++;
            if (typeof Swal !== 'undefined') {
                callback();
            } else if (attempts < maxAttempts) {
                console.log('SweetAlert2 belum dimuat, mencoba lagi... (percobaan ' + attempts + ')');
                setTimeout(checkSwal, interval);
            } else {
                console.error('SweetAlert2 tidak dapat dimuat setelah ' + maxAttempts + ' percobaan');
                // Fallback ke alert biasa
                var absenType = '<?php echo isset($_GET['type']) ? $_GET['type'] : 'masuk'; ?>';
                alert('Absensi ' + (absenType === 'masuk' ? 'Masuk' : 'Pulang') + ' Berhasil\n\nData absensi ' +
                      (absenType === 'masuk' ? 'masuk' : 'pulang') + ' Anda telah berhasil disimpan.');
            }
        }

        checkSwal();
    }

    // Jalankan ketika dokumen dimuat
    document.addEventListener('DOMContentLoaded', function() {
        ensureSweetAlertLoaded(function() {
            // Tentukan tipe absen (masuk atau pulang)
            var absenType = '<?php echo isset($_GET['type']) ? $_GET['type'] : 'masuk'; ?>';

            // Gunakan pesan dari parameter URL jika ada
            var message = '<?php echo isset($_GET['message']) ? htmlspecialchars(urldecode($_GET['message'])) : ''; ?>';
            var title = 'Absensi ' + (absenType === 'masuk' ? 'Masuk' : 'Pulang') + ' Berhasil';
            var text = message ? message : 'Data absensi ' + (absenType === 'masuk' ? 'masuk' : 'pulang') + ' Anda telah berhasil disimpan.';

            // Tampilkan SweetAlert
            Swal.fire({
                icon: 'success',
                title: title,
                text: text,
                timer: 3000,
                timerProgressBar: true,
                showConfirmButton: false
            });
        });
    });
</script>
<?php endif; ?>

<!-- Script untuk menampilkan SweetAlert ketika scan rapat berhasil -->
<?php if (isset($_GET['rapat_status']) && $_GET['rapat_status'] == 'success'): ?>
<script>
    // Jalankan ketika dokumen dimuat
    document.addEventListener('DOMContentLoaded', function() {
        // Fungsi untuk memastikan SweetAlert2 dimuat
        function ensureSweetAlertLoaded(callback, maxAttempts = 10, interval = 300) {
            let attempts = 0;

            function checkSwal() {
                attempts++;
                if (typeof Swal !== 'undefined') {
                    callback();
                } else if (attempts < maxAttempts) {
                    console.log('SweetAlert2 belum dimuat, mencoba lagi... (percobaan ' + attempts + ')');
                    setTimeout(checkSwal, interval);
                } else {
                    console.error('SweetAlert2 tidak dapat dimuat setelah ' + maxAttempts + ' percobaan');
                    // Fallback ke alert biasa
                    alert('Absensi Rapat Berhasil\n\nKehadiran Anda dalam rapat telah berhasil dicatat.');
                }
            }

            checkSwal();
        }

        ensureSweetAlertLoaded(function() {
            // Cek apakah ada pesan di sessionStorage
            var rapatSuccess = sessionStorage.getItem('rapatSuccess');
            var rapatMessage = sessionStorage.getItem('rapatMessage');

            if (rapatSuccess) {
                // Tampilkan SweetAlert
                Swal.fire({
                    icon: 'success',
                    title: 'Absensi Rapat Berhasil',
                    html: rapatMessage || 'Kehadiran Anda dalam rapat telah berhasil dicatat.',
                    timer: 3000,
                    timerProgressBar: true,
                    showConfirmButton: false
                });

                // Hapus data dari sessionStorage
                sessionStorage.removeItem('rapatSuccess');
                sessionStorage.removeItem('rapatMessage');
            }
        });
    });
</script>
<?php endif; ?>

<!-- Script untuk menampilkan SweetAlert ketika karyawan memiliki jadwal rapat hari ini yang belum dihadiri -->
<?php
// Hitung jumlah rapat yang belum dihadiri
$belum_hadir_count = 0;
if (!empty($rapat_list_today)) {
    foreach ($rapat_list_today as $rapat) {
        if ($rapat['status'] != 'hadir') {
            $belum_hadir_count++;
        }
    }
}

// Tampilkan popup hanya jika ada rapat yang belum dihadiri
if ($belum_hadir_count > 0):
?>
<script>
    // Jalankan ketika dokumen dimuat
    document.addEventListener('DOMContentLoaded', function() {
        // Fungsi untuk memastikan SweetAlert2 dimuat
        function ensureSweetAlertLoaded(callback, maxAttempts = 10, interval = 300) {
            let attempts = 0;

            function checkSwal() {
                attempts++;
                if (typeof Swal !== 'undefined') {
                    callback();
                } else if (attempts < maxAttempts) {
                    console.log('SweetAlert2 belum dimuat, mencoba lagi... (percobaan ' + attempts + ')');
                    setTimeout(checkSwal, interval);
                } else {
                    console.error('SweetAlert2 tidak dapat dimuat setelah ' + maxAttempts + ' percobaan');
                    // Fallback ke alert biasa
                    alert('Anda memiliki jadwal rapat hari ini yang belum dihadiri. Silakan cek jadwal rapat Anda.');
                }
            }

            checkSwal();
        }

        ensureSweetAlertLoaded(function() {
            // Buat daftar rapat yang belum dihadiri dalam format HTML
            var rapatListHtml = '<div class="text-left"><ul class="list-group">';
            <?php foreach ($rapat_list_today as $rapat): ?>
                <?php if ($rapat['status'] != 'hadir'): ?>
                rapatListHtml += '<li class="list-group-item">' +
                    '<strong><?php echo htmlspecialchars($rapat['judul']); ?></strong><br>' +
                    '<i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($rapat['lokasi']); ?><br>' +
                    '<i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($rapat['waktu_mulai'])) . ' - ' . date('H:i', strtotime($rapat['waktu_selesai'])); ?><br>' +
                    '<span class="badge bg-warning text-dark">Belum Hadir</span>' +
                    '</li>';
                <?php endif; ?>
            <?php endforeach; ?>
            rapatListHtml += '</ul></div>';

            // Tampilkan SweetAlert
            Swal.fire({
                icon: 'info',
                title: 'Jadwal Rapat Hari Ini',
                html: rapatListHtml,
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'Saya Mengerti'
            });
        });
    });
</script>
<?php endif; ?>

<?php if ($izin_dinas_aktif): ?>
<script>
    function showDinasAlert() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'info',
                title: 'Perjalanan Dinas Aktif',
                text: 'Anda sedang dalam perjalanan dinas ke <?php echo $izin_dinas_aktif['tujuan']; ?>. Presensi Anda akan diisi otomatis selama periode perjalanan dinas.',
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'Saya Mengerti'
            });
        } else {
            alert('Anda sedang dalam perjalanan dinas. Presensi diisi otomatis.');
        }
    }
</script>
<?php endif; ?>

<?php if ($hari_libur): ?>
<script>
    function showHolidayAlert() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'warning',
                title: 'Hari Libur',
                text: 'Hari ini adalah hari libur: <?php echo $hari_libur['nama_libur']; ?>. Tidak perlu melakukan absensi pada hari libur.',
                confirmButtonColor: '#e74a3b',
                confirmButtonText: 'Saya Mengerti'
            });
        } else {
            alert('Hari ini adalah hari libur: <?php echo $hari_libur['nama_libur']; ?>. Tidak perlu melakukan absensi.');
        }
    }
</script>
<?php endif; ?>

<!-- Script untuk menampilkan pesan ketika tidak memiliki izin absensi -->
<script>
    function showNoPermissionAlert() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'warning',
                title: 'Tidak Ada Izin Absensi',
                text: 'Anda tidak memiliki izin untuk melakukan absensi. Silakan hubungi administrator untuk mendapatkan izin absensi.',
                confirmButtonColor: '#6c757d',
                confirmButtonText: 'Saya Mengerti'
            });
        } else {
            alert('Anda tidak memiliki izin untuk melakukan absensi. Silakan hubungi administrator.');
        }
    }

    function showNoRapatAlert() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'info',
                title: 'Tidak Ada Jadwal Rapat',
                text: 'Anda tidak memiliki jadwal rapat hari ini atau semua rapat Anda hari ini sudah dihadiri.',
                confirmButtonColor: '#6c757d',
                confirmButtonText: 'Saya Mengerti'
            });
        } else {
            alert('Anda tidak memiliki jadwal rapat hari ini atau semua rapat Anda hari ini sudah dihadiri.');
        }
    }

    function showScheduleSelectionModal() {
        var modal = new bootstrap.Modal(document.getElementById('scheduleSelectionModal'));
        modal.show();
    }

    // Toggle Menu Lainnya
    document.addEventListener('DOMContentLoaded', function() {
        const toggleButton = document.getElementById('toggleAdditionalMenu');
        const additionalMenu = document.getElementById('additionalMenu');
        const toggleIcon = document.getElementById('toggleIcon');
        const menuText = document.getElementById('menuLainnyaText');

        // Tambahkan CSS untuk class show jika belum ada
        if (!document.querySelector('style[data-toggle-menu]')) {
            const style = document.createElement('style');
            style.setAttribute('data-toggle-menu', 'true');
            style.textContent = `
                .additional-menu.show {
                    max-height: 500px !important;
                    opacity: 1 !important;
                }
                .toggle-arrow-button.active i {
                    transform: rotate(180deg) !important;
                }
            `;
            document.head.appendChild(style);
        }

        if (toggleButton && additionalMenu && toggleIcon) {
            // Event listener untuk tombol toggle
            toggleButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const isMenuVisible = additionalMenu.classList.contains('show');

                if (isMenuVisible) {
                    // Sembunyikan menu
                    additionalMenu.classList.remove('show');
                    toggleIcon.classList.remove('fa-chevron-up');
                    toggleIcon.classList.add('fa-chevron-down');
                    toggleButton.classList.remove('active');
                    if (menuText) {
                        menuText.textContent = 'Menu Lainnya';
                    }
                } else {
                    // Tampilkan menu
                    additionalMenu.classList.add('show');
                    toggleIcon.classList.remove('fa-chevron-down');
                    toggleIcon.classList.add('fa-chevron-up');
                    toggleButton.classList.add('active');
                    if (menuText) {
                        menuText.textContent = 'Tutup Menu';
                    }
                }
            });

            // Event listener untuk span text juga
            if (menuText) {
                menuText.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleButton.click();
                });

                // Tambahkan style cursor pointer
                menuText.style.cursor = 'pointer';
            }
        }
    });
</script>

</div>
</body>
</html>
<?php
// Include footer
include_once '../includes/footer.php';
?>