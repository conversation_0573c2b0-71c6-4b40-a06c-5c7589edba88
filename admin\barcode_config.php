<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek apakah tabel barcode_config sudah ada
$query = "SHOW TABLES LIKE 'barcode_config'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('warning', 'Tabel barcode_config belum ada. Silakan klik tombol "Buat Tabel" untuk membuatnya.');
}

// Proses tambah konfigurasi barcode
if (isset($_POST['tambah'])) {
    $lokasi_id = clean($_POST['lokasi_id']);
    $barcode_value = clean($_POST['barcode_value']);
    $radius = clean($_POST['radius']);
    
    // Validasi input
    if (empty($lokasi_id) || empty($barcode_value) || empty($radius)) {
        setMessage('danger', 'Semua field harus diisi!');
        redirect('admin/barcode_config.php');
    }
    
    // Cek apakah lokasi sudah memiliki barcode
    $query = "SELECT * FROM barcode_config WHERE lokasi_id = '$lokasi_id'";
    $result = mysqli_query($conn, $query);
    
    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Lokasi ini sudah memiliki konfigurasi barcode!');
        redirect('admin/barcode_config.php');
    }
    
    // Insert data barcode baru
    $query = "INSERT INTO barcode_config (lokasi_id, barcode_value, radius) VALUES ('$lokasi_id', '$barcode_value', '$radius')";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Konfigurasi barcode berhasil ditambahkan!');
        $_SESSION['show_sweet_alert'] = true;
    } else {
        setMessage('danger', 'Gagal menambahkan konfigurasi barcode: ' . mysqli_error($conn));
    }
    
    redirect('admin/barcode_config.php');
}

// Proses edit konfigurasi barcode
if (isset($_POST['edit'])) {
    $id = clean($_POST['id']);
    $lokasi_id = clean($_POST['lokasi_id']);
    $barcode_value = clean($_POST['barcode_value']);
    $radius = clean($_POST['radius']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Validasi input
    if (empty($id) || empty($lokasi_id) || empty($barcode_value) || empty($radius)) {
        setMessage('danger', 'Semua field harus diisi!');
        redirect('admin/barcode_config.php');
    }
    
    // Cek apakah lokasi sudah memiliki barcode (selain yang sedang diedit)
    $query = "SELECT * FROM barcode_config WHERE lokasi_id = '$lokasi_id' AND id != '$id'";
    $result = mysqli_query($conn, $query);
    
    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Lokasi ini sudah memiliki konfigurasi barcode!');
        redirect('admin/barcode_config.php');
    }
    
    // Update data barcode
    $query = "UPDATE barcode_config SET lokasi_id = '$lokasi_id', barcode_value = '$barcode_value', radius = '$radius', is_active = '$is_active' WHERE id = '$id'";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Konfigurasi barcode berhasil diperbarui!');
        $_SESSION['show_sweet_alert'] = true;
    } else {
        setMessage('danger', 'Gagal memperbarui konfigurasi barcode: ' . mysqli_error($conn));
    }
    
    redirect('admin/barcode_config.php');
}

// Proses hapus konfigurasi barcode
if (isset($_GET['hapus'])) {
    $id = clean($_GET['hapus']);
    
    // Hapus data barcode
    $query = "DELETE FROM barcode_config WHERE id = '$id'";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Konfigurasi barcode berhasil dihapus!');
        $_SESSION['show_sweet_alert'] = true;
    } else {
        setMessage('danger', 'Gagal menghapus konfigurasi barcode: ' . mysqli_error($conn));
    }
    
    redirect('admin/barcode_config.php');
}

// Ambil data lokasi untuk dropdown
$query = "SELECT * FROM lokasi ORDER BY nama_lokasi ASC";
$result = mysqli_query($conn, $query);

$lokasi_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $lokasi_list[] = $row;
    }
}

// Ambil data barcode_config
$query = "SELECT bc.*, l.nama_lokasi 
          FROM barcode_config bc 
          JOIN lokasi l ON bc.lokasi_id = l.id 
          ORDER BY l.nama_lokasi ASC";
$result = mysqli_query($conn, $query);

$barcode_configs = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $barcode_configs[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<style>
/* CSS untuk form edit inline */
.bg-light {
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Memperbaiki tampilan form pada layar kecil */
@media (max-width: 768px) {
    .row > div {
        margin-bottom: 15px;
    }
}

/* Memperbaiki tampilan tabel */
.table td {
    vertical-align: middle;
}

/* Animasi transisi untuk form edit */
tr {
    transition: all 0.3s ease;
}

/* Highlight untuk baris yang sedang diedit */
.editing-row {
    background-color: #e8f4ff !important;
}

/* Styling untuk barcode */
.barcode-container {
    padding: 10px;
    background: white;
    display: inline-block;
    margin-bottom: 10px;
}
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Konfigurasi Barcode</h1>
        <div>
            <?php if (mysqli_num_rows(mysqli_query($conn, "SHOW TABLES LIKE 'barcode_config'")) == 0): ?>
            <a href="create_barcode_table.php" class="btn btn-warning me-2">
                <i class="fas fa-database"></i> Buat Tabel
            </a>
            <?php endif; ?>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#tambahBarcodeModal">
                <i class="fas fa-plus"></i> Tambah Barcode
            </button>
        </div>
    </div>
    
    <div class="alert alert-info mb-4">
        <h5 class="alert-heading">Informasi Absensi Barcode:</h5>
        <p>Fitur ini memungkinkan karyawan untuk melakukan absensi dengan memindai barcode yang telah dikonfigurasi untuk lokasi tertentu.</p>
        <p>Karyawan harus berada dalam radius yang ditentukan dari lokasi barcode untuk dapat melakukan absensi.</p>
    </div>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Data Konfigurasi Barcode</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Lokasi</th>
                            <th>Barcode</th>
                            <th>Radius (meter)</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($barcode_configs)): ?>
                            <tr>
                                <td colspan="5" class="text-center">Tidak ada data konfigurasi barcode</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($barcode_configs as $bc): ?>
                                <!-- Baris tampilan data -->
                                <tr id="view_<?php echo $bc['id']; ?>">
                                    <td><?php echo $bc['nama_lokasi']; ?></td>
                                    <td>
                                        <div class="barcode-container">
                                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=<?php echo urlencode($bc['barcode_value']); ?>" alt="Barcode" class="img-fluid" style="max-width: 100px;">
                                        </div>
                                        <div>
                                            <small class="text-muted"><?php echo $bc['barcode_value']; ?></small>
                                        </div>
                                    </td>
                                    <td><?php echo $bc['radius']; ?> meter</td>
                                    <td>
                                        <?php if ($bc['is_active']): ?>
                                            <span class="badge bg-success">Aktif</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Tidak Aktif</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="toggleEdit(<?php echo $bc['id']; ?>)">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <a href="barcode_config.php?hapus=<?php echo $bc['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus konfigurasi barcode ini?')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </a>
                                        <a href="print_barcode.php?id=<?php echo $bc['id']; ?>" class="btn btn-sm btn-success" target="_blank">
                                            <i class="fas fa-print"></i> Cetak
                                        </a>
                                    </td>
                                </tr>
                                
                                <!-- Baris form edit -->
                                <tr id="edit_<?php echo $bc['id']; ?>" style="display: none;">
                                    <td colspan="5">
                                        <form method="post" action="" class="p-3 bg-light rounded">
                                            <input type="hidden" name="id" value="<?php echo $bc['id']; ?>">
                                            
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="lokasi_id_<?php echo $bc['id']; ?>" class="form-label">Lokasi</label>
                                                    <select class="form-select" id="lokasi_id_<?php echo $bc['id']; ?>" name="lokasi_id" required>
                                                        <?php foreach ($lokasi_list as $l): ?>
                                                            <option value="<?php echo $l['id']; ?>" <?php echo ($bc['lokasi_id'] == $l['id']) ? 'selected' : ''; ?>>
                                                                <?php echo $l['nama_lokasi']; ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="barcode_value_<?php echo $bc['id']; ?>" class="form-label">Nilai Barcode</label>
                                                    <input type="text" class="form-control" id="barcode_value_<?php echo $bc['id']; ?>" name="barcode_value" value="<?php echo $bc['barcode_value']; ?>" required>
                                                    <small class="text-muted">Nilai unik untuk barcode ini</small>
                                                </div>
                                            </div>
                                            
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="radius_<?php echo $bc['id']; ?>" class="form-label">Radius (meter)</label>
                                                    <input type="number" class="form-control" id="radius_<?php echo $bc['id']; ?>" name="radius" value="<?php echo $bc['radius']; ?>" min="1" required>
                                                    <small class="text-muted">Jarak maksimal karyawan dari lokasi barcode</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Status</label>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="is_active_<?php echo $bc['id']; ?>" name="is_active" <?php echo $bc['is_active'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="is_active_<?php echo $bc['id']; ?>">Aktif</label>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="text-end">
                                                <button type="button" class="btn btn-secondary" onclick="toggleEdit(<?php echo $bc['id']; ?>)">
                                                    <i class="fas fa-times"></i> Batal
                                                </button>
                                                <button type="submit" name="edit" class="btn btn-primary">
                                                    <i class="fas fa-save"></i> Simpan
                                                </button>
                                            </div>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Barcode -->
<div class="modal fade" id="tambahBarcodeModal" tabindex="-1" aria-labelledby="tambahBarcodeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tambahBarcodeModalLabel">Tambah Konfigurasi Barcode</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="lokasi_id" class="form-label">Lokasi</label>
                        <select class="form-select" id="lokasi_id" name="lokasi_id" required>
                            <option value="">Pilih Lokasi</option>
                            <?php foreach ($lokasi_list as $l): ?>
                                <option value="<?php echo $l['id']; ?>"><?php echo $l['nama_lokasi']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="barcode_value" class="form-label">Nilai Barcode</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="barcode_value" name="barcode_value" required>
                            <button class="btn btn-outline-secondary" type="button" id="generateBarcode">
                                <i class="fas fa-random"></i> Generate
                            </button>
                        </div>
                        <small class="text-muted">Nilai unik untuk barcode ini</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="radius" class="form-label">Radius (meter)</label>
                        <input type="number" class="form-control" id="radius" name="radius" value="100" min="1" required>
                        <small class="text-muted">Jarak maksimal karyawan dari lokasi barcode</small>
                    </div>
                    
                    <div id="barcodePreview" class="text-center mt-3" style="display: none;">
                        <div class="barcode-container">
                            <img id="barcodeImage" src="" alt="Barcode Preview" class="img-fluid">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="tambah" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Fungsi untuk menampilkan/menyembunyikan form edit
function toggleEdit(id) {
    // Tampilkan/sembunyikan baris tampilan data
    const viewRow = document.getElementById('view_' + id);
    const editRow = document.getElementById('edit_' + id);
    
    // Sembunyikan semua form edit yang terbuka
    const allEditRows = document.querySelectorAll('[id^="edit_"]');
    const allViewRows = document.querySelectorAll('[id^="view_"]');
    
    allEditRows.forEach(row => {
        if (row.id !== 'edit_' + id) {
            row.style.display = 'none';
        }
    });
    
    allViewRows.forEach(row => {
        if (row.id !== 'view_' + id) {
            row.style.display = '';
            row.classList.remove('editing-row');
        }
    });
    
    // Toggle tampilan baris yang diklik
    if (editRow.style.display === 'none') {
        viewRow.classList.add('editing-row');
        editRow.style.display = '';
    } else {
        viewRow.classList.remove('editing-row');
        viewRow.style.display = '';
        editRow.style.display = 'none';
    }
}

// Generate random barcode value
document.getElementById('generateBarcode').addEventListener('click', function() {
    const randomValue = 'BARCODE_' + Math.random().toString(36).substring(2, 10).toUpperCase();
    document.getElementById('barcode_value').value = randomValue;
    
    // Update barcode preview
    updateBarcodePreview(randomValue);
});

// Update barcode preview when value changes
document.getElementById('barcode_value').addEventListener('input', function() {
    updateBarcodePreview(this.value);
});

// Function to update barcode preview
function updateBarcodePreview(value) {
    if (value.trim() !== '') {
        const barcodeImage = document.getElementById('barcodeImage');
        barcodeImage.src = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' + encodeURIComponent(value);
        document.getElementById('barcodePreview').style.display = 'block';
    } else {
        document.getElementById('barcodePreview').style.display = 'none';
    }
}

// Tampilkan SweetAlert jika ada pesan sukses
document.addEventListener('DOMContentLoaded', function() {
    <?php if (isset($_SESSION['message']) && isset($_SESSION['show_sweet_alert']) && $_SESSION['show_sweet_alert']): ?>
        Swal.fire({
            icon: '<?php echo ($_SESSION['message']['type'] == 'success') ? 'success' : 'error'; ?>',
            title: '<?php echo ($_SESSION['message']['type'] == 'success') ? 'Berhasil!' : 'Gagal!'; ?>',
            text: '<?php echo $_SESSION['message']['text']; ?>',
            timer: 2000,
            showConfirmButton: false
        });
        <?php unset($_SESSION['show_sweet_alert']); ?>
    <?php endif; ?>
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
