<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Inisialisasi variabel
$tahun = isset($_POST['tahun']) ? clean($_POST['tahun']) : date('Y');
$success_count = 0;
$error_count = 0;
$duplicate_count = 0;
$skipped_count = 0;
$messages = [];

// Fungsi untuk mengambil data dari API
function fetchHolidaysFromAPI($tahun) {
    $url = "https://api-harilibur.vercel.app/api";

    // Inisialisasi cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // Eksekusi cURL
    $response = curl_exec($ch);

    // Cek error
    if (curl_errno($ch)) {
        return [
            'success' => false,
            'message' => 'Error: ' . curl_error($ch)
        ];
    }

    // Tutup cURL
    curl_close($ch);

    // Decode JSON
    $data = json_decode($response, true);

    // Cek apakah data valid
    if (!$data || !is_array($data)) {
        return [
            'success' => false,
            'message' => 'Format data tidak valid atau tidak ada data libur yang tersedia'
        ];
    }

    // Filter data berdasarkan tahun yang dipilih
    $filtered_data = [];
    foreach ($data as $holiday) {
        if (isset($holiday['holiday_date']) && strpos($holiday['holiday_date'], $tahun) === 0) {
            $filtered_data[] = $holiday;
        }
    }

    if (empty($filtered_data)) {
        return [
            'success' => false,
            'message' => "Tidak ada data hari libur untuk tahun {$tahun}"
        ];
    }

    return [
        'success' => true,
        'data' => $filtered_data
    ];
}

// Proses sinkronisasi hari libur
if (isset($_POST['sync'])) {
    // Ambil data dari API
    $result = fetchHolidaysFromAPI($tahun);
    $include_non_national = isset($_POST['include_non_national']);

    if ($result['success']) {
        $holidays = $result['data'];

        // Loop melalui data hari libur
        foreach ($holidays as $holiday) {
            // Format tanggal dari API (YYYY-MM-DD)
            $tanggal = $holiday['holiday_date'];
            $nama_libur = $holiday['holiday_name'];
            $is_national = $holiday['is_national_holiday'];

            // Skip hari libur yang bukan nasional jika tidak diinginkan
            if (!$is_national && !$include_non_national) {
                $skipped_count++;
                continue;
            }

            // Cek apakah tanggal sudah terdaftar
            $query = "SELECT * FROM hari_libur WHERE tanggal = '$tanggal'";
            $check_result = mysqli_query($conn, $query);

            if (mysqli_num_rows($check_result) > 0) {
                // Tanggal sudah terdaftar, skip
                $duplicate_count++;
                continue;
            }

            // Insert data hari libur baru
            $query = "INSERT INTO hari_libur (nama_libur, tanggal) VALUES ('$nama_libur', '$tanggal')";

            if (mysqli_query($conn, $query)) {
                $success_count++;
            } else {
                $error_count++;
                $messages[] = "Gagal menambahkan hari libur: {$nama_libur} ({$tanggal})";
            }
        }

        // Set pesan berhasil
        $holiday_type = $include_non_national ? "hari libur" : "hari libur nasional";

        if ($success_count > 0) {
            setMessage('success', "Berhasil menambahkan {$success_count} {$holiday_type} baru. {$duplicate_count} hari libur sudah ada.");
        } else if ($duplicate_count > 0) {
            setMessage('info', "Semua {$holiday_type} untuk tahun {$tahun} sudah terdaftar.");
        } else {
            setMessage('warning', "Tidak ada {$holiday_type} yang ditambahkan untuk tahun {$tahun}.");
        }

        // Tambahkan pesan tentang hari libur yang dilewati
        if ($skipped_count > 0 && !$include_non_national) {
            setMessage('info', "{$skipped_count} hari libur non-nasional dilewati. Centang opsi 'Sertakan hari libur non-nasional' untuk menyertakannya.");
        }

        // Tambahkan pesan error jika ada
        if ($error_count > 0) {
            setMessage('danger', "Gagal menambahkan {$error_count} hari libur. Lihat log untuk detail.");
        }
    } else {
        // Set pesan error
        setMessage('danger', "Gagal mengambil data dari API: " . $result['message']);
    }

    // Redirect kembali ke halaman hari libur
    redirect('admin/hari_libur.php');
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Sinkronisasi Hari Libur Nasional</h1>
        <a href="hari_libur.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Sinkronisasi dari API Hari Libur Nasional</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5 class="alert-heading">Informasi:</h5>
                <p>Fitur ini akan mengambil data hari libur nasional dari API <a href="https://api-harilibur.vercel.app/api" target="_blank">api-harilibur.vercel.app</a> dan menambahkannya ke database.</p>
                <p>Hanya hari libur nasional yang akan ditambahkan (hari libur lokal/adat akan dilewati).</p>
                <p>Hari libur yang sudah ada di database tidak akan ditambahkan lagi (tidak duplikat).</p>
                <hr>
                <p class="mb-0">Pilih tahun dan klik tombol "Sinkronisasi" untuk memulai.</p>
            </div>

            <form method="post" action="">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="tahun" class="form-label">Tahun</label>
                        <select class="form-select" id="tahun" name="tahun">
                            <?php for ($i = date('Y') - 2; $i <= date('Y') + 5; $i++): ?>
                                <option value="<?php echo $i; ?>" <?php echo ($i == $tahun) ? 'selected' : ''; ?>>
                                    <?php echo $i; ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="include_non_national" name="include_non_national">
                            <label class="form-check-label" for="include_non_national">
                                Sertakan hari libur non-nasional (hari raya adat/lokal)
                            </label>
                        </div>
                    </div>
                </div>

                <button type="submit" name="sync" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> Sinkronisasi
                </button>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
