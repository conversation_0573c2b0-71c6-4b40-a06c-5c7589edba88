<?php
/**
 * API untuk menerima data absensi offline
 * Endpoint ini menerima data absensi offline dari aplikasi Android dan menyimpannya ke database
 * 
 * Format data:
 * {
 *     "api_key": "absensiku_api_key_2023",
 *     "nik": "123456789",
 *     "nama": "<PERSON><PERSON>",
 *     "tanggal": "2023-08-15",
 *     "jam": "08:30:00",
 *     "jenis_absen": "masuk", // atau "keluar"
 *     "foto": "base64_encoded_image"
 * }
 * 
 * Atau format batch:
 * {
 *     "api_key": "absensiku_api_key_2023",
 *     "data": [
 *         {
 *             "nik": "123456789",
 *             "nama": "<PERSON><PERSON>",
 *             "tanggal": "2023-08-15",
 *             "jam": "08:30:00",
 *             "jenis_absen": "masuk",
 *             "foto": "base64_encoded_image"
 *         },
 *         {
 *             "nik": "987654321",
 *             "nama": "<PERSON><PERSON>",
 *             "tanggal": "2023-08-15",
 *             "jam": "17:00:00",
 *             "jenis_absen": "keluar",
 *             "foto": "base64_encoded_image"
 *         }
 *     ]
 * }
 */

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set header untuk JSON
header('Content-Type: application/json');

// Fungsi untuk validasi API key
function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Fungsi untuk mendapatkan user_id berdasarkan NIK
function getUserIdByNik($nik) {
    global $conn;
    $nik = mysqli_real_escape_string($conn, $nik);
    $query = "SELECT id FROM users WHERE nik = '$nik' AND role = 'karyawan'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result)['id'];
    }
    return false;
}

// Fungsi untuk menyimpan foto
function saveFoto($foto, $nik, $tanggal, $jenis_absen) {
    // Decode base64
    $foto_data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $foto));
    
    // Buat nama file
    $foto_name = $nik . '_' . str_replace('-', '', $tanggal) . '_' . $jenis_absen . '_' . time() . '.jpg';
    
    // Buat direktori jika belum ada
    $upload_dir = '../uploads/absensi_offline/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    // Simpan file
    $foto_path = $upload_dir . $foto_name;
    file_put_contents($foto_path, $foto_data);
    
    // Return path relatif untuk disimpan di database
    return 'uploads/absensi_offline/' . $foto_name;
}

// Fungsi untuk menyimpan data absensi offline
function saveAbsensiOffline($data) {
    global $conn;
    
    // Validasi data
    if (!isset($data['nik']) || !isset($data['nama']) || !isset($data['tanggal']) || 
        !isset($data['jam']) || !isset($data['jenis_absen']) || !isset($data['foto'])) {
        return [
            'status' => 'error',
            'message' => 'Data tidak lengkap'
        ];
    }
    
    // Validasi jenis absen
    if ($data['jenis_absen'] != 'masuk' && $data['jenis_absen'] != 'keluar') {
        return [
            'status' => 'error',
            'message' => 'Jenis absen tidak valid'
        ];
    }
    
    // Dapatkan user_id berdasarkan NIK
    $user_id = getUserIdByNik($data['nik']);
    if (!$user_id) {
        return [
            'status' => 'error',
            'message' => 'NIK tidak valid'
        ];
    }
    
    // Simpan foto
    $foto_path = saveFoto($data['foto'], $data['nik'], $data['tanggal'], $data['jenis_absen']);
    
    // Escape data
    $user_id = mysqli_real_escape_string($conn, $user_id);
    $nik = mysqli_real_escape_string($conn, $data['nik']);
    $nama = mysqli_real_escape_string($conn, $data['nama']);
    $tanggal = mysqli_real_escape_string($conn, $data['tanggal']);
    $jam = mysqli_real_escape_string($conn, $data['jam']);
    $jenis_absen = mysqli_real_escape_string($conn, $data['jenis_absen']);
    $foto = mysqli_real_escape_string($conn, $foto_path);
    
    // Query untuk menyimpan data
    $query = "INSERT INTO absensi_offline (user_id, nik, nama, tanggal, jam, jenis_absen, foto, status) 
              VALUES ('$user_id', '$nik', '$nama', '$tanggal', '$jam', '$jenis_absen', '$foto', 'pending')";
    
    // Eksekusi query
    if (mysqli_query($conn, $query)) {
        return [
            'status' => 'success',
            'message' => 'Data absensi offline berhasil disimpan'
        ];
    } else {
        return [
            'status' => 'error',
            'message' => 'Gagal menyimpan data absensi offline: ' . mysqli_error($conn)
        ];
    }
}

// Ambil data dari request
$data = json_decode(file_get_contents('php://input'), true);

// Jika data tidak valid, coba ambil dari $_POST
if (!$data) {
    $data = $_POST;
}

// Validasi API key
if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'API key tidak valid'
    ]);
    exit;
}

// Cek apakah data adalah batch atau single
if (isset($data['data']) && is_array($data['data'])) {
    // Batch data
    $results = [];
    $success_count = 0;
    $error_count = 0;
    
    foreach ($data['data'] as $index => $item) {
        $result = saveAbsensiOffline($item);
        $results[] = [
            'index' => $index,
            'status' => $result['status'],
            'message' => $result['message']
        ];
        
        if ($result['status'] === 'success') {
            $success_count++;
        } else {
            $error_count++;
        }
    }
    
    echo json_encode([
        'status' => $error_count === 0 ? 'success' : ($success_count === 0 ? 'error' : 'partial'),
        'message' => "Berhasil menyimpan $success_count data, gagal menyimpan $error_count data",
        'results' => $results
    ]);
} else {
    // Single data
    $result = saveAbsensiOffline($data);
    echo json_encode($result);
}
?>
