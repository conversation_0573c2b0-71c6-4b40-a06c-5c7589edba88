<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek apakah tabel rapat sudah ada
$query = "SHOW TABLES LIKE 'rapat'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('warning', 'Tabel rapat belum ada. Silakan klik tombol "Buat Tabel" untuk membuatnya.');
}

// Proses hapus rapat
if (isset($_GET['hapus'])) {
    $id = clean($_GET['hapus']);
    
    // Hapus data rapat
    $query = "DELETE FROM rapat WHERE id = '$id'";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Data rapat berhasil dihapus!');
        $_SESSION['show_sweet_alert'] = true;
    } else {
        setMessage('danger', 'Gagal menghapus data rapat: ' . mysqli_error($conn));
    }
    
    redirect('admin/rapat.php');
}

// Ambil data rapat
$query = "SELECT r.*, u.nama as created_by_name, 
          (SELECT COUNT(*) FROM rapat_peserta WHERE rapat_id = r.id) as jumlah_peserta,
          (SELECT COUNT(*) FROM rapat_peserta WHERE rapat_id = r.id AND status = 'hadir') as jumlah_hadir
          FROM rapat r 
          JOIN users u ON r.created_by = u.id 
          ORDER BY r.tanggal DESC, r.waktu_mulai DESC";
$result = mysqli_query($conn, $query);

$rapat_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $rapat_list[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Manajemen Rapat</h1>
        <div>
            <?php if (mysqli_num_rows(mysqli_query($conn, "SHOW TABLES LIKE 'rapat'")) == 0): ?>
            <a href="create_rapat_table.php" class="btn btn-warning me-2">
                <i class="fas fa-database"></i> Buat Tabel
            </a>
            <?php endif; ?>
            <a href="tambah_rapat.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Rapat
            </a>
        </div>
    </div>
    
    <?php if (isset($_SESSION['message'])): ?>
    <div class="alert alert-<?php echo $_SESSION['message']['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['message']['text']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php unset($_SESSION['message']); endif; ?>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Daftar Rapat</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Judul</th>
                            <th>Tanggal</th>
                            <th>Waktu</th>
                            <th>Lokasi</th>
                            <th>Peserta</th>
                            <th>Kehadiran</th>
                            <th>Dibuat Oleh</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($rapat_list)): ?>
                            <tr>
                                <td colspan="8" class="text-center">Tidak ada data rapat</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($rapat_list as $rapat): ?>
                                <tr>
                                    <td><?php echo $rapat['judul']; ?></td>
                                    <td><?php echo date('d/m/Y', strtotime($rapat['tanggal'])); ?></td>
                                    <td><?php echo date('H:i', strtotime($rapat['waktu_mulai'])) . ' - ' . date('H:i', strtotime($rapat['waktu_selesai'])); ?></td>
                                    <td><?php echo $rapat['lokasi']; ?></td>
                                    <td><?php echo $rapat['jumlah_peserta']; ?> orang</td>
                                    <td>
                                        <?php if ($rapat['jumlah_peserta'] > 0): ?>
                                            <?php echo $rapat['jumlah_hadir']; ?> / <?php echo $rapat['jumlah_peserta']; ?>
                                            (<?php echo round(($rapat['jumlah_hadir'] / $rapat['jumlah_peserta']) * 100); ?>%)
                                        <?php else: ?>
                                            0 / 0 (0%)
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $rapat['created_by_name']; ?></td>
                                    <td>
                                        <a href="detail_rapat.php?id=<?php echo $rapat['id']; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> Detail
                                        </a>
                                        <a href="edit_rapat.php?id=<?php echo $rapat['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="print_barcode_rapat.php?id=<?php echo $rapat['id']; ?>" class="btn btn-sm btn-success" target="_blank">
                                            <i class="fas fa-print"></i> Barcode
                                        </a>
                                        <a href="rapat.php?hapus=<?php echo $rapat['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus rapat ini?')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
