<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses admin
checkAccess('admin');

// Proses form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $user_id = (int)$_POST['user_id'];
    $judul = mysqli_real_escape_string($conn, $_POST['judul']);
    $pesan = mysqli_real_escape_string($conn, $_POST['pesan']);
    $jenis_pelanggaran = mysqli_real_escape_string($conn, $_POST['jenis_pelanggaran']);
    $tingkat_peringatan = mysqli_real_escape_string($conn, $_POST['tingkat_peringatan']);
    $keterangan = mysqli_real_escape_string($conn, $_POST['keterangan']);
    $dibuat_oleh = $_SESSION['user_id'];

    // Validasi input
    $errors = [];
    if (empty($user_id)) $errors[] = "Pilih karyawan yang akan diberi peringatan.";
    if (empty($judul)) $errors[] = "Judul pesan harus diisi.";
    if (empty($pesan)) $errors[] = "Isi pesan harus diisi.";
    if (empty($jenis_pelanggaran)) $errors[] = "Jenis pelanggaran harus dipilih.";
    if (empty($tingkat_peringatan)) $errors[] = "Tingkat peringatan harus dipilih.";

    if (empty($errors)) {
        $query = "INSERT INTO pesan_peringatan (user_id, judul, pesan, jenis_pelanggaran, tingkat_peringatan, dibuat_oleh, keterangan)
                  VALUES ($user_id, '$judul', '$pesan', '$jenis_pelanggaran', '$tingkat_peringatan', $dibuat_oleh, '$keterangan')";
        
        if (mysqli_query($conn, $query)) {
            $_SESSION['success_message'] = "Pesan peringatan berhasil dibuat.";
            header('Location: pesan_peringatan.php');
            exit;
        } else {
            $_SESSION['error_message'] = "Gagal membuat pesan peringatan: " . mysqli_error($conn);
        }
    } else {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Query untuk mengambil daftar karyawan
$karyawan_query = "SELECT id, nama, nik, bidang FROM users WHERE role = 'karyawan' ORDER BY nama ASC";
$karyawan_result = mysqli_query($conn, $karyawan_query);

$karyawan_list = [];
if ($karyawan_result) {
    while ($row = mysqli_fetch_assoc($karyawan_result)) {
        $karyawan_list[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<style>
.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.tingkat-preview {
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: bold;
    text-align: center;
    margin-top: 10px;
}

.tingkat-ringan { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
.tingkat-sedang { background-color: #ffe8d1; color: #8a4a00; border: 1px solid #fd7e14; }
.tingkat-berat { background-color: #f8d7da; color: #721c24; border: 1px solid #dc3545; }
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">
            <i class="fas fa-plus me-2"></i>
            Buat Pesan Peringatan
        </h1>
        <a href="pesan_peringatan.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Kembali
        </a>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-edit me-2"></i>
                        Form Pesan Peringatan
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="pesanForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="user_id" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Pilih Karyawan <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="user_id" name="user_id" required>
                                    <option value="">-- Pilih Karyawan --</option>
                                    <?php foreach ($karyawan_list as $karyawan): ?>
                                        <option value="<?php echo $karyawan['id']; ?>" 
                                                data-nik="<?php echo $karyawan['nik']; ?>"
                                                data-bidang="<?php echo $karyawan['bidang']; ?>">
                                            <?php echo $karyawan['nama']; ?> (<?php echo $karyawan['nik']; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div id="karyawan-info" class="mt-2" style="display: none;">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        <span id="info-text"></span>
                                    </small>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="jenis_pelanggaran" class="form-label">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Jenis Pelanggaran <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="jenis_pelanggaran" name="jenis_pelanggaran" required>
                                    <option value="">-- Pilih Jenis Pelanggaran --</option>
                                    <option value="terlambat">Terlambat</option>
                                    <option value="tidak_absen">Tidak Absen</option>
                                    <option value="pulang_awal">Pulang Awal</option>
                                    <option value="lainnya">Lainnya</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tingkat_peringatan" class="form-label">
                                    <i class="fas fa-thermometer-half me-1"></i>
                                    Tingkat Peringatan <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="tingkat_peringatan" name="tingkat_peringatan" required>
                                    <option value="">-- Pilih Tingkat --</option>
                                    <option value="ringan">Ringan</option>
                                    <option value="sedang">Sedang</option>
                                    <option value="berat">Berat</option>
                                </select>
                                <div id="tingkat-preview" class="tingkat-preview" style="display: none;"></div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="judul" class="form-label">
                                    <i class="fas fa-heading me-1"></i>
                                    Judul Pesan <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="judul" name="judul" 
                                       placeholder="Contoh: Peringatan Keterlambatan" required maxlength="255">
                                <div class="form-text">Maksimal 255 karakter</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="pesan" class="form-label">
                                <i class="fas fa-comment me-1"></i>
                                Isi Pesan <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="pesan" name="pesan" rows="5" 
                                      placeholder="Tuliskan pesan peringatan yang akan ditampilkan kepada karyawan..." required></textarea>
                            <div class="form-text">Jelaskan pelanggaran dan tindakan yang diharapkan</div>
                        </div>

                        <div class="mb-3">
                            <label for="keterangan" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>
                                Keterangan Tambahan
                            </label>
                            <textarea class="form-control" id="keterangan" name="keterangan" rows="3" 
                                      placeholder="Catatan internal untuk admin (opsional)"></textarea>
                            <div class="form-text">Catatan ini hanya untuk admin, tidak ditampilkan ke karyawan</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="pesan_peringatan.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>
                                Kirim Pesan Peringatan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-info-circle me-2"></i>
                        Panduan
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Tingkat Peringatan:</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <span class="badge bg-warning">Ringan</span>
                            <small class="ms-2">Pelanggaran pertama atau ringan</small>
                        </li>
                        <li class="mb-2">
                            <span class="badge bg-warning">Sedang</span>
                            <small class="ms-2">Pelanggaran berulang atau cukup serius</small>
                        </li>
                        <li class="mb-2">
                            <span class="badge bg-danger">Berat</span>
                            <small class="ms-2">Pelanggaran serius atau berulang kali</small>
                        </li>
                    </ul>

                    <hr>

                    <h6>Tips Menulis Pesan:</h6>
                    <ul class="small">
                        <li>Gunakan bahasa yang sopan dan profesional</li>
                        <li>Jelaskan pelanggaran dengan spesifik</li>
                        <li>Berikan arahan untuk perbaikan</li>
                        <li>Sertakan konsekuensi jika diperlukan</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const userSelect = document.getElementById('user_id');
    const karyawanInfo = document.getElementById('karyawan-info');
    const infoText = document.getElementById('info-text');
    const tingkatSelect = document.getElementById('tingkat_peringatan');
    const tingkatPreview = document.getElementById('tingkat-preview');
    const jenisSelect = document.getElementById('jenis_pelanggaran');
    const judulInput = document.getElementById('judul');

    // Show karyawan info when selected
    userSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const nik = selectedOption.getAttribute('data-nik');
            const bidang = selectedOption.getAttribute('data-bidang');
            infoText.textContent = `NIK: ${nik} | Bidang: ${bidang}`;
            karyawanInfo.style.display = 'block';
        } else {
            karyawanInfo.style.display = 'none';
        }
    });

    // Show tingkat preview
    tingkatSelect.addEventListener('change', function() {
        const tingkat = this.value;
        if (tingkat) {
            tingkatPreview.className = `tingkat-preview tingkat-${tingkat}`;
            tingkatPreview.textContent = `Tingkat: ${tingkat.charAt(0).toUpperCase() + tingkat.slice(1)}`;
            tingkatPreview.style.display = 'block';
        } else {
            tingkatPreview.style.display = 'none';
        }
    });

    // Auto-generate judul based on jenis pelanggaran
    jenisSelect.addEventListener('change', function() {
        const jenis = this.value;
        if (jenis && !judulInput.value) {
            const judulMap = {
                'terlambat': 'Peringatan Keterlambatan',
                'tidak_absen': 'Peringatan Tidak Melakukan Absensi',
                'pulang_awal': 'Peringatan Pulang Sebelum Waktunya',
                'lainnya': 'Peringatan Pelanggaran'
            };
            judulInput.value = judulMap[jenis] || '';
        }
    });

    // Form validation
    document.getElementById('pesanForm').addEventListener('submit', function(e) {
        const requiredFields = ['user_id', 'judul', 'pesan', 'jenis_pelanggaran', 'tingkat_peringatan'];
        let isValid = true;

        requiredFields.forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Mohon lengkapi semua field yang wajib diisi.');
        }
    });

    // Remove invalid class on input
    document.querySelectorAll('.form-control, .form-select').forEach(field => {
        field.addEventListener('input', function() {
            this.classList.remove('is-invalid');
        });
    });
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
