<?php
// Include file konfigurasi
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('karyawan');

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Proses pengajuan pengaduan gangguan absensi
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $tanggal = $_POST['tanggal'] ?? '';
    $jenis_absen = $_POST['jenis_absen'] ?? '';
    $keterangan = $_POST['keterangan'] ?? '';
    
    // Validasi input
    if (empty($tanggal) || empty($jenis_absen) || empty($keterangan)) {
        setMessage('danger', 'Semua field harus diisi!');
    } else {
        // Upload bukti foto jika ada
        $bukti_foto = '';
        if (isset($_FILES['bukti_foto']) && $_FILES['bukti_foto']['error'] == 0) {
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png'];
            $max_size = 5 * 1024 * 1024; // 5MB
            
            if (!in_array($_FILES['bukti_foto']['type'], $allowed_types)) {
                setMessage('danger', 'Tipe file tidak didukung. Hanya file JPG, JPEG, dan PNG yang diperbolehkan.');
            } elseif ($_FILES['bukti_foto']['size'] > $max_size) {
                setMessage('danger', 'Ukuran file terlalu besar. Maksimal 5MB.');
            } else {
                $filename = time() . '_' . $user_id . '_' . $_FILES['bukti_foto']['name'];
                $upload_path = '../uploads/gangguan_absensi/' . $filename;
                
                if (move_uploaded_file($_FILES['bukti_foto']['tmp_name'], $upload_path)) {
                    $bukti_foto = $filename;
                } else {
                    setMessage('danger', 'Gagal mengupload file.');
                }
            }
        }
        
        // Jika tidak ada pesan error, simpan data pengaduan
        if (!isset($_SESSION['message'])) {
            // Cek apakah sudah ada pengaduan pada tanggal dan jenis absen yang sama
            $query = "SELECT * FROM gangguan_absensi WHERE user_id = '$user_id' AND tanggal = '$tanggal' AND jenis_absen = '$jenis_absen'";
            $result = mysqli_query($conn, $query);
            
            if (mysqli_num_rows($result) > 0) {
                setMessage('danger', 'Anda sudah mengajukan pengaduan gangguan absensi untuk tanggal dan jenis absen yang sama.');
            } else {
                // Simpan data pengaduan
                $query = "INSERT INTO gangguan_absensi (user_id, tanggal, jenis_absen, keterangan, bukti_foto) 
                          VALUES ('$user_id', '$tanggal', '$jenis_absen', '$keterangan', '$bukti_foto')";
                
                if (mysqli_query($conn, $query)) {
                    setMessage('success', 'Pengaduan gangguan absensi berhasil diajukan!');
                } else {
                    setMessage('danger', 'Gagal mengajukan pengaduan gangguan absensi!');
                }
            }
        }
    }
    
    // Redirect untuk menghindari resubmission
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Ambil data pengaduan gangguan absensi
$query = "SELECT * FROM gangguan_absensi WHERE user_id = '$user_id' ORDER BY created_at DESC";
$result = mysqli_query($conn, $query);
$pengaduan_list = [];

while ($row = mysqli_fetch_assoc($result)) {
    $pengaduan_list[] = $row;
}

// Set variabel untuk tampilan mobile
$no_sidebar = true;

// Include header
include_once '../includes/header.php';
?>

<div class="mobile-app-container">
    <!-- Header Section -->
    <div class="mobile-app-header">
        <div class="left-section">
            <div class="user-info">
                <div class="user-avatar">
                                    <?php if (!empty($karyawan['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $karyawan['foto_profil']; ?>" alt="Foto Profil" style="width: 70px; height: 70px; ">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
                </div>
                <div class="user-details">
                    <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                    <div class="user-position"><?php echo $karyawan['bidang'] ?? 'Karyawan'; ?></div>
                </div>
            </div>
            <a href="<?php echo BASE_URL; ?>logout.php" class="logout-button" title="Logout">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
        <div class="date-info">
            <i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?>
        </div>
    </div>

    <!-- Content Section -->
    <div class="container px-0">
        <div class="page-title-container">
            <a href="index.php" class="back-button">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h5 class="page-title">Pengaduan Gangguan Absensi</h5>
        </div>

        <?php
        // Tampilkan pesan jika ada
        $message = getMessage();
        if ($message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Form Pengaduan -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">Form Pengaduan Gangguan Absensi</h6>
            </div>
            <div class="card-body">
                <form method="post" action="" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="tanggal" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" required max="<?php echo date('Y-m-d'); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="jenis_absen" class="form-label">Jenis Absen</label>
                        <select class="form-select" id="jenis_absen" name="jenis_absen" required>
                            <option value="">Pilih Jenis Absen</option>
                            <option value="masuk">Absen Masuk</option>
                            <option value="pulang">Absen Pulang</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="keterangan" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="keterangan" name="keterangan" rows="3" required placeholder="Jelaskan alasan gangguan absensi"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="bukti_foto" class="form-label">Bukti Foto (opsional)</label>
                        <input type="file" class="form-control" id="bukti_foto" name="bukti_foto" accept="image/jpeg,image/jpg,image/png">
                        <div class="form-text">Format: JPG, JPEG, PNG. Maks: 5MB</div>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">Ajukan Pengaduan</button>
                </form>
            </div>
        </div>

        <!-- Riwayat Pengaduan -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">Riwayat Pengaduan</h6>
            </div>
            <div class="card-body">
                <?php if (empty($pengaduan_list)): ?>
                    <p class="text-center text-muted">Belum ada pengaduan gangguan absensi</p>
                <?php else: ?>
                    <div class="list-group">
                        <?php foreach ($pengaduan_list as $pengaduan): ?>
                            <a href="#" class="list-group-item list-group-item-action" data-bs-toggle="modal" data-bs-target="#detailModal<?php echo $pengaduan['id']; ?>">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1"><?php echo ucfirst($pengaduan['jenis_absen']); ?> - <?php echo date('d/m/Y', strtotime($pengaduan['tanggal'])); ?></h6>
                                    <?php if ($pengaduan['status'] == 'pending'): ?>
                                        <span class="badge bg-warning text-dark">Menunggu</span>
                                    <?php elseif ($pengaduan['status'] == 'approved'): ?>
                                        <span class="badge bg-success">Disetujui</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Ditolak</span>
                                    <?php endif; ?>
                                </div>
                                <p class="mb-1"><?php echo substr($pengaduan['keterangan'], 0, 100); ?><?php echo strlen($pengaduan['keterangan']) > 100 ? '...' : ''; ?></p>
                                <small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($pengaduan['created_at'])); ?></small>
                            </a>
                            
                            <!-- Modal Detail -->
                            <div class="modal fade" id="detailModal<?php echo $pengaduan['id']; ?>" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="detailModalLabel">Detail Pengaduan</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p><strong>Tanggal:</strong> <?php echo date('d/m/Y', strtotime($pengaduan['tanggal'])); ?></p>
                                            <p><strong>Jenis Absen:</strong> <?php echo ucfirst($pengaduan['jenis_absen']); ?></p>
                                            <p><strong>Keterangan:</strong> <?php echo $pengaduan['keterangan']; ?></p>
                                            <p><strong>Status:</strong> 
                                                <?php if ($pengaduan['status'] == 'pending'): ?>
                                                    <span class="badge bg-warning text-dark">Menunggu</span>
                                                <?php elseif ($pengaduan['status'] == 'approved'): ?>
                                                    <span class="badge bg-success">Disetujui</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Ditolak</span>
                                                <?php endif; ?>
                                            </p>
                                            <?php if (!empty($pengaduan['catatan_admin'])): ?>
                                                <p><strong>Catatan Admin:</strong> <?php echo $pengaduan['catatan_admin']; ?></p>
                                            <?php endif; ?>
                                            <?php if (!empty($pengaduan['bukti_foto'])): ?>
                                                <p><strong>Bukti Foto:</strong></p>
                                                <img src="<?php echo BASE_URL . 'uploads/gangguan_absensi/' . $pengaduan['bukti_foto']; ?>" alt="Bukti Foto" class="img-fluid">
                                            <?php endif; ?>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <?php include_once '../includes/bottom_nav.php'; ?>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
