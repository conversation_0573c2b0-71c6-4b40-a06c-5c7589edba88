<?php
/**
 * Fungsi-fungsi untuk mengelola pesan peringatan karyawan
 */

/**
 * Mengambil pesan peringatan aktif untuk karyawan
 * @param int $user_id ID karyawan
 * @return array Daftar pesan peringatan aktif
 */
function getPesanPeringatanAktif($user_id) {
    global $conn;
    
    $user_id = (int)$user_id;
    $query = "SELECT pp.*, admin.nama as admin_nama
              FROM pesan_peringatan pp
              JOIN users admin ON pp.dibuat_oleh = admin.id
              WHERE pp.user_id = $user_id 
              AND pp.status = 'aktif'
              ORDER BY pp.tingkat_peringatan DESC, pp.tanggal_dibuat DESC";
    
    $result = mysqli_query($conn, $query);
    $pesan_list = [];
    
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $pesan_list[] = $row;
        }
    }
    
    return $pesan_list;
}

/**
 * Menandai pesan peringatan sebagai sudah dibaca
 * @param int $pesan_id ID pesan peringatan
 * @param int $user_id ID karyawan (untuk validasi)
 * @return bool Status berhasil atau tidak
 */
function tandaiPesanDibaca($pesan_id, $user_id) {
    global $conn;
    
    $pesan_id = (int)$pesan_id;
    $user_id = (int)$user_id;
    $tanggal_dibaca = date('Y-m-d H:i:s');
    
    $query = "UPDATE pesan_peringatan 
              SET status = 'dibaca', tanggal_dibaca = '$tanggal_dibaca'
              WHERE id = $pesan_id AND user_id = $user_id AND status = 'aktif'";
    
    return mysqli_query($conn, $query);
}

/**
 * Menghitung jumlah pesan peringatan aktif untuk karyawan
 * @param int $user_id ID karyawan
 * @return int Jumlah pesan aktif
 */
function hitungPesanAktif($user_id) {
    global $conn;
    
    $user_id = (int)$user_id;
    $query = "SELECT COUNT(*) as total FROM pesan_peringatan 
              WHERE user_id = $user_id AND status = 'aktif'";
    
    $result = mysqli_query($conn, $query);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        return (int)$row['total'];
    }
    
    return 0;
}

/**
 * Mengambil statistik pesan peringatan untuk karyawan
 * @param int $user_id ID karyawan
 * @return array Statistik pesan
 */
function getStatistikPesan($user_id) {
    global $conn;
    
    $user_id = (int)$user_id;
    $query = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'aktif' THEN 1 ELSE 0 END) as aktif,
                SUM(CASE WHEN status = 'dibaca' THEN 1 ELSE 0 END) as dibaca,
                SUM(CASE WHEN tingkat_peringatan = 'ringan' THEN 1 ELSE 0 END) as ringan,
                SUM(CASE WHEN tingkat_peringatan = 'sedang' THEN 1 ELSE 0 END) as sedang,
                SUM(CASE WHEN tingkat_peringatan = 'berat' THEN 1 ELSE 0 END) as berat
              FROM pesan_peringatan 
              WHERE user_id = $user_id";
    
    $result = mysqli_query($conn, $query);
    if ($result) {
        return mysqli_fetch_assoc($result);
    }
    
    return [
        'total' => 0,
        'aktif' => 0,
        'dibaca' => 0,
        'ringan' => 0,
        'sedang' => 0,
        'berat' => 0
    ];
}

/**
 * Mengambil riwayat pesan peringatan untuk karyawan
 * @param int $user_id ID karyawan
 * @param int $limit Batas jumlah data
 * @return array Riwayat pesan
 */
function getRiwayatPesan($user_id, $limit = 10) {
    global $conn;
    
    $user_id = (int)$user_id;
    $limit = (int)$limit;
    
    $query = "SELECT pp.*, admin.nama as admin_nama
              FROM pesan_peringatan pp
              JOIN users admin ON pp.dibuat_oleh = admin.id
              WHERE pp.user_id = $user_id
              ORDER BY pp.tanggal_dibuat DESC
              LIMIT $limit";
    
    $result = mysqli_query($conn, $query);
    $riwayat = [];
    
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $riwayat[] = $row;
        }
    }
    
    return $riwayat;
}

/**
 * Mengecek apakah karyawan memiliki pesan peringatan berat yang aktif
 * @param int $user_id ID karyawan
 * @return bool True jika ada pesan berat aktif
 */
function adaPesanBerat($user_id) {
    global $conn;
    
    $user_id = (int)$user_id;
    $query = "SELECT COUNT(*) as total FROM pesan_peringatan 
              WHERE user_id = $user_id 
              AND status = 'aktif' 
              AND tingkat_peringatan = 'berat'";
    
    $result = mysqli_query($conn, $query);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        return (int)$row['total'] > 0;
    }
    
    return false;
}

/**
 * Format tingkat peringatan untuk tampilan
 * @param string $tingkat Tingkat peringatan
 * @return array Array dengan class dan label
 */
function formatTingkatPeringatan($tingkat) {
    $format = [
        'ringan' => [
            'class' => 'warning',
            'bg_class' => 'bg-warning',
            'text_class' => 'text-warning',
            'icon' => 'fas fa-exclamation-triangle',
            'label' => 'Peringatan Ringan'
        ],
        'sedang' => [
            'class' => 'warning',
            'bg_class' => 'bg-warning',
            'text_class' => 'text-warning',
            'icon' => 'fas fa-exclamation-circle',
            'label' => 'Peringatan Sedang'
        ],
        'berat' => [
            'class' => 'danger',
            'bg_class' => 'bg-danger',
            'text_class' => 'text-danger',
            'icon' => 'fas fa-times-circle',
            'label' => 'Peringatan Berat'
        ]
    ];
    
    return $format[$tingkat] ?? $format['ringan'];
}

/**
 * Format jenis pelanggaran untuk tampilan
 * @param string $jenis Jenis pelanggaran
 * @return string Label yang sudah diformat
 */
function formatJenisPelanggaran($jenis) {
    $labels = [
        'terlambat' => 'Keterlambatan',
        'tidak_absen' => 'Tidak Melakukan Absensi',
        'pulang_awal' => 'Pulang Sebelum Waktunya',
        'lainnya' => 'Pelanggaran Lainnya'
    ];
    
    return $labels[$jenis] ?? ucfirst($jenis);
}

/**
 * Mengecek apakah user adalah admin
 * @return bool True jika user adalah admin
 */
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

/**
 * Mengecek apakah user adalah karyawan
 * @return bool True jika user adalah karyawan
 */
function isKaryawan() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'karyawan';
}
?>
