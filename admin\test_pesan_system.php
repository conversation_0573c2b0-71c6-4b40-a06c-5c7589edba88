<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/pesan_functions.php';

// Cek akses admin
checkAccess('admin');

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-vial me-2"></i>
                        Test Sistem Pesan Peringatan
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Halaman ini untuk menguji apakah sistem pesan peringatan berfungsi dengan baik.
                    </div>

                    <?php
                    echo "<h6>1. Test Database Connection:</h6>";
                    if ($conn) {
                        echo "<div class='alert alert-success'>✅ Database connection: OK</div>";
                    } else {
                        echo "<div class='alert alert-danger'>❌ Database connection: FAILED</div>";
                    }

                    echo "<h6>2. Test Table pesan_peringatan:</h6>";
                    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'pesan_peringatan'");
                    if ($table_check && mysqli_num_rows($table_check) > 0) {
                        echo "<div class='alert alert-success'>✅ Table pesan_peringatan: EXISTS</div>";
                        
                        // Cek struktur tabel
                        $structure_check = mysqli_query($conn, "DESCRIBE pesan_peringatan");
                        if ($structure_check) {
                            echo "<div class='alert alert-info'>";
                            echo "<strong>Table Structure:</strong><br>";
                            echo "<table class='table table-sm mt-2'>";
                            echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr></thead>";
                            echo "<tbody>";
                            while ($row = mysqli_fetch_assoc($structure_check)) {
                                echo "<tr>";
                                echo "<td>" . $row['Field'] . "</td>";
                                echo "<td>" . $row['Type'] . "</td>";
                                echo "<td>" . $row['Null'] . "</td>";
                                echo "<td>" . $row['Key'] . "</td>";
                                echo "</tr>";
                            }
                            echo "</tbody></table>";
                            echo "</div>";
                        }
                    } else {
                        echo "<div class='alert alert-danger'>❌ Table pesan_peringatan: NOT EXISTS</div>";
                        echo "<p><a href='create_pesan_table.php' class='btn btn-primary'>Create Table</a></p>";
                    }

                    echo "<h6>3. Test Functions:</h6>";
                    
                    // Test function exists
                    $functions_to_test = [
                        'getPesanPeringatanAktif',
                        'tandaiPesanDibaca',
                        'hitungPesanAktif',
                        'formatTingkatPeringatan',
                        'formatJenisPelanggaran'
                    ];
                    
                    foreach ($functions_to_test as $func) {
                        if (function_exists($func)) {
                            echo "<div class='alert alert-success'>✅ Function $func: EXISTS</div>";
                        } else {
                            echo "<div class='alert alert-danger'>❌ Function $func: NOT EXISTS</div>";
                        }
                    }

                    echo "<h6>4. Test Sample Data:</h6>";
                    
                    // Cek apakah ada data karyawan
                    $karyawan_check = mysqli_query($conn, "SELECT COUNT(*) as total FROM users WHERE role = 'karyawan'");
                    if ($karyawan_check) {
                        $karyawan_count = mysqli_fetch_assoc($karyawan_check)['total'];
                        if ($karyawan_count > 0) {
                            echo "<div class='alert alert-success'>✅ Karyawan data: $karyawan_count karyawan found</div>";
                        } else {
                            echo "<div class='alert alert-warning'>⚠️ Karyawan data: No karyawan found</div>";
                        }
                    }

                    // Cek apakah ada data pesan
                    if (mysqli_num_rows($table_check) > 0) {
                        $pesan_check = mysqli_query($conn, "SELECT COUNT(*) as total FROM pesan_peringatan");
                        if ($pesan_check) {
                            $pesan_count = mysqli_fetch_assoc($pesan_check)['total'];
                            echo "<div class='alert alert-info'>📊 Pesan peringatan: $pesan_count pesan in database</div>";
                        }
                    }

                    echo "<h6>5. Test Format Functions:</h6>";
                    
                    // Test format functions
                    $test_tingkat = ['ringan', 'sedang', 'berat'];
                    foreach ($test_tingkat as $tingkat) {
                        $format = formatTingkatPeringatan($tingkat);
                        echo "<div class='alert alert-light'>";
                        echo "<strong>Tingkat '$tingkat':</strong> ";
                        echo "<span class='badge {$format['bg_class']} text-white'>{$format['label']}</span> ";
                        echo "<i class='{$format['icon']} {$format['text_class']}'></i>";
                        echo "</div>";
                    }

                    $test_jenis = ['terlambat', 'tidak_absen', 'pulang_awal', 'lainnya'];
                    foreach ($test_jenis as $jenis) {
                        $label = formatJenisPelanggaran($jenis);
                        echo "<div class='alert alert-light'>";
                        echo "<strong>Jenis '$jenis':</strong> $label";
                        echo "</div>";
                    }

                    echo "<h6>6. Test User Session:</h6>";
                    if (isset($_SESSION['user_id'])) {
                        echo "<div class='alert alert-success'>✅ User ID: " . $_SESSION['user_id'] . "</div>";
                        echo "<div class='alert alert-success'>✅ User Role: " . $_SESSION['role'] . "</div>";
                        echo "<div class='alert alert-success'>✅ User Name: " . $_SESSION['nama'] . "</div>";
                    } else {
                        echo "<div class='alert alert-danger'>❌ No user session found</div>";
                    }
                    ?>
                    
                    <div class="mt-4">
                        <h6>Quick Actions:</h6>
                        <div class="btn-group">
                            <a href="pesan_peringatan.php" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i>
                                Kelola Pesan Peringatan
                            </a>
                            <a href="tambah_pesan_peringatan.php" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                Buat Pesan Baru
                            </a>
                            <a href="create_pesan_table.php" class="btn btn-warning">
                                <i class="fas fa-database me-2"></i>
                                Create/Check Table
                            </a>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
