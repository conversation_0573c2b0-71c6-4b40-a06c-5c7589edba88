<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Absensiku - Offline</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">

    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .offline-container {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 24px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            max-width: 500px;
            width: 100%;
            text-align: center;
            padding: 40px 30px;
        }

        .offline-icon {
            font-size: 60px;
            color: #6a11cb;
            margin-bottom: 20px;
        }

        .offline-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .offline-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .offline-button {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            border-radius: 50px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(106, 17, 203, 0.3);
        }

        .offline-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(106, 17, 203, 0.4);
            color: white;
        }

        .offline-image {
            max-width: 250px;
            margin: 0 auto 30px;
        }

        .offline-footer {
            margin-top: 30px;
            font-size: 14px;
            color: #888;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            <i class="fas fa-wifi-slash"></i>
        </div>

        <h1 class="offline-title">Anda Sedang Offline</h1>

        <p class="offline-message">
            Sepertinya Anda tidak terhubung ke internet. Periksa koneksi Anda dan coba lagi.
        </p>

        <img src="assets/images/offline.svg" alt="Offline Illustration" class="offline-image">

        <a href="index.php" class="offline-button">
            <i class="fas fa-sync-alt me-2"></i> Coba Lagi
        </a>

        <div class="offline-footer">
            Aplikasi ini dibuat oleh Ikhlasul Amal
        </div>
    </div>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Fungsi untuk mendapatkan base URL yang benar untuk webhosting
        function getBaseUrl() {
            // Gunakan index.php sebagai default untuk domain absensiku.trunois.my.id
            let baseUrl = 'index.php';

            // Jika ada referrer, gunakan itu untuk kembali ke halaman sebelumnya
            if (document.referrer) {
                try {
                    const referrerUrl = new URL(document.referrer);
                    // Jika referrer dari domain yang sama, gunakan path-nya
                    if (referrerUrl.hostname === window.location.hostname) {
                        baseUrl = document.referrer;
                    }
                } catch (e) {
                    console.error('Error parsing referrer URL:', e);
                }
            }

            return baseUrl;
        }

        // Set URL yang benar untuk tombol coba lagi
        document.querySelector('.offline-button').href = getBaseUrl();

        // Cek koneksi internet secara berkala
        function checkConnection() {
            if (navigator.onLine) {
                document.querySelector('.offline-button').classList.remove('disabled');
                document.querySelector('.offline-title').textContent = 'Anda Sedang Offline';
                document.querySelector('.offline-message').textContent =
                    'Sepertinya Anda tidak terhubung ke internet. Periksa koneksi Anda dan coba lagi.';
            } else {
                document.querySelector('.offline-button').classList.add('disabled');
                document.querySelector('.offline-title').textContent = 'Anda Sedang Offline';
                document.querySelector('.offline-message').textContent =
                    'Sepertinya Anda tidak terhubung ke internet. Periksa koneksi Anda dan coba lagi.';
            }
        }

        // Cek koneksi saat halaman dimuat
        window.addEventListener('load', checkConnection);

        // Cek koneksi saat status online/offline berubah
        window.addEventListener('online', function() {
            document.querySelector('.offline-button').classList.remove('disabled');
            document.querySelector('.offline-title').textContent = 'Koneksi Pulih';
            document.querySelector('.offline-message').textContent =
                'Koneksi internet Anda telah pulih. Klik tombol di bawah untuk kembali ke aplikasi.';
        });

        window.addEventListener('offline', function() {
            document.querySelector('.offline-button').classList.add('disabled');
        });

        // Tombol coba lagi dengan SweetAlert
        document.querySelector('.offline-button').addEventListener('click', function(e) {
            if (!navigator.onLine) {
                e.preventDefault();

                // Tampilkan SweetAlert sebagai pengganti alert bawaan
                Swal.fire({
                    title: 'Tidak Ada Koneksi',
                    text: 'Anda masih offline. Silakan periksa koneksi internet Anda.',
                    icon: 'error',
                    confirmButtonText: 'Tutup',
                    confirmButtonColor: '#6a11cb',
                    customClass: {
                        popup: 'swal-offline-popup',
                        title: 'swal-offline-title',
                        content: 'swal-offline-content',
                        confirmButton: 'swal-offline-button'
                    }
                });
            }
        });
    </script>

    <style>
        /* Custom style untuk SweetAlert */
        .swal-offline-popup {
            border-radius: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .swal-offline-title {
            font-family: 'Roboto', sans-serif;
            font-weight: 600;
        }

        .swal-offline-content {
            font-family: 'Roboto', sans-serif;
        }

        .swal-offline-button {
            border-radius: 50px !important;
            padding: 10px 25px !important;
            font-weight: 500 !important;
            box-shadow: 0 4px 10px rgba(106, 17, 203, 0.3) !important;
        }
    </style>
</body>
</html>
