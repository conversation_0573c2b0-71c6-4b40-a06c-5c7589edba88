/* Mobile Help and Change Password Style */

/* Base Styles */
.mobile-help-container,
.mobile-password-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
    padding-bottom: 80px; /* Add padding for bottom navigation */
}

@media (min-width: 768px) {
    .mobile-help-container,
    .mobile-password-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-help-container,
    .mobile-password-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-help-container,
    .mobile-password-container {
        max-width: 840px;
    }
}

.mobile-help-header,
.mobile-password-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
    text-align: center;
}

.mobile-help-header h4,
.mobile-password-header h4 {
    margin: 0;
    font-size: 22px;
    font-weight: bold;
}

/* Content Styles */
.mobile-help-content,
.mobile-password-content {
    padding: 0 15px 80px 15px;
}

/* Card Styles */
.help-card,
.password-card {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
}

.help-card-header,
.password-card-header {
    background-color: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    font-weight: bold;
    color: #4e73df;
}

.help-card-body,
.password-card-body {
    padding: 15px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    color: #343a40;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ced4da;
    border-radius: 10px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-control:focus {
    border-color: #4e73df;
    outline: none;
    box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
}

.form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 500;
    color: white;
    width: 100%;
    transition: all 0.3s;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3a5fcc 0%, #1a3da3 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-secondary {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 500;
    color: #6c757d;
    width: 100%;
    transition: all 0.3s;
    margin-top: 10px;
}

.btn-secondary:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
}

.btn-secondary:active {
    transform: translateY(0);
}

/* Accordion Styles for Help */
.accordion-item {
    border: none;
    margin-bottom: 10px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.accordion-button {
    padding: 15px;
    font-weight: 500;
    background-color: white;
}

.accordion-button:not(.collapsed) {
    background-color: #e7f1ff;
    color: #4e73df;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: #e9ecef;
}

.accordion-body {
    padding: 15px;
    background-color: white;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: none;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: none;
}

/* Back Button */
.back-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    color: #4e73df;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
    background-color: #f8f9fa;
}

/* Bottom Navigation Styles */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 70px;
    background-color: #ffffff;
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding-bottom: env(safe-area-inset-bottom);
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    text-decoration: none;
    padding: 8px 0;
    width: 20%;
    transition: all 0.3s ease;
}

.bottom-nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
}

.bottom-nav-item span {
    font-size: 12px;
    font-weight: 500;
}

.bottom-nav-item.active {
    color: #4e73df;
}

.bottom-nav-item:hover {
    color: #4e73df;
}

.bottom-nav-item-placeholder {
    width: 20%;
}

/* Floating Attendance Button */
.attendance-button {
    position: fixed;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    text-decoration: none;
    transition: all 0.3s ease;
}

.attendance-button:hover {
    transform: translateX(-50%) scale(1.05);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    color: white;
}

.attendance-button:active {
    transform: translateX(-50%) scale(0.95);
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .mobile-help-header,
    .mobile-password-header {
        padding: 15px;
    }

    .mobile-help-header h4,
    .mobile-password-header h4 {
        font-size: 18px;
    }

    .mobile-help-content,
    .mobile-password-content {
        padding: 0 10px 80px 10px;
    }
}

/* Desktop Specific Adjustments */
@media (min-width: 992px) {
    .mobile-help-container,
    .mobile-password-container {
        padding: 0;
        border-radius: 15px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        margin-top: 20px;
        margin-bottom: 20px;
        min-height: calc(100vh - 40px);
    }

    .mobile-help-header,
    .mobile-password-header {
        margin-left: 0;
        margin-right: 0;
        margin-top: 0;
        border-radius: 15px 15px 20px 20px;
    }
}
