<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek apakah tabel rapat sudah ada
$query = "SHOW TABLES LIKE 'rapat'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    // Buat tabel rapat
    $query = "CREATE TABLE IF NOT EXISTS `rapat` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `judul` varchar(255) NOT NULL,
      `tanggal` date NOT NULL,
      `waktu_mulai` time NOT NULL,
      `waktu_selesai` time NOT NULL,
      `lokasi` varchar(255) NOT NULL,
      `deskripsi` text DEFAULT NULL,
      `barcode_value` varchar(255) NOT NULL,
      `created_by` int(11) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `created_by` (`created_by`),
      CONSTRAINT `rapat_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Tabel rapat berhasil dibuat!');
    } else {
        setMessage('danger', 'Gagal membuat tabel rapat: ' . mysqli_error($conn));
    }
}

// Cek apakah tabel rapat_peserta sudah ada
$query = "SHOW TABLES LIKE 'rapat_peserta'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    // Buat tabel rapat_peserta
    $query = "CREATE TABLE IF NOT EXISTS `rapat_peserta` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `rapat_id` int(11) NOT NULL,
      `user_id` int(11) NOT NULL,
      `status` enum('hadir','tidak hadir') DEFAULT NULL,
      `waktu_hadir` datetime DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `rapat_user_unique` (`rapat_id`,`user_id`),
      KEY `rapat_id` (`rapat_id`),
      KEY `user_id` (`user_id`),
      CONSTRAINT `rapat_peserta_ibfk_1` FOREIGN KEY (`rapat_id`) REFERENCES `rapat` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
      CONSTRAINT `rapat_peserta_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Tabel rapat_peserta berhasil dibuat!');
    } else {
        setMessage('danger', 'Gagal membuat tabel rapat_peserta: ' . mysqli_error($conn));
    }
}

// Redirect ke halaman rapat
redirect('admin/rapat.php');
?>
