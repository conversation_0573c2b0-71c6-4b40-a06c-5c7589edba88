<?php
/**
 * API untuk mendapatkan status device
 * Endpoint ini mengembalikan status device dan informasi tambahan untuk aplikasi Android
 *
 * Format request:
 * {
 *     "api_key": "absensiku_api_key_2023",
 *     "device_id": "abc123xyz456",
 *     "nik": "123456789" // opsional
 * }
 * 
 * Format response jika device diblokir:
 * {
 *     "status": "blocked",
 *     "message": "Device diblokir",
 *     "can_unblock": true/false,
 *     "data": {
 *         "nik": "123456789",
 *         "nama": "<PERSON><PERSON>",
 *         "alasan": "Alasan pemblokiran",
 *         "tanggal_blokir": "01/01/2023 12:00:00"
 *     }
 * }
 * 
 * Format response jika device diizinkan:
 * {
 *     "status": "allowed",
 *     "message": "Device diizinkan"
 * }
 */

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Fungsi untuk validasi API key
function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Fungsi untuk memeriksa status blokir device dengan informasi tambahan
function getDeviceStatus($device_id, $nik = null) {
    global $conn;

    // Escape data
    $device_id = mysqli_real_escape_string($conn, $device_id);

    // Buat query dasar
    $query = "SELECT bd.*, u.nama, u.role
              FROM blokir_device bd
              LEFT JOIN users u ON bd.user_id = u.id
              WHERE bd.status = 'active' AND (bd.device_id = '$device_id'";

    // Tambahkan kondisi NIK jika ada
    if ($nik) {
        $nik = mysqli_real_escape_string($conn, $nik);
        $query .= " OR bd.nik = '$nik'";
    }

    $query .= ")";

    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        // Device atau NIK diblokir
        $data = mysqli_fetch_assoc($result);
        
        // Tentukan apakah device dapat dibuka blokirnya
        // Misalnya, hanya admin yang dapat membuka blokir
        $can_unblock = false;
        
        // Jika ada kondisi khusus untuk membuka blokir, tambahkan di sini
        // Misalnya, jika alasan pemblokiran tertentu, device dapat dibuka blokirnya
        if (strpos(strtolower($data['alasan']), 'sementara') !== false) {
            $can_unblock = true;
        }
        
        return [
            'status' => 'blocked',
            'message' => 'Device diblokir',
            'can_unblock' => $can_unblock,
            'data' => [
                'id' => $data['id'],
                'nik' => $data['nik'],
                'nama' => $data['nama'],
                'alasan' => $data['alasan'],
                'tanggal_blokir' => date('d/m/Y H:i:s', strtotime($data['created_at'])),
                'tanggal_update' => date('d/m/Y H:i:s', strtotime($data['updated_at']))
            ]
        ];
    } else {
        // Device dan NIK tidak diblokir
        return [
            'status' => 'allowed',
            'message' => 'Device diizinkan'
        ];
    }
}

// Fungsi untuk membuka blokir device
function unblockDevice($device_id, $nik = null) {
    global $conn;

    // Escape data
    $device_id = mysqli_real_escape_string($conn, $device_id);
    
    // Buat query dasar
    $query = "UPDATE blokir_device SET status = 'inactive', updated_at = NOW() WHERE device_id = '$device_id'";
    
    // Tambahkan kondisi NIK jika ada
    if ($nik) {
        $nik = mysqli_real_escape_string($conn, $nik);
        $query = "UPDATE blokir_device SET status = 'inactive', updated_at = NOW() WHERE device_id = '$device_id' OR nik = '$nik'";
    }
    
    $result = mysqli_query($conn, $query);
    
    if ($result) {
        return [
            'status' => 'success',
            'message' => 'Device berhasil dibuka blokirnya'
        ];
    } else {
        return [
            'status' => 'error',
            'message' => 'Gagal membuka blokir device: ' . mysqli_error($conn)
        ];
    }
}

// Ambil data dari request
$data = json_decode(file_get_contents('php://input'), true);

// Jika data tidak valid, coba ambil dari $_POST atau $_GET
if (!$data) {
    $data = $_POST ?: $_GET;
}

// Validasi API key
if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'API key tidak valid'
    ]);
    exit;
}

// Validasi device_id
if (!isset($data['device_id'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Device ID tidak ditemukan'
    ]);
    exit;
}

// Ambil NIK jika ada
$nik = isset($data['nik']) ? $data['nik'] : null;

// Cek apakah request untuk membuka blokir
if (isset($data['action']) && $data['action'] === 'unblock') {
    // Periksa apakah ada kode verifikasi (opsional)
    if (isset($data['verification_code']) && $data['verification_code'] === 'UNBLOCK123') {
        $result = unblockDevice($data['device_id'], $nik);
        echo json_encode($result);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'Kode verifikasi tidak valid'
        ]);
    }
} else {
    // Periksa status device
    $result = getDeviceStatus($data['device_id'], $nik);
    echo json_encode($result);
}
?>
