            </div>
        </div>
    </div>

    <?php
    // Tambahkan Bottom Navigation Bar untuk halaman karyawan
    if (isset($is_karyawan_page) && $is_karyawan_page) {
        include_once 'bottom-nav.php';
    }
    ?>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.1/js/dataTables.bootstrap5.min.js"></script>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

    <!-- Custom JS -->
    <script>
        $(document).ready(function() {
            // Nonaktifkan DataTables untuk tabel dengan kelas 'datatable'
            // dan gunakan kelas 'table-datatable' untuk tabel yang ingin menggunakan DataTables
            try {
                // Tambahkan kelas untuk styling tabel yang tidak menggunakan DataTables
                $('.datatable').addClass('table-striped table-hover');

                // Inisialisasi DataTables hanya untuk tabel dengan kelas 'table-datatable'
                if ($('.table-datatable').length > 0) {
                    $('.table-datatable').DataTable({
                        language: {
                            url: BASE_URL + 'assets/js/dataTables.indonesian.json'
                        },
                        // Nonaktifkan sorting otomatis untuk mencegah masalah
                        "aaSorting": []
                    });
                }
            } catch (e) {
                console.error('Error initializing DataTables:', e);
            }

            // Tooltip
            try {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl)
                });
            } catch (e) {
                console.error('Error initializing Tooltips:', e);
            }

            // Fix untuk modal berkedip
            try {
                // Pastikan hanya satu modal yang aktif pada satu waktu
                $('.modal').on('show.bs.modal', function () {
                    $('.modal').not(this).modal('hide');
                });

                // Perbaiki masalah backdrop
                $('.modal').on('shown.bs.modal', function () {
                    if ($('.modal-backdrop').length > 1) {
                        $('.modal-backdrop').not(':first').remove();
                    }
                });

                // Perbaiki masalah z-index
                $('.modal').on('show.bs.modal', function (e) {
                    var zIndex = 1050 + (10 * $('.modal:visible').length);
                    $(this).css('z-index', zIndex);
                    setTimeout(function() {
                        $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
                    }, 0);
                });

                // Fix khusus untuk modal edit karyawan
                $('.edit-modal').on('show.bs.modal', function (e) {
                    // Pastikan modal ini memiliki z-index tertinggi
                    $(this).css('z-index', 1060);

                    // Tambahkan kelas khusus pada backdrop
                    setTimeout(function() {
                        $('.modal-backdrop').last().addClass('edit-backdrop');
                    }, 0);
                });

                // Fix khusus untuk modal reset password
                $('.reset-modal').on('show.bs.modal', function (e) {
                    // Pastikan modal ini memiliki z-index tertinggi
                    $(this).css('z-index', 1070);

                    // Tambahkan kelas khusus pada backdrop
                    setTimeout(function() {
                        $('.modal-backdrop').last().addClass('reset-backdrop');
                    }, 0);
                });

                // Fix khusus untuk modal tambah karyawan
                $('.add-modal').on('show.bs.modal', function (e) {
                    // Pastikan modal ini memiliki z-index tertinggi
                    $(this).css('z-index', 1050);

                    // Tambahkan kelas khusus pada backdrop
                    setTimeout(function() {
                        $('.modal-backdrop').last().addClass('add-backdrop');
                    }, 0);
                });
            } catch (e) {
                console.error('Error fixing modal issues:', e);
            }

            // Aktifkan sidebar sesuai halaman aktif
            try {
                var currentUrl = window.location.pathname;

                // Cek untuk dropdown items
                var activeDropdownFound = false;
                $('.sidebar .dropdown-item').each(function() {
                    var linkUrl = $(this).attr('href');
                    if (linkUrl && currentUrl.indexOf(linkUrl) !== -1) {
                        $(this).addClass('active');
                        $(this).closest('.dropdown').find('.nav-link').addClass('active');
                        activeDropdownFound = true;
                    }
                });

                // Jika tidak ada dropdown item yang aktif, cek nav-link biasa
                if (!activeDropdownFound) {
                    $('.sidebar .nav-link').not('.dropdown-toggle').each(function() {
                        var linkUrl = $(this).attr('href');
                        if (linkUrl && currentUrl.indexOf(linkUrl) !== -1) {
                            $(this).addClass('active');
                        }
                    });
                }

                // Buka dropdown yang memiliki item aktif
                $('.sidebar .dropdown-item.active').closest('.dropdown-menu').addClass('show');
                $('.sidebar .dropdown-item.active').closest('.dropdown').find('.dropdown-toggle').attr('aria-expanded', 'true');
            } catch (e) {
                console.error('Error activating sidebar:', e);
            }

            // Mobile sidebar toggle
            try {
                // Toggle sidebar
                $('#sidebarToggle').on('click', function() {
                    $('.sidebar').toggleClass('show');
                    $('.overlay').toggleClass('show');
                });

                // Close sidebar when clicking on overlay
                $('.overlay').on('click', function() {
                    $('.sidebar').removeClass('show');
                    $('.overlay').removeClass('show');
                });

                // Close sidebar when clicking close button
                $('#sidebarClose').on('click', function() {
                    $('.sidebar').removeClass('show');
                    $('.overlay').removeClass('show');
                });
            } catch (e) {
                console.error('Error setting up mobile sidebar:', e);
            }
        });
    </script>
</body>
</html>
