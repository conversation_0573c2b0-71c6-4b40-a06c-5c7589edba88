<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil ID presensi
$id = isset($_GET['id']) ? clean($_GET['id']) : 0;

// Ambil data presensi
$query = "SELECT p.*, u.nama, u.bidang 
          FROM presensi p 
          JOIN users u ON p.user_id = u.id 
          WHERE p.id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Data presensi tidak ditemukan!');
    redirect('admin/monitoring.php');
}

$presensi = mysqli_fetch_assoc($result);
$tanggal = $presensi['tanggal'];
$user_id = $presensi['user_id'];
$nama = $presensi['nama'];

// Cek apakah sudah ada absen pulang
if (!empty($presensi['jam_pulang'])) {
    setMessage('warning', 'Karyawan sudah melakukan absen pulang!');
    redirect('admin/monitoring.php?tanggal=' . $tanggal);
}

// Proses form
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $tujuan = isset($_POST['tujuan']) ? clean($_POST['tujuan']) : '';
    $keterangan = isset($_POST['keterangan']) ? clean($_POST['keterangan']) : '';
    $jam_pulang = isset($_POST['jam_pulang']) ? clean($_POST['jam_pulang']) : date('H:i:s');
    
    if (empty($tujuan)) {
        setMessage('danger', 'Tujuan perjalanan dinas tidak boleh kosong!');
    } else {
        // Update data presensi dengan status perjalanan dinas
        $keterangan_lengkap = "Perjalanan Dinas: $tujuan" . (!empty($keterangan) ? " - $keterangan" : "");
        
        $query = "UPDATE presensi SET 
                  jam_pulang = '$jam_pulang',
                  lokasi_pulang = 'Perjalanan Dinas',
                  status = 'Tepat Waktu',
                  keterangan = '$keterangan_lengkap',
                  updated_at = NOW()
                  WHERE id = '$id'";
                  
        if (mysqli_query($conn, $query)) {
            // Cek apakah sudah ada izin dinas untuk tanggal ini
            $query_cek = "SELECT * FROM izin_dinas 
                         WHERE user_id = '$user_id' 
                         AND tanggal_mulai <= '$tanggal' 
                         AND tanggal_selesai >= '$tanggal'
                         AND status = 'Approved'";
            $result_cek = mysqli_query($conn, $query_cek);
            
            // Jika belum ada izin dinas, buat izin dinas baru
            if (mysqli_num_rows($result_cek) == 0) {
                $query_izin = "INSERT INTO izin_dinas (user_id, tanggal_mulai, tanggal_selesai, tujuan, keterangan, status, created_at, updated_at)
                              VALUES ('$user_id', '$tanggal', '$tanggal', '$tujuan', '$keterangan', 'Approved', NOW(), NOW())";
                mysqli_query($conn, $query_izin);
            }
            
            setMessage('success', "Berhasil menandai presensi $nama sebagai perjalanan dinas!");
            redirect('admin/monitoring.php?tanggal=' . $tanggal);
        } else {
            setMessage('danger', 'Gagal memperbarui data presensi: ' . mysqli_error($conn));
        }
    }
}

// Include header
include_once '../includes/header.php';

// Ambil pesan jika ada
$message = getMessage();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Tandai Perjalanan Dinas</h1>
        <a href="monitoring.php?tanggal=<?php echo $tanggal; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $message['text']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Tandai Perjalanan Dinas</h6>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Informasi Presensi</h5>
                    <table class="table table-borderless">
                        <tr>
                            <td width="150">Nama</td>
                            <td>: <?php echo $presensi['nama']; ?></td>
                        </tr>
                        <tr>
                            <td>Bidang</td>
                            <td>: <?php echo $presensi['bidang']; ?></td>
                        </tr>
                        <tr>
                            <td>Tanggal</td>
                            <td>: <?php echo date('d F Y', strtotime($presensi['tanggal'])); ?></td>
                        </tr>
                        <tr>
                            <td>Jam Masuk</td>
                            <td>: <?php echo $presensi['jam_masuk']; ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <form method="post" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="tujuan" class="form-label">Tujuan Perjalanan Dinas <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="tujuan" name="tujuan" required>
                            <small class="text-muted">Contoh: Jakarta, Surabaya, dll.</small>
                        </div>
                        <div class="mb-3">
                            <label for="keterangan" class="form-label">Keterangan Tambahan</label>
                            <textarea class="form-control" id="keterangan" name="keterangan" rows="3"></textarea>
                            <small class="text-muted">Contoh: Rapat koordinasi, Pelatihan, dll.</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="jam_pulang" class="form-label">Jam Pulang</label>
                            <input type="time" class="form-control" id="jam_pulang" name="jam_pulang" value="<?php echo date('H:i'); ?>">
                            <small class="text-muted">Jam pulang yang akan dicatat untuk presensi ini.</small>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i> Dengan menandai presensi ini sebagai perjalanan dinas:
                            <ul class="mb-0 mt-2">
                                <li>Absen pulang akan otomatis diisi</li>
                                <li>Status presensi akan diubah menjadi "Tepat Waktu"</li>
                                <li>Izin perjalanan dinas akan otomatis dibuat</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-end">
                    <a href="monitoring.php?tanggal=<?php echo $tanggal; ?>" class="btn btn-secondary me-2">Batal</a>
                    <button type="button" id="btnConfirm" class="btn btn-primary">Simpan</button>
                    <button type="submit" id="btnSubmit" class="d-none">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Konfirmasi sebelum menyimpan
    document.getElementById('btnConfirm').addEventListener('click', function() {
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menandai presensi ini sebagai perjalanan dinas?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Tandai',
            cancelButtonText: 'Batal',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                document.getElementById('btnSubmit').click();
            }
        });
    });
});
</script>
