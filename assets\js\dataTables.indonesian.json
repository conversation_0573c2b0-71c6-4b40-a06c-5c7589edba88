{"emptyTable": "Tidak ada data yang tersedia pada tabel ini", "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri", "infoEmpty": "Menampilkan 0 sampai 0 dari 0 entri", "infoFiltered": "(disaring dari _MA<PERSON>_ entri <PERSON>)", "infoThousands": ".", "lengthMenu": "<PERSON><PERSON><PERSON><PERSON> _MENU_ entri", "loadingRecords": "Sedang memuat...", "processing": "Sedang memproses...", "search": "Cari:", "zeroRecords": "Tidak ditemukan data yang sesuai", "thousands": ".", "paginate": {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "next": "Selanjutnya", "previous": "Sebelumnya"}, "aria": {"sortAscending": ": aktifkan untuk mengurutkan kolom ke atas", "sortDescending": ": aktifkan untuk mengurutkan kolom ke bawah"}, "autoFill": {"cancel": "Batalkan", "fill": "<PERSON>i semua sel dengan <i>%d</i>", "fillHorizontal": "<PERSON>i sel secara horizontal", "fillVertical": "<PERSON>i sel secara vertikal"}, "buttons": {"collection": "Kumpulan <span class='ui-button-icon-primary ui-icon ui-icon-triangle-1-s'></span>", "colvis": "Visibilitas Kolom", "colvisRestore": "Kembalikan visibilitas", "copy": "<PERSON><PERSON>", "copySuccess": {"1": "1 baris disalin ke papan klip", "_": "%d baris disalin ke papan klip"}, "copyTitle": "<PERSON><PERSON> ke Papan klip", "csv": "CSV", "excel": "Excel", "pageLength": {"-1": "<PERSON><PERSON><PERSON><PERSON> semua baris", "_": "Tampilkan %d baris"}, "pdf": "PDF", "print": "Cetak", "copyKeys": "Tekan ctrl atau u2318 + C untuk menyalin tabel ke papan klip.<br><br>Untuk membatalkan, klik pesan ini atau tekan esc."}, "searchBuilder": {"add": "Tambah Kondisi", "button": {"0": "Cari Builder", "_": "Cari Builder (%d)"}, "clearAll": "<PERSON><PERSON><PERSON><PERSON>", "condition": "<PERSON><PERSON><PERSON>", "data": "Data", "deleteTitle": "Hapus filter", "leftTitle": "<PERSON>", "logicAnd": "<PERSON>", "logicOr": "<PERSON><PERSON>", "rightTitle": "<PERSON>", "title": {"0": "Cari Builder", "_": "Cari Builder (%d)"}, "value": "<PERSON><PERSON>", "conditions": {"date": {"after": "Setela<PERSON>", "before": "Sebelum", "between": "Diantara", "empty": "Kosong", "equals": "<PERSON><PERSON> den<PERSON>", "not": "Tidak sama", "notBetween": "Tidak diantara", "notEmpty": "Tidak kosong"}, "number": {"between": "Diantara", "empty": "Kosong", "equals": "<PERSON><PERSON> den<PERSON>", "gt": "<PERSON><PERSON><PERSON> besar dari", "gte": "<PERSON><PERSON><PERSON> besar atau sama dengan", "lt": "<PERSON><PERSON><PERSON> kecil dari", "lte": "<PERSON><PERSON><PERSON> kecil atau sama dengan", "not": "Tidak sama", "notBetween": "Tidak diantara", "notEmpty": "Tidak kosong"}, "string": {"contains": "<PERSON><PERSON><PERSON>", "empty": "Kosong", "endsWith": "<PERSON><PERSON><PERSON><PERSON>", "equals": "<PERSON><PERSON>", "not": "Tidak sama", "notEmpty": "Tidak kosong", "startsWith": "<PERSON><PERSON><PERSON>"}, "array": {"equals": "<PERSON><PERSON> den<PERSON>", "empty": "Kosong", "contains": "<PERSON><PERSON><PERSON>", "not": "Tidak", "notEmpty": "Tidak kosong", "without": "<PERSON><PERSON>"}}}, "searchPanes": {"clearMessage": "<PERSON><PERSON><PERSON><PERSON>", "count": "{total}", "countFiltered": "{shown} ({total})", "title": "Filter Aktif - %d", "collapse": {"0": "<PERSON>ian", "_": "Panel Pencarian (%d)"}, "emptyPanes": "Tidak Ada Panel Pencarian", "loadMessage": "Memuat Panel Pencarian"}, "select": {"cells": {"1": "1 sel terpilih", "_": "%d sel terpilih"}, "columns": {"1": "1 kolom terpilih", "_": "%d kolom terpilih"}, "rows": {"1": "1 baris terpilih", "_": "%d baris terpilih"}}, "datetime": {"previous": "Sebelumnya", "next": "Selanjutnya", "hours": "Jam", "minutes": "Menit", "seconds": "<PERSON><PERSON>", "unknown": "-", "amPm": ["am", "pm"], "weekdays": ["Min", "<PERSON>", "<PERSON>l", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>b"], "months": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "April", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "September", "Oktober", "November", "Desember"]}, "editor": {"close": "<PERSON><PERSON><PERSON>", "create": {"button": "Tambah", "submit": "Tambah", "title": "Tambah data baru"}, "edit": {"button": "Ubah", "submit": "<PERSON><PERSON><PERSON>", "title": "Ubah data"}, "error": {"system": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han sistem"}, "multi": {"info": "Item yang dipilih berisi nilai yang berbeda untuk input ini. Untuk mengedit dan mengatur semua item untuk input ini ke nilai yang sama, klik atau ketuk di sini, jika tidak maka mereka akan mempertahankan nilai masing-masing.", "noMulti": "Input ini dapat diubah satu per satu, tetapi bukan sebagai bagian dari grup.", "restore": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> nilai"}, "remove": {"button": "Hapus", "confirm": {"_": "<PERSON><PERSON><PERSON><PERSON> Anda yakin untuk menghapus %d baris?", "1": "<PERSON><PERSON><PERSON><PERSON> Anda yakin untuk menghapus 1 baris?"}, "submit": "Hapus", "title": "Hapus data"}}}