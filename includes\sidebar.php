<?php
// Cek role
$role = $_SESSION['role'] ?? '';
?>

<div class="position-sticky pt-3">
    <div class="d-flex justify-content-between align-items-center mb-4 px-3">
        <h4 class="m-0"><?php echo APP_NAME; ?></h4>
        <button type="button" class="btn-close d-md-none text-white" id="sidebarClose" aria-label="Close"></button>
    </div>

        <ul class="nav flex-column">
            <?php if ($role == 'admin'): ?>
                <!-- Menu Admin -->
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>admin/index.php">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                </li>

                <!-- Dropdown Manajemen Karyawan -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="karyawanDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-users me-2"></i> Manajemen Karyawan
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="karyawanDropdown">
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/karyawan.php">
                                <i class="fas fa-user-tie me-2"></i> Data Karyawan
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/bidang.php">
                                <i class="fas fa-building me-2"></i> Bidang
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/deteksi_wajah.php">
                                <i class="fas fa-camera me-2"></i> Deteksi Wajah
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/pesan_peringatan.php">
                                <i class="fas fa-exclamation-triangle me-2"></i> Pesan Peringatan
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Dropdown Presensi -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="presensiDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-clipboard-list me-2"></i> Presensi
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="presensiDropdown">
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/monitoring.php">
                                <i class="fas fa-desktop me-2"></i> Monitoring Presensi
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/rapat.php">
                                <i class="fas fa-users me-2"></i> Rapat
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/absensi_offline.php">
                                <i class="fas fa-clipboard-check me-2"></i> Absensi Offline
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/gangguan_absensi.php">
                                <i class="fas fa-exclamation-triangle me-2"></i> Gangguan Absensi
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/izin_dinas.php">
                                <i class="fas fa-plane me-2"></i> Izin Dinas
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Dropdown Jadwal -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="jadwalDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-calendar-alt me-2"></i> Jadwal
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="jadwalDropdown">
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/jam_kerja.php">
                                <i class="fas fa-clock me-2"></i> Jam Kerja
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/hari_kerja.php">
                                <i class="fas fa-calendar-day me-2"></i> Hari Kerja
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/jam_kerja_bidang.php">
                                <i class="fas fa-calendar-week me-2"></i> Jam Kerja Bidang
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/hari_libur.php">
                                <i class="fas fa-calendar-times me-2"></i> Hari Libur
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Dropdown Aktivitas Karyawan -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="aktivitasDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-map-marker-alt me-2"></i> Aktivitas Karyawan
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="aktivitasDropdown">
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/aktivitas_karyawan.php">
                                <i class="fas fa-list me-2"></i> Data Aktivitas
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/laporan_aktivitas.php">
                                <i class="fas fa-chart-bar me-2"></i> Laporan Statistik
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/export_aktivitas.php">
                                <i class="fas fa-file-export me-2"></i> Export Data
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Dropdown Keamanan -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="keamananDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-shield-alt me-2"></i> Keamanan
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="keamananDropdown">
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/blokir_device.php">
                                <i class="fas fa-ban me-2"></i> Blokir Device
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/manage_sessions.php">
                                <i class="fas fa-key me-2"></i> Manajemen Sesi Login
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Dropdown Konfigurasi -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="konfigurasiDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-cogs me-2"></i> Konfigurasi
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="konfigurasiDropdown">
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/lokasi.php">
                                <i class="fas fa-map-marker-alt me-2"></i> Lokasi
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/barcode_config.php">
                                <i class="fas fa-qrcode me-2"></i> Barcode
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/denda.php">
                                <i class="fas fa-money-bill-alt me-2"></i> Denda
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/fix_presensi_table.php">
                                <i class="fas fa-database me-2"></i> Perbaiki Tabel Presensi
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/create_tables.php">
                                <i class="fas fa-table me-2"></i> Buat Tabel Database
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Laporan -->
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>admin/laporan.php">
                        <i class="fas fa-file-alt me-2"></i> Laporan
                    </a>
                </li>
            <?php else: ?>
                <!-- Menu Karyawan -->
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>karyawan/index.php">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>karyawan/presensi.php">
                        <i class="fas fa-clipboard-check me-2"></i> Presensi
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>karyawan/riwayat.php">
                        <i class="fas fa-history me-2"></i> Riwayat Presensi
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>karyawan/izin_dinas.php">
                        <i class="fas fa-plane me-2"></i> Izin Dinas
                    </a>
                </li>
            <?php endif; ?>
        </ul>

        <hr>

        <ul class="nav flex-column">
            <?php if ($role == 'admin'): ?>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo BASE_URL; ?>admin/profile.php">
                    <i class="fas fa-user-circle me-2"></i> Profil Admin
                </a>
            </li>
            <?php endif; ?>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo BASE_URL; ?>logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i> Logout
                </a>
            </li>
        </ul>
    </div>
</div>
