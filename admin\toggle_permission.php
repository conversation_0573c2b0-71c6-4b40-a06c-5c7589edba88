<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil parameter
$id = isset($_GET['id']) ? clean($_GET['id']) : null;
$type = isset($_GET['type']) ? clean($_GET['type']) : null;
$value = isset($_GET['value']) ? (int)$_GET['value'] : 0;

// Validasi parameter
if (!$id || !$type || !in_array($type, ['barcode', 'face', 'flexible_schedule']) || !in_array($value, [0, 1])) {
    setMessage('danger', 'Parameter tidak valid!');
    redirect('admin/karyawan.php');
}

// Cek apakah karyawan ada
$query = "SELECT * FROM users WHERE id = '$id' AND role = 'karyawan'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Karyawan tidak ditemukan!');
    redirect('admin/karyawan.php');
}

// Update perizinan
if ($type == 'barcode') {
    $column = 'allow_barcode';
    $permission_type = 'absensi barcode';
} elseif ($type == 'face') {
    $column = 'allow_face';
    $permission_type = 'absensi wajah';
} elseif ($type == 'flexible_schedule') {
    $column = 'allow_flexible_schedule';
    $permission_type = 'jam kerja fleksibel';
}

$query = "UPDATE users SET $column = '$value' WHERE id = '$id'";

if (mysqli_query($conn, $query)) {
    $status = $value == 1 ? 'diaktifkan' : 'dinonaktifkan';
    setMessage('success', "Perizinan $permission_type berhasil $status!");
} else {
    setMessage('danger', 'Gagal mengubah perizinan: ' . mysqli_error($conn));
}

// Redirect ke halaman karyawan
redirect('admin/karyawan.php');
?>
