<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Inisialisasi variabel
$tanggal = isset($_GET['tanggal']) ? $_GET['tanggal'] : (isset($_POST['tanggal']) ? $_POST['tanggal'] : date('Y-m-d'));
$jam_masuk = isset($_POST['jam_masuk']) ? $_POST['jam_masuk'] : '08:00';
$jam_pulang = isset($_POST['jam_pulang']) ? $_POST['jam_pulang'] : '17:00';
$status = isset($_POST['status']) ? $_POST['status'] : 'Tepat Waktu';
$keterangan = isset($_POST['keterangan']) ? $_POST['keterangan'] : 'Absensi manual oleh admin';
$selected_karyawan = isset($_POST['karyawan']) ? $_POST['karyawan'] : [];

// Ambil semua data karyawan
$query = "SELECT id, nik, nama, bidang FROM users WHERE role = 'karyawan' ORDER BY nama ASC";
$result = mysqli_query($conn, $query);
$karyawan_list = [];
while ($row = mysqli_fetch_assoc($result)) {
    $karyawan_list[] = $row;
}

// Cek karyawan yang sudah absen pada tanggal tersebut
$query = "SELECT user_id FROM presensi WHERE tanggal = '$tanggal'";
$result = mysqli_query($conn, $query);
$karyawan_sudah_absen = [];
while ($row = mysqli_fetch_assoc($result)) {
    $karyawan_sudah_absen[] = $row['user_id'];
}

// Proses form absensi manual
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['submit'])) {
    // Validasi input
    if (empty($tanggal) || empty($jam_masuk) || empty($status) || empty($selected_karyawan)) {
        setMessage('danger', 'Semua field wajib diisi!');
    } else {
        // Cek apakah file gambar dummy ada
        if (!file_exists('../uploads/manual_absensi.jpg')) {
            // Jika tidak ada, buat pesan error
            setMessage('danger', 'File gambar dummy untuk absensi manual tidak ditemukan. Silakan hubungi administrator.');
            redirect('admin/absensi_manual.php');
            exit;
        }

        $success_count = 0;
        $error_count = 0;

        // Loop untuk setiap karyawan yang dipilih
        foreach ($selected_karyawan as $karyawan_id) {
            // Cek apakah karyawan sudah absen pada tanggal tersebut
            $query = "SELECT * FROM presensi WHERE user_id = '$karyawan_id' AND tanggal = '$tanggal'";
            $result = mysqli_query($conn, $query);

            if (mysqli_num_rows($result) > 0) {
                // Jika sudah ada, update data
                $presensi = mysqli_fetch_assoc($result);
                $presensi_id = $presensi['id'];

                $query = "UPDATE presensi SET
                          jam_masuk = '$jam_masuk',
                          jam_pulang = " . (!empty($jam_pulang) ? "'$jam_pulang'" : "NULL") . ",
                          status = '$status',
                          keterangan = '$keterangan'
                          WHERE id = '$presensi_id'";
            } else {
                // Jika belum ada, insert data baru
                $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, jam_pulang, foto_masuk, lokasi_masuk, status, keterangan)
                          VALUES ('$karyawan_id', '$tanggal', '$jam_masuk', " .
                          (!empty($jam_pulang) ? "'$jam_pulang'" : "NULL") .
                          ", 'manual_absensi.jpg', 'Absensi Manual', '$status', '$keterangan')";
            }

            if (mysqli_query($conn, $query)) {
                $success_count++;
            } else {
                $error_count++;
                // Log error untuk debugging
                error_log("Error absensi manual: " . mysqli_error($conn) . " Query: " . $query);
            }
        }

        if ($success_count > 0) {
            setMessage('success', "Berhasil mengatur absensi untuk $success_count karyawan" . ($error_count > 0 ? ", gagal untuk $error_count karyawan" : ""));
        } else {
            setMessage('danger', "Gagal mengatur absensi untuk semua karyawan yang dipilih");
        }

        // Redirect ke halaman monitoring dengan tanggal yang dipilih
        redirect('admin/monitoring.php?tanggal=' . $tanggal);
    }
}

// Include header
include_once '../includes/header.php';

// Ambil pesan jika ada
$message = getMessage();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Absensi Manual</h1>
        <a href="monitoring.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if ($message): ?>
    <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $message['text']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> Fitur ini memungkinkan admin untuk menambahkan atau mengubah data absensi karyawan secara manual. Pilih tanggal, jam, dan karyawan yang ingin diabsenkan.
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Form Absensi Manual</h6>
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="tanggal">Tanggal Absensi</label>
                            <input type="date" class="form-control" id="tanggal" name="tanggal" value="<?php echo $tanggal; ?>" required>
                            <small class="form-text text-muted">Perubahan tanggal akan memperbarui data karyawan yang sudah absen</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="jam_masuk">Jam Masuk</label>
                            <input type="time" class="form-control" id="jam_masuk" name="jam_masuk" value="<?php echo $jam_masuk; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="jam_pulang">Jam Pulang (Opsional)</label>
                            <input type="time" class="form-control" id="jam_pulang" name="jam_pulang" value="<?php echo $jam_pulang; ?>">
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="Tepat Waktu" <?php echo $status == 'Tepat Waktu' ? 'selected' : ''; ?>>Tepat Waktu</option>
                                <option value="Terlambat" <?php echo $status == 'Terlambat' ? 'selected' : ''; ?>>Terlambat</option>
                                <option value="Pulang Awal" <?php echo $status == 'Pulang Awal' ? 'selected' : ''; ?>>Pulang Awal</option>
                                <option value="Lembur" <?php echo $status == 'Lembur' ? 'selected' : ''; ?>>Lembur</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="keterangan">Keterangan</label>
                            <input type="text" class="form-control" id="keterangan" name="keterangan" value="<?php echo $keterangan; ?>" required>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label>Pilih Karyawan</label>
                    <div class="d-flex justify-content-between mb-2">
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="selectAll">Pilih Semua</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAll">Batalkan Semua</button>
                        </div>
                        <div class="input-group" style="max-width: 300px;">
                            <input type="text" class="form-control form-control-sm" id="searchKaryawan" placeholder="Cari karyawan...">
                            <button class="btn btn-sm btn-outline-secondary" type="button" id="clearSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th width="50">Pilih</th>
                                    <th>NIK</th>
                                    <th>Nama</th>
                                    <th>Bidang</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($karyawan_list)): ?>
                                    <tr>
                                        <td colspan="4" class="text-center">Tidak ada data karyawan</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($karyawan_list as $k): ?>
                                        <tr>
                                            <td class="text-center">
                                                <div class="form-check">
                                                    <input class="form-check-input karyawan-checkbox" type="checkbox" name="karyawan[]" value="<?php echo $k['id']; ?>" id="karyawan_<?php echo $k['id']; ?>">
                                                </div>
                                            </td>
                                            <td><?php echo $k['nik']; ?></td>
                                            <td>
                                                <?php echo $k['nama']; ?>
                                                <?php if (in_array($k['id'], $karyawan_sudah_absen)): ?>
                                                    <span class="badge bg-success">Sudah Absen</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $k['bidang'] ?? '-'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="text-center">
                    <button type="submit" name="submit" class="btn btn-primary" id="submitBtn" disabled>
                        <i class="fas fa-save"></i> Simpan Absensi
                    </button>
                    <div class="mt-2 text-danger" id="validationMessage">
                        Pilih minimal satu karyawan untuk melanjutkan
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const submitBtn = document.getElementById('submitBtn');
        const validationMessage = document.getElementById('validationMessage');
        const checkboxes = document.querySelectorAll('.karyawan-checkbox');

        // Fungsi untuk memilih/membatalkan semua checkbox
        document.getElementById('selectAll').addEventListener('click', function() {
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = true;
            });
            validateForm();
        });

        document.getElementById('deselectAll').addEventListener('click', function() {
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = false;
            });
            validateForm();
        });

        // Tambahkan event listener untuk setiap checkbox
        checkboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', validateForm);
        });

        // Fungsi untuk validasi form
        function validateForm() {
            // Cek apakah ada checkbox yang dicentang
            const isAnyChecked = Array.from(checkboxes).some(checkbox => checkbox.checked);

            // Aktifkan/nonaktifkan tombol submit
            submitBtn.disabled = !isAnyChecked;

            // Tampilkan/sembunyikan pesan validasi
            validationMessage.style.display = isAnyChecked ? 'none' : 'block';
        }

        // Validasi form saat halaman dimuat
        validateForm();

        // Fungsi pencarian karyawan
        const searchInput = document.getElementById('searchKaryawan');
        const clearSearchBtn = document.getElementById('clearSearch');
        const karyawanRows = document.querySelectorAll('tbody tr');

        // Fungsi untuk memperbarui halaman saat tanggal berubah
        const tanggalInput = document.getElementById('tanggal');
        tanggalInput.addEventListener('change', function() {
            // Buat form untuk submit
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = window.location.href;

            // Tambahkan input tanggal
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'tanggal';
            input.value = this.value;

            // Tambahkan input ke form dan submit
            form.appendChild(input);
            document.body.appendChild(form);
            form.submit();
        });

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterKaryawan(searchTerm);
        });

        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            filterKaryawan('');
        });

        function filterKaryawan(searchTerm) {
            karyawanRows.forEach(function(row) {
                const nik = row.cells[1].textContent.toLowerCase();
                const nama = row.cells[2].textContent.toLowerCase();
                const bidang = row.cells[3].textContent.toLowerCase();

                if (nik.includes(searchTerm) || nama.includes(searchTerm) || bidang.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
    });
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
