<?php
// Include file konfigurasi
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/auth.php';

// Cek jika sudah login sebagai karyawan
if (isLoggedIn()) {
    if (isAdmin()) {
        redirect('admin/index.php');
    } else {
        redirect('karyawan/index.php');
    }
}

// Proses login
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nik = $_POST['nik'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($nik) || empty($password)) {
        setMessage('danger', 'NIK dan Password harus diisi!');
    } else {
        // Cek apakah device diblokir sebelum login
        $device_id = $_SESSION['device_id'] ?? ($_COOKIE['device_id'] ?? null);
        if (!$device_id) {
            $device_id = md5($_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']);
            // Simpan device_id di cookie selama 1 tahun
            setcookie('device_id', $device_id, time() + 60 * 60 * 24 * 365, '/');
        }

        // Cek apakah device diblokir
        $blocked = isDeviceBlocked($nik, $device_id);
        if ($blocked) {
            // Buat pesan error dengan alasan pemblokiran
            $pesan = "Device atau NIK Anda diblokir.<br>";
            $pesan .= "<strong>Alasan:</strong> " . $blocked['alasan'] . "<br>";
            $pesan .= "<strong>Tanggal Blokir:</strong> " . $blocked['tanggal_blokir'] . "<br>";
            $pesan .= "Silakan hubungi administrator untuk informasi lebih lanjut.";

            // Set pesan error
            setMessage('danger', $pesan);
        } else {
            // Cek apakah NIK sudah login di perangkat lain
            $logged_in_other_device = isNikLoggedInOtherDevice($nik, $device_id);
            if ($logged_in_other_device) {
                // Format waktu login dan last activity
                $login_time = date('d/m/Y H:i:s', strtotime($logged_in_other_device['login_time']));
                $last_activity = date('d/m/Y H:i:s', strtotime($logged_in_other_device['last_activity']));

                // Buat pesan error dengan informasi perangkat
                $pesan = "<strong>NIK ini sudah login di perangkat lain.</strong><br>";
                $pesan .= "Anda tidak dapat login sampai riwayat login sebelumnya dihapus oleh administrator.<br><br>";
                $pesan .= "<strong>Informasi Perangkat:</strong><br>";
                $pesan .= "- Login terakhir: " . $login_time . "<br>";
                $pesan .= "- Aktivitas terakhir: " . $last_activity . "<br>";
                $pesan .= "- IP Address: " . $logged_in_other_device['ip_address'] . "<br>";
                $pesan .= "<br>Silakan hubungi administrator untuk menghapus riwayat login Anda.";

                // Set pesan error
                setMessage('danger', $pesan);
            } else {
                // Set flag bahwa device sudah dicek
                $_SESSION['device_checked'] = true;
                // Tidak perlu set role, biarkan fungsi login mendeteksi role berdasarkan NIK
                if (login($nik, $password)) {
                    // Redirect dilakukan di fungsi login
                } else {
                    setMessage('danger', 'NIK atau Password salah!');
                }
            }
        }
    }
}

// Ambil pesan jika ada
$message = getMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Login Karyawan</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#6a11cb">
    <meta name="description" content="Aplikasi absensi karyawan dengan fitur selfie dan GPS">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="<?php echo APP_NAME; ?>">

    <!-- PWA Manifest -->
    <link rel="manifest" href="<?php echo BASE_URL; ?>manifest.json">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo BASE_URL; ?>assets/images/favicon.png">
    <link rel="apple-touch-icon" href="<?php echo BASE_URL; ?>assets/images/icon-192x192.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="<?php echo BASE_URL; ?>assets/css/android-login.css" rel="stylesheet">
</head>
<body>
    <!-- Particles Container -->
    <div class="particles-container"></div>

    <div class="android-login-container">
        <div class="android-login-card">
            <div class="android-login-header">
                <div class="android-login-logo">
                    <img src="<?php echo BASE_URL; ?>assets/img/logo_absen.png" alt="Logo" style="width: 60px; height: auto;">
                </div>
                <h3><?php echo APP_NAME; ?></h3>
                <p>Sistem Absensi Karyawan</p>
            </div>

            <div class="android-login-body">
                <?php if ($message): ?>
                    <div class="android-alert <?php echo $message['type']; ?>">
                        <?php echo $message['text']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form method="post" action="" id="android-login-form">
                    <div class="android-form-group">
                        <label for="nik">NIK Karyawan</label>
                        <div class="input-container">
                            <input type="text" class="android-form-control" id="nik" name="nik" required>
                            <div class="android-form-icon">
                                <i class="fas fa-id-card"></i>
                            </div>
                        </div>
                    </div>

                    <div class="android-form-group">
                        <label for="password">Password</label>
                        <div class="input-container">
                            <input type="password" class="android-form-control" id="password" name="password" required>
                            <button type="button" class="password-toggle" id="togglePassword" aria-label="Toggle password visibility">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="android-form-group remember-me">
                        <label class="remember-label">
                            <input type="checkbox" name="remember" id="remember" checked disabled>
                            <span class="checkmark"></span>
                            Selalu Ingat Saya
                        </label>
                        <input type="hidden" name="remember" value="1">
                    </div>

                    <button type="submit" class="android-btn">
                        <i class="fas fa-sign-in-alt me-2"></i> Masuk
                    </button>
                </form>

                <div class="android-login-footer">
                    <p class="mt-3 text-center">Aplikasi ini dibuat oleh Ikhlasul Amal</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Particles JS -->
    <script src="<?php echo BASE_URL; ?>assets/js/particles.js"></script>

    <!-- Password Toggle and Remember Me Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            const nikInput = document.getElementById('nik');
            const rememberCheckbox = document.getElementById('remember');
            const loginForm = document.getElementById('android-login-form');

            // Cek apakah ada NIK yang tersimpan di localStorage
            if (localStorage.getItem('rememberedNik')) {
                nikInput.value = localStorage.getItem('rememberedNik');
                rememberCheckbox.checked = true;
                // Fokus ke field password
                setTimeout(() => {
                    passwordInput.focus();
                }, 500);
            }

            // Toggle password visibility
            if (togglePassword && passwordInput) {
                togglePassword.addEventListener('click', function() {
                    // Toggle type attribute
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);

                    // Toggle icon
                    const icon = this.querySelector('i');
                    if (type === 'text') {
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                        this.classList.add('active');
                    } else {
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                        this.classList.remove('active');
                    }
                });
            }

            // Handle form submission
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    // Selalu simpan NIK di localStorage
                    localStorage.setItem('rememberedNik', nikInput.value);
                });
            }
        });
    </script>

    <!-- Service Worker Registration -->
    <script>
        // Cek apakah browser mendukung Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                // Tambahkan parameter versi untuk memastikan service worker diperbarui
                // Gunakan path absolut untuk memastikan service worker berfungsi di semua URL
                const swUrl = new URL('sw.js?v=4', window.location.origin).href;
                navigator.serviceWorker.register(swUrl)
                    .then(function(registration) {
                        console.log('Service Worker registered with scope:', registration.scope);

                        // Cek pembaruan service worker secara berkala
                        setInterval(function() {
                            registration.update();
                        }, 60 * 60 * 1000); // Cek pembaruan setiap 1 jam
                    })
                    .catch(function(error) {
                        console.log('Service Worker registration failed:', error);
                    });
            });
        }

        // Tampilkan pesan saat offline/online
        window.addEventListener('online', function() {
            if (document.querySelector('.connection-status')) {
                document.querySelector('.connection-status').remove();
            }

            const statusDiv = document.createElement('div');
            statusDiv.className = 'connection-status online';
            statusDiv.innerHTML = '<i class="fas fa-wifi"></i> Anda kembali online';
            document.body.appendChild(statusDiv);

            setTimeout(function() {
                statusDiv.classList.add('show');

                setTimeout(function() {
                    statusDiv.classList.remove('show');
                    setTimeout(function() {
                        statusDiv.remove();
                    }, 500);
                }, 3000);
            }, 100);

            // Reload halaman saat kembali online jika sebelumnya offline
            if (window.wasOffline) {
                window.wasOffline = false;
                // Tunda reload untuk memberikan waktu notifikasi ditampilkan
                setTimeout(function() {
                    window.location.reload();
                }, 1500);
            }
        });

        window.addEventListener('offline', function() {
            if (document.querySelector('.connection-status')) {
                document.querySelector('.connection-status').remove();
            }

            const statusDiv = document.createElement('div');
            statusDiv.className = 'connection-status offline';
            statusDiv.innerHTML = '<i class="fas fa-wifi-slash"></i> Anda sedang offline';
            document.body.appendChild(statusDiv);

            setTimeout(function() {
                statusDiv.classList.add('show');
            }, 100);

            // Tandai bahwa aplikasi sedang offline
            window.wasOffline = true;
        });
    </script>

    <style>
        .connection-status {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(-100px);
            padding: 12px 20px;
            border-radius: 50px;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            z-index: 9999;
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
        }

        .connection-status.show {
            transform: translateX(-50%) translateY(0);
        }

        .connection-status.online {
            background-color: #1cc88a;
        }

        .connection-status.offline {
            background-color: #e74a3b;
        }

        .connection-status i {
            margin-right: 8px;
        }

        /* Styling untuk pesan error login */
        .android-alert.danger {
            border-left: 4px solid #e74a3b;
            background-color: rgba(231, 74, 59, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.5;
        }

        .android-alert.danger strong {
            color: #e74a3b;
            font-weight: 600;
        }

        .android-alert.danger ul {
            margin-top: 10px;
            margin-bottom: 10px;
            padding-left: 20px;
        }

        .android-alert.danger li {
            margin-bottom: 5px;
        }
    </style>
</body>
</html>
