<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil data untuk dashboard
$total_karyawan = 0;
$total_presensi_hari_ini = 0;
$total_terlambat_hari_ini = 0;
$total_tidak_hadir = 0;

// Query untuk total karyawan
$query = "SELECT COUNT(*) as total FROM users WHERE role = 'karyawan'";
$result = mysqli_query($conn, $query);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    $total_karyawan = $row['total'];
}

// Tanggal hari ini
$today = date('Y-m-d');

// Query untuk total presensi hari ini
$query = "SELECT COUNT(*) as total FROM presensi WHERE tanggal = '$today'";
$result = mysqli_query($conn, $query);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    $total_presensi_hari_ini = $row['total'];
}

// Query untuk total terlambat hari ini
$query = "SELECT COUNT(*) as total FROM presensi WHERE tanggal = '$today' AND status = 'Terlambat'";
$result = mysqli_query($conn, $query);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    $total_terlambat_hari_ini = $row['total'];
}

// Query untuk total tidak hadir (karyawan - presensi hari ini)
$total_tidak_hadir = $total_karyawan - $total_presensi_hari_ini;
if ($total_tidak_hadir < 0) $total_tidak_hadir = 0;

// Query untuk presensi terbaru
$query = "SELECT p.*, u.nik, u.nama FROM presensi p 
          JOIN users u ON p.user_id = u.id 
          ORDER BY p.tanggal DESC, p.jam_masuk DESC 
          LIMIT 5";
$result = mysqli_query($conn, $query);
$presensi_terbaru = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $presensi_terbaru[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4">Dashboard</h1>
    
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Karyawan</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_karyawan; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Presensi Hari Ini</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_presensi_hari_ini; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Terlambat Hari Ini</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_terlambat_hari_ini; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Tidak Hadir</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_tidak_hadir; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-times fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Presensi Terbaru</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>NIK</th>
                                    <th>Nama</th>
                                    <th>Tanggal</th>
                                    <th>Jam Masuk</th>
                                    <th>Jam Pulang</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($presensi_terbaru)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center">Tidak ada data presensi</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($presensi_terbaru as $presensi): ?>
                                        <tr>
                                            <td><?php echo $presensi['nik']; ?></td>
                                            <td><?php echo $presensi['nama']; ?></td>
                                            <td><?php echo date('d-m-Y', strtotime($presensi['tanggal'])); ?></td>
                                            <td><?php echo $presensi['jam_masuk']; ?></td>
                                            <td><?php echo $presensi['jam_pulang'] ?? '-'; ?></td>
                                            <td>
                                                <?php if ($presensi['status'] == 'Tepat Waktu'): ?>
                                                    <span class="badge bg-success">Tepat Waktu</span>
                                                <?php elseif ($presensi['status'] == 'Terlambat'): ?>
                                                    <span class="badge bg-warning">Terlambat</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?php echo $presensi['status']; ?></span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="monitoring.php" class="btn btn-primary">Lihat Semua Presensi</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
