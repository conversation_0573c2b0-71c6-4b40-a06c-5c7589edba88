<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Aktifkan error reporting untuk debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Fungsi untuk menangani error dan men<PERSON>likan respons JSON
function handleError($message, $details = '') {
    header('Content-Type: application/json; charset=utf-8');
    $response = [
        'status' => 'error',
        'message' => $message
    ];

    if (!empty($details) && ini_get('display_errors')) {
        $response['details'] = $details;
    }

    echo json_encode($response);
    exit;
}

// Set error handler
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        handleError("Terjadi kesalahan pada server", "$errstr in $errfile on line $errline");
    }
    return false; // Lanjutkan ke error handler standar PHP
});

// Cek login
if (!isLoggedIn() || $_SESSION['role'] != 'karyawan') {
    redirect('../index.php');
}

// Ambil data user
$user_id = $_SESSION['user_id'];
$query = "SELECT u.*, l.nama_lokasi, l.latitude, l.longitude, l.radius
          FROM users u
          LEFT JOIN lokasi l ON u.lokasi_id = l.id
          WHERE u.id = '$user_id'";
$result = mysqli_query($conn, $query);
$user = mysqli_fetch_assoc($result);

// Ambil data lokasi
$lokasi = null;
if (!empty($user['lokasi_id'])) {
    $query = "SELECT * FROM lokasi WHERE id = '{$user['lokasi_id']}'";
    $result = mysqli_query($conn, $query);
    $lokasi = mysqli_fetch_assoc($result);
}

// Ambil data barcode untuk lokasi user
$query = "SELECT bc.*, l.nama_lokasi, l.latitude, l.longitude
          FROM barcode_config bc
          JOIN lokasi l ON bc.lokasi_id = l.id
          WHERE bc.lokasi_id = '{$user['lokasi_id']}' AND bc.is_active = 1";
$result = mysqli_query($conn, $query);
$barcode_config = mysqli_fetch_assoc($result);

// Cek apakah ada konfigurasi barcode untuk lokasi user
$has_barcode = ($result && mysqli_num_rows($result) > 0);

// Cek apakah user diizinkan menggunakan absensi barcode
$allow_barcode = isset($user['allow_barcode']) && $user['allow_barcode'] == 1;
$allow_flexible_schedule = isset($user['allow_flexible_schedule']) && $user['allow_flexible_schedule'] == 1;

// Ambil data presensi hari ini
$today = date('Y-m-d');
$query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result = mysqli_query($conn, $query);
$presensi_hari_ini = mysqli_fetch_assoc($result);

// Ambil parameter type dari URL (masuk/pulang)
$jenis_absen = isset($_GET['type']) ? clean($_GET['type']) : 'masuk';
if (!in_array($jenis_absen, ['masuk', 'pulang'])) {
    $jenis_absen = 'masuk';
}

// Cek apakah karyawan sedang dalam perjalanan dinas
$query = "SELECT * FROM izin_dinas
          WHERE user_id = '$user_id'
          AND status = 'Approved'
          AND tanggal_mulai <= '$today'
          AND tanggal_selesai >= '$today'";
$result = mysqli_query($conn, $query);
$izin_dinas_aktif = mysqli_fetch_assoc($result);

// Cek apakah hari ini hari libur
$query = "SELECT * FROM hari_libur WHERE tanggal = '$today'";
$result = mysqli_query($conn, $query);
$hari_libur = mysqli_fetch_assoc($result);

// Ambil jam kerja berdasarkan bidang dan hari
$hari_map = [
    'Monday' => 'Senin',
    'Tuesday' => 'Selasa',
    'Wednesday' => 'Rabu',
    'Thursday' => 'Kamis',
    'Friday' => 'Jumat',
    'Saturday' => 'Sabtu',
    'Sunday' => 'Minggu'
];
$hari_en = date('l'); // Mendapatkan nama hari dalam bahasa Inggris
$hari_name = $hari_map[$hari_en]; // Konversi ke bahasa Indonesia

// Ambil jam kerja berdasarkan apakah user memiliki izin jam kerja fleksibel
$jam_kerja = null;
$selected_schedule = null;
$jam_kerja_options = [];

if ($allow_flexible_schedule) {
    // Cek apakah karyawan sudah memilih jam kerja untuk hari ini
    $query = "SELECT usc.*, jk.*
              FROM user_schedule_choices usc
              JOIN jam_kerja jk ON usc.jam_kerja_id = jk.id
              WHERE usc.user_id = '$user_id' AND usc.tanggal = '$today'";
    $result = mysqli_query($conn, $query);
    if ($result && mysqli_num_rows($result) > 0) {
        $selected_schedule = mysqli_fetch_assoc($result);
        $jam_kerja = $selected_schedule;
    }

    // Ambil semua jam kerja yang tersedia untuk bidang ini
    if ($user['bidang_id']) {
        $query = "SELECT DISTINCT jk.*
                  FROM jam_kerja jk
                  JOIN jam_kerja_bidang jkb ON jk.id = jkb.jam_kerja_id
                  WHERE jkb.bidang_id = '{$user['bidang_id']}'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $jam_kerja_options[] = $row;
            }
        }
    }
} else {
    // Gunakan fungsi getJamKerjaByUserId dari functions.php
    $jam_kerja = getJamKerjaByUserId($user_id);

    // Log untuk debugging
    error_log("Hari ini: $hari_en ($hari_name)");
    error_log("Jam kerja dari fungsi: " . ($jam_kerja ? json_encode($jam_kerja) : "tidak ditemukan"));

    // Jika tidak ada jam kerja dari fungsi, coba query langsung
    if (!$jam_kerja) {
        $query = "SELECT jkb.*, jk.awal_jam_masuk, jk.jam_masuk, jk.akhir_jam_masuk, jk.jam_pulang, jk.akhir_jam_pulang
                  FROM jam_kerja_bidang jkb
                  JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
                  WHERE jkb.bidang_id = '{$user['bidang_id']}' AND jkb.hari = '$hari_name'";
        $result = mysqli_query($conn, $query);
        $jam_kerja = mysqli_fetch_assoc($result);

        error_log("Query langsung: $query");
        error_log("Hasil query langsung: " . ($jam_kerja ? json_encode($jam_kerja) : "tidak ditemukan"));
    }
}

$sudah_absen_masuk = ($presensi_hari_ini && !empty($presensi_hari_ini['jam_masuk']));
$sudah_absen_pulang = ($presensi_hari_ini && !empty($presensi_hari_ini['jam_pulang']));

// Proses pilihan jam kerja fleksibel
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['pilih_jam_kerja'])) {
    $jam_kerja_id = clean($_POST['jam_kerja_id']);

    // Validasi jam kerja ID
    $query = "SELECT jk.*
              FROM jam_kerja jk
              JOIN jam_kerja_bidang jkb ON jk.id = jkb.jam_kerja_id
              WHERE jk.id = '$jam_kerja_id' AND jkb.bidang_id = '{$user['bidang_id']}'";
    $result = mysqli_query($conn, $query);

    if (!$result || mysqli_num_rows($result) == 0) {
        $_SESSION['error_message'] = 'Jam kerja tidak valid atau tidak tersedia untuk bidang Anda!';
        redirect('scan_barcode.php');
    }

    $jam_kerja_data = mysqli_fetch_assoc($result);

    // Cek apakah sudah ada pilihan untuk hari ini
    $query = "SELECT * FROM user_schedule_choices WHERE user_id = '$user_id' AND tanggal = '$today'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        // Update pilihan yang sudah ada
        $query = "UPDATE user_schedule_choices
                  SET jam_kerja_id = '$jam_kerja_id', updated_at = NOW()
                  WHERE user_id = '$user_id' AND tanggal = '$today'";
    } else {
        // Insert pilihan baru
        $query = "INSERT INTO user_schedule_choices (user_id, jam_kerja_id, tanggal, created_at)
                  VALUES ('$user_id', '$jam_kerja_id', '$today', NOW())";
    }

    if (mysqli_query($conn, $query)) {
        $_SESSION['success_message'] = 'Jam kerja berhasil dipilih: ' . $jam_kerja_data['nama_jam_kerja'];
        redirect('scan_barcode.php');
    } else {
        $_SESSION['error_message'] = 'Gagal menyimpan pilihan jam kerja: ' . mysqli_error($conn);
        redirect('scan_barcode.php');
    }
}

// Proses absensi dengan barcode
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['scan_result'])) {
    // Set header untuk respons JSON
    header('Content-Type: application/json; charset=utf-8');
    // Cek apakah hari ini hari libur
    if ($hari_libur) {
        $response = [
            'status' => 'error',
            'message' => 'Hari ini adalah hari libur: ' . $hari_libur['nama_libur']
        ];
        echo json_encode($response);
        exit;
    }

    // Cek apakah karyawan sedang dalam perjalanan dinas
    if ($izin_dinas_aktif) {
        $response = [
            'status' => 'error',
            'message' => 'Anda sedang dalam perjalanan dinas. Absensi otomatis tercatat.'
        ];
        echo json_encode($response);
        exit;
    }

    // Cek apakah jam kerja sudah diatur
    if (!$jam_kerja) {
        $response = [
            'status' => 'error',
            'message' => 'Jam kerja belum diatur untuk bidang Anda. Silakan hubungi administrator.'
        ];
        echo json_encode($response);
        exit;
    }

    // Validasi dan sanitasi input
    $scan_result = isset($_POST['scan_result']) ? clean($_POST['scan_result']) : '';
    $latitude = isset($_POST['latitude']) ? clean($_POST['latitude']) : 0;
    $longitude = isset($_POST['longitude']) ? clean($_POST['longitude']) : 0;
    $accuracy = isset($_POST['accuracy']) ? clean($_POST['accuracy']) : 0;
    $jenis_absen = isset($_POST['jenis_absen']) ? clean($_POST['jenis_absen']) : 'masuk';
    $jam_sekarang = date('H:i:s');

    // Log data untuk debugging
    error_log("Scan data received: " . json_encode([
        'scan_result' => $scan_result,
        'latitude' => $latitude,
        'longitude' => $longitude,
        'accuracy' => $accuracy,
        'jenis_absen' => $jenis_absen
    ]));

    // Validasi hasil scan
    if (empty($scan_result)) {
        $response = [
            'status' => 'error',
            'message' => 'Hasil scan barcode kosong!'
        ];
        echo json_encode($response);
        exit;
    }

    // Validasi izin barcode
    if (!$allow_barcode) {
        $response = [
            'status' => 'error',
            'message' => 'Anda tidak diizinkan menggunakan fitur absensi barcode. Silakan hubungi administrator.'
        ];
        echo json_encode($response);
        exit;
    }

    // Validasi lokasi
    if (empty($latitude) || empty($longitude)) {
        $response = [
            'status' => 'error',
            'message' => 'Lokasi tidak terdeteksi! Pastikan GPS aktif dan izin lokasi diberikan.'
        ];
        echo json_encode($response);
        exit;
    }

    // Cek apakah barcode valid
    try {
        // Sanitasi input untuk query
        $scan_result_safe = mysqli_real_escape_string($conn, $scan_result);

        $query = "SELECT bc.*, l.nama_lokasi, l.latitude, l.longitude, l.radius
                FROM barcode_config bc
                JOIN lokasi l ON bc.lokasi_id = l.id
                WHERE bc.barcode_value = '$scan_result_safe' AND bc.is_active = 1";

        $result = mysqli_query($conn, $query);

        if (!$result) {
            throw new Exception("Database error: " . mysqli_error($conn));
        }

        if (mysqli_num_rows($result) == 0) {
            $response = [
                'status' => 'error',
                'message' => 'Barcode tidak valid atau tidak aktif!'
            ];
            echo json_encode($response);
            exit;
        }

        $barcode = mysqli_fetch_assoc($result);

        // Log barcode data untuk debugging
        error_log("Barcode data found: " . json_encode($barcode));
    } catch (Exception $e) {
        $response = [
            'status' => 'error',
            'message' => 'Terjadi kesalahan saat memvalidasi barcode: ' . $e->getMessage()
        ];
        echo json_encode($response);
        exit;
    }

    // Hitung jarak antara lokasi user dan lokasi barcode
    try {
        // Pastikan semua nilai adalah angka yang valid
        $lat1 = floatval($latitude);
        $lon1 = floatval($longitude);
        $lat2 = floatval($barcode['latitude']);
        $lon2 = floatval($barcode['longitude']);

        // Cek apakah nilai valid
        if (!is_numeric($lat1) || !is_numeric($lon1) || !is_numeric($lat2) || !is_numeric($lon2)) {
            throw new Exception("Koordinat lokasi tidak valid");
        }

        $jarak = hitungJarak($lat1, $lon1, $lat2, $lon2);

        // Log jarak untuk debugging
        error_log("Jarak dihitung: $jarak meter");
    } catch (Exception $e) {
        $response = [
            'status' => 'error',
            'message' => 'Terjadi kesalahan saat menghitung jarak: ' . $e->getMessage()
        ];
        echo json_encode($response);
        exit;
    }

    // Validasi radius
    if ($jarak > $barcode['radius']) {
        $response = [
            'status' => 'error',
            'message' => 'Anda berada di luar radius yang diizinkan! Jarak Anda: ' . round($jarak) . ' meter, Radius: ' . $barcode['radius'] . ' meter'
        ];
        echo json_encode($response);
        exit;
    }

    // Proses absensi
    if ($jenis_absen == 'masuk') {
        // Cek apakah sudah absen masuk
        if ($sudah_absen_masuk) {
            $response = [
                'status' => 'error',
                'message' => 'Anda sudah melakukan absensi masuk hari ini!'
            ];
            echo json_encode($response);
            exit;
        }

        // Cek apakah sudah melewati jam akhir masuk
        if ($jam_sekarang < $jam_kerja['awal_jam_masuk']) {
            $response = [
                'status' => 'error',
                'message' => 'Belum waktunya absen masuk! Waktu absen masuk dimulai pukul ' . $jam_kerja['awal_jam_masuk']
            ];
            echo json_encode($response);
            exit;
        }

        if ($jam_sekarang > $jam_kerja['akhir_jam_masuk']) {
            $response = [
                'status' => 'error',
                'message' => 'Waktu absen masuk telah berakhir! Batas akhir absen masuk pukul ' . $jam_kerja['akhir_jam_masuk']
            ];
            echo json_encode($response);
            exit;
        }

        // Tentukan status berdasarkan waktu
        $status = 'Tepat Waktu';
        if ($jam_sekarang > $jam_kerja['jam_masuk']) {
            $status = 'Terlambat';
        }

        // Buat lokasi masuk dalam format string
        $lokasi_masuk = "Barcode: {$barcode['nama_lokasi']}, Lat: $latitude, Long: $longitude, Akurasi: $accuracy m";

        // Simpan data presensi
        $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, status, keterangan)
                  VALUES ('$user_id', '$today', '$jam_sekarang', NULL, '$lokasi_masuk', '$status', 'Absensi dengan barcode')";

        if (mysqli_query($conn, $query)) {
            $response = [
                'status' => 'success',
                'message' => 'Absensi masuk berhasil! Status: ' . $status
            ];
            echo json_encode($response);
            exit;
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Gagal menyimpan data absensi: ' . mysqli_error($conn)
            ];
            echo json_encode($response);
            exit;
        }
    } else if ($jenis_absen == 'pulang') {
        // Cek apakah sudah absen masuk
        if (!$sudah_absen_masuk) {
            $response = [
                'status' => 'error',
                'message' => 'Anda belum melakukan absensi masuk hari ini!'
            ];
            echo json_encode($response);
            exit;
        }

        // Cek apakah sudah absen pulang
        if ($sudah_absen_pulang) {
            $response = [
                'status' => 'error',
                'message' => 'Anda sudah melakukan absensi pulang hari ini!'
            ];
            echo json_encode($response);
            exit;
        }

        // Cek apakah sudah melewati jam akhir pulang
        if ($jam_sekarang > $jam_kerja['akhir_jam_pulang']) {
            $response = [
                'status' => 'error',
                'message' => 'Anda tidak dapat melakukan absensi pulang karena sudah melewati jam akhir pulang (' . $jam_kerja['akhir_jam_pulang'] . ').'
            ];
            echo json_encode($response);
            exit;
        }

        // Tentukan status berdasarkan waktu
        $status = $presensi_hari_ini['status']; // Pertahankan status sebelumnya
        if ($jam_sekarang < $jam_kerja['jam_pulang'] && $status != 'Terlambat') {
            $status = 'Pulang Awal';
        }

        if ($jam_sekarang > $jam_kerja['akhir_jam_pulang']) {
            $status = 'Lembur';
        }

        // Buat lokasi pulang dalam format string
        $lokasi_pulang = "Barcode: {$barcode['nama_lokasi']}, Lat: $latitude, Long: $longitude, Akurasi: $accuracy m";

        // Update data absensi
        $query = "UPDATE presensi SET jam_pulang = '$jam_sekarang', foto_pulang = NULL, lokasi_pulang = '$lokasi_pulang', status = '$status' WHERE id = '{$presensi_hari_ini['id']}'";

        if (mysqli_query($conn, $query)) {
            $response = [
                'status' => 'success',
                'message' => 'Absensi pulang berhasil! Status: ' . $status
            ];
            echo json_encode($response);
            exit;
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Gagal menyimpan data absensi: ' . mysqli_error($conn)
            ];
            echo json_encode($response);
            exit;
        }
    }

    // Jika sampai di sini, berarti ada kesalahan
    $response = [
        'status' => 'error',
        'message' => 'Terjadi kesalahan yang tidak diketahui!'
    ];
    echo json_encode($response);
    exit;
}

// Jika ini adalah request AJAX, hentikan eksekusi di sini
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
    exit;
}

// Fungsi hitungJarak() sudah ada di includes/functions.php

// Include header
include_once '../includes/header.php';

// Cek apakah ada pesan error atau success dari process_barcode.php
$error_message = isset($_SESSION['error_message']) ? $_SESSION['error_message'] : '';
$success_message = isset($_SESSION['success_message']) ? $_SESSION['success_message'] : '';

// Hapus pesan dari session setelah diambil
unset($_SESSION['error_message']);
unset($_SESSION['success_message']);
?>

<!-- CSS sudah dipindahkan ke file mobile-scan-barcode.css -->

<div class="mobile-profile-container">
    <!-- Header Section -->
    <div class="profile-header">
        <div class="user-info">
            <div class="user-avatar">
                <?php if (!empty($user['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $user['foto_profil']; ?>" alt="Foto Profil" style="width: 100px; height: 100px; ">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo $user['nama']; ?></div>
                <div class="user-position"><?php echo $user['bidang'] ?? 'Karyawan'; ?></div>
            </div>
        </div>
        <div class="date-info">
            <div><i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?></div>
            <div><i class="fas fa-map-marker-alt"></i> <?php echo $user['nama_lokasi'] ? $user['nama_lokasi'] : 'Belum diatur'; ?></div>
        </div>
    </div>

    <!-- Clock Section -->
    <div class="clock-container">
        <div class="clock" id="jam-sekarang"><?php echo date('H:i:s'); ?></div>
        <div class="date"><?php echo date('d F Y'); ?></div>
    </div>

    <div class="scan-container">
        <div class="section-title">
            <h4><i class="fas fa-qrcode me-2"></i> Scan Barcode Absensi</h4>
            <p class="text-muted">Pindai barcode untuk melakukan absensi</p>
        </div>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>Error:</strong> <?php echo $error_message; ?>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Berhasil!</strong> <?php echo $success_message; ?>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (!$allow_barcode): ?>
            <div class="alert alert-warning">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle me-3 fa-2x"></i>
                    <div>
                        <h5 class="mb-1">Akses Ditolak</h5>
                        <p class="mb-0">Anda tidak diizinkan menggunakan fitur absensi barcode. Silakan hubungi administrator.</p>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <a href="presensi.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i> Kembali ke Presensi
                </a>
            </div>
        <?php elseif (!$has_barcode): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i> Tidak ada konfigurasi barcode untuk lokasi Anda.
            </div>
        <?php elseif ($allow_flexible_schedule && !$selected_schedule): ?>
            <div class="alert alert-warning">
                <div class="d-flex align-items-center">
                    <i class="fas fa-clock me-3 fa-2x"></i>
                    <div>
                        <h5 class="mb-1">Pilih Jam Kerja Terlebih Dahulu</h5>
                        <p class="mb-0">Anda perlu memilih jam kerja fleksibel sebelum melakukan absensi barcode.</p>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <a href="presensi.php" class="btn btn-primary">
                    <i class="fas fa-clock me-2"></i> Pilih Jam Kerja
                </a>
            </div>
        <?php elseif ($sudah_absen_masuk && $sudah_absen_pulang): ?>
            <div class="alert alert-secondary">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle me-3 fa-2x"></i>
                    <div>
                        <h5 class="mb-1">Absensi Hari Ini Selesai</h5>
                        <p class="mb-0">Anda telah melakukan absensi masuk dan pulang hari ini.</p>
                    </div>
                </div>
                <div class="mt-3">
                    <p><strong>Jam Masuk:</strong> <?php echo $presensi_hari_ini['jam_masuk']; ?></p>
                    <p class="mb-0"><strong>Jam Pulang:</strong> <?php echo $presensi_hari_ini['jam_pulang']; ?></p>
                </div>
            </div>
            <div class="text-center mt-3">
                <a href="presensi.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i> Kembali ke Presensi
                </a>
            </div>
        <?php else: ?>
            <div id="scanner" class="scanner-container">
                <div id="scanner-overlay" class="scanner-overlay">
                    <div class="spinner-border text-primary mb-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>Memuat kamera...</div>
                </div>
                <div class="scanner-guide"></div>
                <div class="scanner-laser"></div>
                <div id="preview" class="preview-container"></div>
            </div>

            <div class="scan-actions">
                <div class="scan-status">
                    <div class="scanning-indicator">
                        <div class="spinner-border text-primary spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span>Memindai...</span>
                    </div>

                    <button id="toggleCamera" class="btn btn-danger btn-sm ms-2" style="display: none;">
                        <i class="fas fa-stop me-1"></i> Berhenti
                    </button>
                </div>

                <div class="absen-status">
                    <?php if (!$sudah_absen_masuk): ?>
                        <span class="badge bg-success"><i class="fas fa-sign-in-alt me-1"></i> Absen Masuk</span>
                    <?php elseif (!$sudah_absen_pulang): ?>
                        <span class="badge bg-danger"><i class="fas fa-sign-out-alt me-1"></i> Absen Pulang</span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="scan-result" id="scanResult">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i> Hasil Scan</h5>
                </div>
                <div id="resultContent"></div>
            </div>

            <div class="location-info">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i> Lokasi Anda</h5>
                </div>

                <div id="locationStatus">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span>Mendapatkan lokasi Anda...</span>
                    </div>
                </div>

                <div id="locationDetails" class="mt-3" style="display: none;">
                    <div class="card bg-light mb-2">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-5 text-muted">Lokasi</div>
                                <div class="col-7 fw-bold"><?php echo $user['nama_lokasi']; ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="card bg-light mb-2">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-5 text-muted">Jarak</div>
                                <div class="col-7 fw-bold">
                                    <span id="distance">Menghitung...</span>
                                    <div id="radius-status" class="mt-1"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card bg-light">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-5 text-muted">Jam Kerja</div>
                                <div class="col-7 fw-bold">
                                    <?php if ($allow_flexible_schedule && $selected_schedule): ?>
                                        <div class="text-success mb-1">
                                            <i class="fas fa-check-circle"></i> <?php echo $selected_schedule['nama_jam_kerja']; ?>
                                        </div>
                                        <div>Masuk: <?php echo $jam_kerja['awal_jam_masuk']; ?> - <?php echo $jam_kerja['akhir_jam_masuk']; ?></div>
                                        <div>Pulang: <?php echo $jam_kerja['jam_pulang']; ?> - <?php echo $jam_kerja['akhir_jam_pulang']; ?></div>
                                    <?php elseif ($allow_flexible_schedule && !$selected_schedule): ?>
                                        <div class="text-warning">
                                            <i class="fas fa-clock"></i> Jam Kerja Fleksibel
                                        </div>
                                        <div class="small">Silakan pilih jam kerja di halaman presensi</div>
                                    <?php elseif ($jam_kerja): ?>
                                        <div>Masuk: <?php echo $jam_kerja['awal_jam_masuk']; ?> - <?php echo $jam_kerja['akhir_jam_masuk']; ?></div>
                                        <div>Pulang: <?php echo $jam_kerja['jam_pulang']; ?> - <?php echo $jam_kerja['akhir_jam_pulang']; ?></div>
                                    <?php else: ?>
                                        <span class="text-danger">Belum diatur</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Menyimpan data yang diperlukan tapi tidak ditampilkan -->
                    <div style="display: none;">
                        <span id="coordinates"></span>
                        <span id="accuracy"></span>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Floating button to go back to presensi page -->
<a href="index.php" class="floating-button">
    <i class="fas fa-arrow-left"></i>
</a>

<!-- Bottom navigation removed -->

<!-- Memuat library HTML5-QR-Code dari beberapa sumber untuk keandalan -->
<script>
    // Fungsi untuk memeriksa apakah library HTML5QrCode sudah dimuat
    function isHtml5QrCodeLoaded() {
        return typeof Html5Qrcode !== 'undefined';
    }

    // Fungsi untuk memuat script dari URL
    function loadScript(url, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load script from:', url);
            if (callback) callback(new Error('Failed to load script'));
        };
        document.head.appendChild(script);
    }

    // Daftar sumber library HTML5-QR-Code
    var scriptSources = [
        '../assets/js/html5-qrcode.min.js', // Prioritaskan file lokal
        'https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js',
        'https://cdn.jsdelivr.net/npm/html5-qrcode@2.3.8/html5-qrcode.min.js'
    ];

    // Fungsi untuk mencoba memuat script dari sumber berikutnya
    function tryLoadingFromNextSource(index) {
        if (isHtml5QrCodeLoaded()) {
            console.log('HTML5QrCode library already loaded');
            return;
        }

        if (index >= scriptSources.length) {
            console.error('Failed to load HTML5QrCode library from all sources');
            alert('Gagal memuat library pemindai barcode. Silakan muat ulang halaman atau coba browser lain.');
            return;
        }

        loadScript(scriptSources[index], function(error) {
            if (!error && isHtml5QrCodeLoaded()) {
                console.log('HTML5QrCode library loaded successfully from:', scriptSources[index]);
            } else {
                console.warn('Trying next source...');
                tryLoadingFromNextSource(index + 1);
            }
        });
    }

    // Mulai memuat library
    tryLoadingFromNextSource(0);
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update jam secara real-time
    function updateJam() {
        var now = new Date();
        var hours = now.getHours().toString().padStart(2, '0');
        var minutes = now.getMinutes().toString().padStart(2, '0');
        var seconds = now.getSeconds().toString().padStart(2, '0');

        // Update semua elemen dengan id jam-sekarang
        var elements = document.querySelectorAll('#jam-sekarang');
        elements.forEach(function(element) {
            element.textContent = hours + ':' + minutes + ':' + seconds;
        });
    }

    // Update jam setiap detik
    setInterval(updateJam, 1000);
    updateJam();
    const scanner = document.getElementById('scanner');
    const scannerOverlay = document.getElementById('scanner-overlay');
    const toggleButton = document.getElementById('toggleCamera');
    const scanResult = document.getElementById('scanResult');
    const resultContent = document.getElementById('resultContent');
    const locationStatus = document.getElementById('locationStatus');
    const locationDetails = document.getElementById('locationDetails');
    const coordinatesElement = document.getElementById('coordinates');
    const accuracyElement = document.getElementById('accuracy');

    let html5QrCode;
    let scanning = false;
    let currentPosition = null;

    // Initialize location tracking
    if (navigator.geolocation) {
        navigator.geolocation.watchPosition(
            function(position) {
                currentPosition = position;
                updateLocationInfo(position);
            },
            function(error) {
                locationStatus.innerHTML = `<div class="alert alert-danger">Error: ${getLocationErrorMessage(error)}</div>`;
            },
            { enableHighAccuracy: true, maximumAge: 10000, timeout: 7000000 }
        );
    } else {
        locationStatus.innerHTML = '<div class="alert alert-danger">Geolokasi tidak didukung oleh browser Anda.</div>';
    }

    // Function to update location info
    function updateLocationInfo(position) {
        const latitude = position.coords.latitude;
        const longitude = position.coords.longitude;
        const accuracy = Math.round(position.coords.accuracy);

        coordinatesElement.textContent = `${latitude}, ${longitude}`;
        accuracyElement.textContent = accuracy;

        locationStatus.innerHTML = '<div class="alert alert-success">Lokasi Anda berhasil terdeteksi</div>';
        locationDetails.style.display = 'block';

        // Hitung jarak ke lokasi barcode
        <?php if ($has_barcode): ?>
        const barcodeLatitude = <?php echo $barcode_config['latitude']; ?>;
        const barcodeLongitude = <?php echo $barcode_config['longitude']; ?>;
        const allowedRadius = <?php echo $barcode_config['radius']; ?>;

        // Hitung jarak menggunakan Haversine formula
        const distance = calculateDistance(
            latitude,
            longitude,
            barcodeLatitude,
            barcodeLongitude
        );

        // Update elemen jarak
        const distanceElement = document.getElementById('distance');
        const radiusStatusElement = document.getElementById('radius-status');

        if (distanceElement && radiusStatusElement) {
            distanceElement.textContent = `${Math.round(distance)} meter`;

            // Update status radius
            if (distance <= allowedRadius) {
                radiusStatusElement.innerHTML = '<span class="radius-indicator radius-in">Dalam jangkauan</span>';
            } else if (distance <= allowedRadius * 1.2) {
                radiusStatusElement.innerHTML = '<span class="radius-indicator radius-warning">Mendekati batas</span>';
            } else {
                radiusStatusElement.innerHTML = '<span class="radius-indicator radius-out">Di luar jangkauan</span>';
            }
        }
        <?php endif; ?>
    }

    // Function to calculate distance between two coordinates in meters (Haversine formula)
    function calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371000; // Radius of the earth in meters
        const dLat = deg2rad(lat2 - lat1);
        const dLon = deg2rad(lon2 - lon1);
        const a =
            Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
            Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        const distance = R * c; // Distance in meters
        return distance;
    }

    // Helper function to convert degrees to radians
    function deg2rad(deg) {
        return deg * (Math.PI/180);
    }

    // Function to get location error message
    function getLocationErrorMessage(error) {
        switch(error.code) {
            case error.PERMISSION_DENIED:
                return "Izin akses lokasi ditolak. Silakan aktifkan GPS dan izinkan akses lokasi.";
            case error.POSITION_UNAVAILABLE:
                return "Informasi lokasi tidak tersedia. Pastikan GPS Anda aktif.";
            case error.TIMEOUT:
                return "Waktu permintaan lokasi habis. Silakan coba lagi.";
            case error.UNKNOWN_ERROR:
                return "Terjadi kesalahan yang tidak diketahui.";
            default:
                return "Terjadi kesalahan saat mendapatkan lokasi.";
        }
    }

    // Function to handle scan failure
    function onScanFailure(error) {
        // Tidak perlu melakukan apa-apa, ini normal selama pemindaian
        // console.log('Scan error: ', error);
    }

    // Toggle camera button (now only for stopping)
    toggleButton.addEventListener('click', function() {
        if (scanning) {
            stopScanner();
            // Setelah berhenti, tampilkan pesan dan tombol untuk memulai kembali
            scannerOverlay.innerHTML = `
                <div class="text-center">
                    <div class="mb-3">Kamera dimatikan</div>
                    <button class="btn btn-primary" onclick="startScannerAgain()">
                        <i class="fas fa-camera me-2"></i> Mulai Scan Lagi
                    </button>
                </div>
            `;
            toggleButton.style.display = 'none';
        }
    });

    // Function to restart scanner
    window.startScannerAgain = function() {
        // Bersihkan instance sebelumnya jika ada
        if (html5QrCode && html5QrCode.isScanning) {
            try {
                html5QrCode.stop().then(() => {
                    console.log("Stopped previous scanner instance");
                    html5QrCode = null;
                    startScanner();
                }).catch(err => {
                    console.error("Error stopping previous scanner:", err);
                    html5QrCode = null;
                    startScanner();
                });
            } catch (e) {
                console.error("Error in cleanup:", e);
                html5QrCode = null;
                startScanner();
            }
        } else {
            startScanner();
        }
    };

    // Start scanner
    function startScanner() {
        // Periksa apakah library HTML5-QR-Code sudah dimuat
        if (!isHtml5QrCodeLoaded()) {
            console.error("HTML5QrCode library not loaded yet");
            scannerOverlay.innerHTML = `
                <div class="alert alert-danger mb-3">
                    <i class="fas fa-exclamation-circle me-2"></i> Library Tidak Dimuat
                </div>
                <p class="mb-3">Library pemindai barcode belum dimuat. Silakan tunggu beberapa saat atau muat ulang halaman.</p>
                <button class="btn btn-primary" onclick="checkAndStartScanner()">
                    <i class="fas fa-redo me-2"></i> Coba Lagi
                </button>
                <button class="btn btn-secondary ms-2" onclick="location.reload()">
                    <i class="fas fa-sync me-2"></i> Muat Ulang Halaman
                </button>
            `;
            return;
        }

        // Tampilkan pesan loading yang lebih informatif
        scannerOverlay.innerHTML = `
            <div class="d-flex flex-column align-items-center justify-content-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="mb-2">Memuat kamera...</div>
                <div class="small text-muted">Mohon tunggu beberapa saat</div>
            </div>
        `;

        // Tambahkan timeout untuk memuat kamera dengan waktu yang lebih lama
        let cameraTimeout = setTimeout(() => {
            scannerOverlay.innerHTML = `
                <div class="alert alert-warning mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i> Waktu memuat kamera habis
                </div>
                <p class="mb-3">Kamera tidak dapat dimuat dalam waktu yang ditentukan.</p>
                <button class="btn btn-primary" onclick="startScannerAgain()">
                    <i class="fas fa-redo me-2"></i> Coba Lagi
                </button>
            `;
        }, 20000); // 20 detik timeout

        try {
            // Pastikan elemen preview ada
            const previewElement = document.getElementById("preview");
            if (!previewElement) {
                throw new Error("Elemen preview tidak ditemukan");
            }

            // Bersihkan elemen preview terlebih dahulu
            previewElement.innerHTML = '';

            // Tambahkan kelas untuk styling
            previewElement.classList.add('preview-container');

            // Buat instance baru dengan konfigurasi yang lebih baik
            html5QrCode = new Html5Qrcode("preview");

            // Konfigurasi scanner dengan nilai yang lebih sederhana
            const config = {
                fps: 10,
                qrbox: 250,
                formatsToSupport: ['CODE_128', 'CODE_39', 'QR_CODE', 'EAN_13']
            };

            // Konfigurasi kamera - gunakan pendekatan yang lebih sederhana
            const cameraConfig = {
                facingMode: "environment"
            };

            console.log("Starting camera with config:", config);

            // Coba dapatkan daftar kamera terlebih dahulu
            Html5Qrcode.getCameras().then(devices => {
                console.log("Kamera tersedia:", devices);

                // Gunakan kamera belakang jika tersedia
                let cameraId = null;
                if (devices && devices.length) {
                    // Prioritaskan kamera belakang
                    for (const device of devices) {
                        if (device.label.toLowerCase().includes("back") ||
                            device.label.toLowerCase().includes("belakang")) {
                            cameraId = device.id;
                            break;
                        }
                    }
                    // Jika tidak ada kamera belakang, gunakan kamera pertama
                    if (!cameraId) {
                        cameraId = devices[0].id;
                    }
                }

                // Mulai scanner dengan kamera yang dipilih atau konfigurasi default
                return html5QrCode.start(
                    cameraId || cameraConfig,
                    config,
                    onScanSuccess,
                    onScanFailure
                );
            }).then(() => {
                // Berhasil memulai kamera, hapus timeout
                clearTimeout(cameraTimeout);
                console.log("Camera started successfully");
                scanning = true;
                scannerOverlay.style.display = 'none';
                toggleButton.style.display = 'block';

                // Tambahkan kelas untuk styling video dan canvas
                setTimeout(() => {
                    const videoElements = document.querySelectorAll('#preview video');
                    const canvasElements = document.querySelectorAll('#preview canvas');

                    videoElements.forEach(video => {
                        video.style.width = '100%';
                        video.style.height = '100%';
                        video.style.objectFit = 'cover';
                    });

                    canvasElements.forEach(canvas => {
                        canvas.style.width = '100%';
                        canvas.style.height = '100%';
                        canvas.style.objectFit = 'cover';
                    });
                }, 500);
            }).catch((err) => {
                // Gagal memulai kamera, hapus timeout
                clearTimeout(cameraTimeout);
                console.error("Camera start error:", err);

                // Tampilkan pesan error yang lebih informatif
                let errorMessage = "Tidak dapat mengakses kamera";

                if (err.toString().includes("NotAllowedError")) {
                    errorMessage = "Izin kamera ditolak. Silakan izinkan akses kamera di pengaturan browser Anda.";
                } else if (err.toString().includes("NotFoundError")) {
                    errorMessage = "Kamera tidak ditemukan. Pastikan perangkat Anda memiliki kamera yang berfungsi.";
                } else if (err.toString().includes("NotReadableError")) {
                    errorMessage = "Kamera sedang digunakan oleh aplikasi lain. Tutup aplikasi lain yang mungkin menggunakan kamera.";
                } else if (err.toString().includes("OverconstrainedError")) {
                    errorMessage = "Kamera tidak mendukung resolusi yang diminta. Coba gunakan kamera lain.";
                } else if (err.toString().includes("TypeError")) {
                    errorMessage = "Terjadi kesalahan saat mengakses kamera. Coba muat ulang halaman atau gunakan browser lain.";
                }

                // Coba fallback ke metode lain jika gagal dengan getCameras
                if (err.toString().includes("TypeError") || err.toString().includes("undefined")) {
                    console.log("Mencoba metode fallback...");
                    try {
                        // Coba langsung dengan facingMode tanpa getCameras
                        html5QrCode.start(
                            { facingMode: "environment" },
                            config,
                            onScanSuccess,
                            onScanFailure
                        ).then(() => {
                            clearTimeout(cameraTimeout);
                            console.log("Camera started with fallback method");
                            scanning = true;
                            scannerOverlay.style.display = 'none';
                            toggleButton.style.display = 'block';
                            return;
                        }).catch(fallbackErr => {
                            console.error("Fallback method failed:", fallbackErr);
                            // Lanjutkan ke tampilan error di bawah
                        });
                    } catch (fallbackErr) {
                        console.error("Error in fallback method:", fallbackErr);
                    }
                }

                scannerOverlay.innerHTML = `
                    <div class="alert alert-danger mb-3">
                        <i class="fas fa-camera-slash me-2"></i> Error Kamera
                    </div>
                    <p class="mb-3">${errorMessage}</p>
                    <button class="btn btn-primary" onclick="startScannerAgain()">
                        <i class="fas fa-redo me-2"></i> Coba Lagi
                    </button>
                `;
            });
        } catch (error) {
            // Error saat inisialisasi, hapus timeout
            clearTimeout(cameraTimeout);
            console.error("Scanner initialization error:", error);

            scannerOverlay.innerHTML = `
                <div class="alert alert-danger mb-3">
                    <i class="fas fa-exclamation-circle me-2"></i> Error Inisialisasi
                </div>
                <p class="mb-3">Terjadi kesalahan saat menginisialisasi pemindai: ${error.message}</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-redo me-2"></i> Muat Ulang Halaman
                </button>
            `;
        }
    }

    // Fungsi untuk memeriksa library dan memulai scanner
    window.checkAndStartScanner = function() {
        if (isHtml5QrCodeLoaded()) {
            startScanner();
        } else {
            scannerOverlay.innerHTML = `
                <div class="alert alert-info mb-3">
                    <i class="fas fa-spinner fa-spin me-2"></i> Memuat Library
                </div>
                <p class="mb-3">Sedang memuat library pemindai barcode. Silakan tunggu...</p>
            `;

            // Coba lagi dalam 2 detik
            setTimeout(function() {
                if (isHtml5QrCodeLoaded()) {
                    startScanner();
                } else {
                    // Coba muat library lagi
                    tryLoadingFromNextSource(0);

                    // Coba lagi dalam 3 detik
                    setTimeout(function() {
                        if (isHtml5QrCodeLoaded()) {
                            startScanner();
                        } else {
                            scannerOverlay.innerHTML = `
                                <div class="alert alert-danger mb-3">
                                    <i class="fas fa-exclamation-circle me-2"></i> Gagal Memuat Library
                                </div>
                                <p class="mb-3">Tidak dapat memuat library pemindai barcode. Silakan coba lagi nanti.</p>
                                <button class="btn btn-primary" onclick="location.reload()">
                                    <i class="fas fa-sync me-2"></i> Muat Ulang Halaman
                                </button>
                            `;
                        }
                    }, 3000);
                }
            }, 2000);
        }
    }

    // Stop scanner
    function stopScanner() {
        if (html5QrCode) {
            try {
                if (html5QrCode.isScanning) {
                    console.log("Stopping camera...");
                    html5QrCode.stop().then(() => {
                        console.log("Camera stopped successfully");
                        scanning = false;

                        // Bersihkan elemen preview
                        const previewElement = document.getElementById("preview");
                        if (previewElement) {
                            previewElement.innerHTML = '';
                        }

                        // Tampilkan overlay
                        scannerOverlay.style.display = 'flex';
                        scannerOverlay.innerHTML = `
                            <div class="text-center">
                                <div class="mb-3">Kamera dimatikan</div>
                                <button class="btn btn-primary" onclick="startScannerAgain()">
                                    <i class="fas fa-camera me-2"></i> Mulai Scan Lagi
                                </button>
                            </div>
                        `;

                        // Lepaskan sumber daya kamera
                        setTimeout(() => {
                            try {
                                // Hapus semua elemen video dan canvas yang mungkin masih ada
                                const videoElements = document.querySelectorAll('video');
                                const canvasElements = document.querySelectorAll('canvas');

                                videoElements.forEach(video => {
                                    if (video.srcObject) {
                                        const tracks = video.srcObject.getTracks();
                                        tracks.forEach(track => track.stop());
                                        video.srcObject = null;
                                    }
                                    if (video.parentNode) {
                                        video.parentNode.removeChild(video);
                                    }
                                });

                                canvasElements.forEach(canvas => {
                                    if (canvas.parentNode && canvas.parentNode.id === 'preview') {
                                        canvas.parentNode.removeChild(canvas);
                                    }
                                });

                                html5QrCode = null;
                            } catch (e) {
                                console.error('Error releasing camera resources:', e);
                            }
                        }, 500);
                    }).catch((err) => {
                        console.error('Error stopping scanner:', err);
                        scanning = false;
                        scannerOverlay.style.display = 'flex';
                        scannerOverlay.innerHTML = `
                            <div class="alert alert-warning mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i> Gagal mematikan kamera
                            </div>
                            <button class="btn btn-primary" onclick="location.reload()">
                                <i class="fas fa-redo me-2"></i> Muat Ulang Halaman
                            </button>
                        `;
                    });
                } else {
                    // Jika tidak scanning, pastikan overlay ditampilkan
                    scanning = false;
                    scannerOverlay.style.display = 'flex';
                    scannerOverlay.innerHTML = `
                        <div class="text-center">
                            <div class="mb-3">Kamera tidak aktif</div>
                            <button class="btn btn-primary" onclick="startScannerAgain()">
                                <i class="fas fa-camera me-2"></i> Mulai Scan
                            </button>
                        </div>
                    `;

                    // Coba lepaskan sumber daya kamera
                    try {
                        html5QrCode = null;
                    } catch (e) {
                        console.error('Error releasing camera resources:', e);
                    }
                }
            } catch (error) {
                console.error('Error in stopScanner:', error);
                scanning = false;
                scannerOverlay.style.display = 'flex';
                scannerOverlay.innerHTML = `
                    <div class="alert alert-warning mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i> Terjadi kesalahan
                    </div>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo me-2"></i> Muat Ulang Halaman
                    </button>
                `;
            }
        }
    }

    // Mulai scanner secara otomatis saat halaman dimuat
    // Tunggu library dimuat sebelum memulai scanner
    if (isHtml5QrCodeLoaded()) {
        // Library sudah dimuat, mulai scanner
        startScanner();
    } else {
        // Tunggu library dimuat
        scannerOverlay.innerHTML = `
            <div class="d-flex flex-column align-items-center justify-content-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div>Memuat library pemindai barcode...</div>
            </div>
        `;

        // Cek setiap 500ms apakah library sudah dimuat
        let libraryCheckInterval = setInterval(function() {
            if (isHtml5QrCodeLoaded()) {
                clearInterval(libraryCheckInterval);
                startScanner();
            }
        }, 500);

        // Timeout setelah 10 detik jika library tidak dimuat
        setTimeout(function() {
            clearInterval(libraryCheckInterval);
            if (!isHtml5QrCodeLoaded()) {
                scannerOverlay.innerHTML = `
                    <div class="alert alert-danger mb-3">
                        <i class="fas fa-exclamation-circle me-2"></i> Gagal Memuat Library
                    </div>
                    <p class="mb-3">Tidak dapat memuat library pemindai barcode dalam waktu yang ditentukan.</p>
                    <button class="btn btn-primary" onclick="checkAndStartScanner()">
                        <i class="fas fa-redo me-2"></i> Coba Lagi
                    </button>
                    <button class="btn btn-secondary ms-2" onclick="location.reload()">
                        <i class="fas fa-sync me-2"></i> Muat Ulang Halaman
                    </button>
                `;
            }
        }, 10000);
    }

    // On scan success
    function onScanSuccess(decodedText, decodedResult) {
        // Stop scanning
        stopScanner();
        toggleButton.style.display = 'none';

        // Show result
        scanResult.style.display = 'block';
        resultContent.innerHTML = `
            <div class="alert alert-info">
                <p><strong>Barcode terdeteksi!</strong></p>
                <p>Memproses absensi...</p>
            </div>
        `;

        // Check if location is available
        if (!currentPosition) {
            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <p><strong>Error:</strong> Lokasi tidak tersedia.</p>
                    <p>Pastikan GPS aktif dan izin lokasi diberikan.</p>
                </div>
            `;
            return;
        }

        // Cek apakah karyawan berada dalam radius yang diizinkan
        <?php if ($has_barcode): ?>
        const barcodeLatitude = <?php echo $barcode_config['latitude']; ?>;
        const barcodeLongitude = <?php echo $barcode_config['longitude']; ?>;
        const allowedRadius = <?php echo $barcode_config['radius']; ?>;

        const distance = calculateDistance(
            currentPosition.coords.latitude,
            currentPosition.coords.longitude,
            barcodeLatitude,
            barcodeLongitude
        );

        if (distance > allowedRadius) {
            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-map-marker-alt me-3 fa-2x text-danger"></i>
                        <h5 class="mb-0">Di Luar Jangkauan</h5>
                    </div>
                    <p>Anda berada di luar jangkauan lokasi absensi!</p>
                    <p>Jarak Anda: <strong>${Math.round(distance)} meter</strong></p>
                    <p>Silakan mendekati lokasi absensi dan coba lagi.</p>
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo me-2"></i> Coba Lagi
                    </button>
                </div>
            `;
            return;
        }
        <?php endif; ?>

        // Validasi jenis absen berdasarkan status presensi
        <?php if ($sudah_absen_masuk && $sudah_absen_pulang): ?>
        // Jika sudah absen masuk dan pulang
        resultContent.innerHTML = `
            <div class="alert alert-secondary">
                <p><strong>Informasi:</strong></p>
                <p>Anda sudah melakukan absensi masuk dan pulang hari ini.</p>
            </div>
            <div class="text-center mt-3">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i> Kembali ke Dashboard
                </a>
            </div>
        `;
        return;
        <?php endif; ?>

        // Send to server
        const formData = new FormData();
        formData.append('scan_result', decodedText);

        // Pastikan data lokasi valid sebelum dikirim
        if (currentPosition && currentPosition.coords) {
            formData.append('latitude', currentPosition.coords.latitude);
            formData.append('longitude', currentPosition.coords.longitude);
            formData.append('accuracy', currentPosition.coords.accuracy);
        } else {
            // Jika tidak ada data lokasi, gunakan nilai default
            formData.append('latitude', 0);
            formData.append('longitude', 0);
            formData.append('accuracy', 0);
            console.warn('Location data not available, using default values');
        }

        // Parameter jenis_absen tidak lagi diperlukan karena akan ditentukan otomatis di server

        // Tambahkan timestamp untuk mencegah caching
        const timestamp = new Date().getTime();

        // Tampilkan indikator loading
        resultContent.innerHTML = `
            <div class="d-flex justify-content-center my-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Memproses absensi...</span>
            </div>
        `;

        // Tampilkan indikator loading
        resultContent.innerHTML = `
            <div class="d-flex justify-content-center my-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Memproses absensi...</span>
            </div>
        `;

        // Kirim form langsung ke process_barcode.php
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'process_barcode.php';
        form.style.display = 'none';

        // Tambahkan semua field dari formData
        for (const [key, value] of formData.entries()) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            form.appendChild(input);
        }

        // Tambahkan form ke dokumen dan submit
        document.body.appendChild(form);
        form.submit();
    }

    // On scan failure - sudah didefinisikan di atas
    // function onScanFailure(error) {
    //     // Tidak perlu melakukan apa-apa, ini normal selama pemindaian
    // }

    // Debug: Log nilai variabel untuk troubleshooting
    console.log('Debug Flexible Schedule:');
    console.log('allow_flexible_schedule: <?php echo $allow_flexible_schedule ? 'true' : 'false'; ?>');
    console.log('selected_schedule: <?php echo $selected_schedule ? 'true' : 'false'; ?>');
    console.log('jam_kerja_options count: <?php echo count($jam_kerja_options); ?>');

    // Cek apakah perlu menampilkan pop-up pemilihan jam kerja
    <?php if ($allow_flexible_schedule && !$selected_schedule && !empty($jam_kerja_options)): ?>
    console.log('Kondisi terpenuhi - menampilkan pop-up pemilihan jam kerja');
    // Tampilkan pop-up pemilihan jam kerja
    setTimeout(function() {
        showScheduleSelectionModal();
    }, 2000); // Delay 2 detik untuk memastikan SweetAlert sudah dimuat
    <?php else: ?>
    console.log('Kondisi tidak terpenuhi untuk menampilkan pop-up');
    console.log('Alasan:');
    console.log('- Allow flexible: <?php echo $allow_flexible_schedule ? 'YES' : 'NO'; ?>');
    console.log('- Selected schedule: <?php echo $selected_schedule ? 'YES' : 'NO'; ?>');
    console.log('- Jam kerja options: <?php echo count($jam_kerja_options); ?>');
    <?php endif; ?>
});

// Fungsi untuk menampilkan modal pemilihan jam kerja
function showScheduleSelectionModal() {
    if (typeof Swal !== 'undefined') {
        const scheduleOptions = {};
        <?php foreach ($jam_kerja_options as $option): ?>
        scheduleOptions['<?php echo $option['id']; ?>'] = '<?php echo $option['nama_jam_kerja']; ?> (<?php echo $option['jam_masuk']; ?> - <?php echo $option['jam_pulang']; ?>)';
        <?php endforeach; ?>

        Swal.fire({
            title: 'Pilih Jam Kerja Anda',
            text: 'Silakan pilih jam kerja yang sesuai untuk hari ini',
            input: 'select',
            inputOptions: scheduleOptions,
            inputPlaceholder: 'Pilih jam kerja...',
            showCancelButton: false,
            allowOutsideClick: false,
            allowEscapeKey: false,
            confirmButtonText: 'Pilih',
            inputValidator: (value) => {
                if (!value) {
                    return 'Anda harus memilih jam kerja!'
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Submit pilihan jam kerja
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '';

                const jamKerjaInput = document.createElement('input');
                jamKerjaInput.type = 'hidden';
                jamKerjaInput.name = 'jam_kerja_id';
                jamKerjaInput.value = result.value;

                const submitInput = document.createElement('input');
                submitInput.type = 'hidden';
                submitInput.name = 'pilih_jam_kerja';
                submitInput.value = '1';

                form.appendChild(jamKerjaInput);
                form.appendChild(submitInput);
                document.body.appendChild(form);
                form.submit();
            }
        });
    } else {
        // Fallback jika SweetAlert tidak tersedia
        alert('Silakan refresh halaman untuk memilih jam kerja Anda.');
    }
}
</script>


