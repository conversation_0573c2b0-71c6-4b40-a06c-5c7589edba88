<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Aktifkan error reporting untuk debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Fungsi untuk menangani error dan menge<PERSON>likan respons JSON
function handleError($message, $details = '') {
    header('Content-Type: application/json; charset=utf-8');
    $response = [
        'status' => 'error',
        'message' => $message
    ];

    if (!empty($details) && ini_get('display_errors')) {
        $response['details'] = $details;
    }

    echo json_encode($response);
    exit;
}

// Set error handler
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        handleError("Terjadi kesalahan pada server", "$errstr in $errfile on line $errline");
    }
    return false; // Lanjutkan ke error handler standar PHP
});

// Fungsi untuk mengambil jam kerja
function getJamKerjaForScanBarcode($conn, $user_id, $user, $allow_flexible_schedule, $today, $hari_name, $hari_en) {
    $jam_kerja = null;
    $selected_schedule = null;
    $jam_kerja_options = [];

    if ($allow_flexible_schedule) {
        // Debug: Log flexible schedule check
        error_log("=== DEBUG FLEXIBLE SCHEDULE SCAN BARCODE ===");
        error_log("User ID: $user_id");
        error_log("Today: $today");
        error_log("Allow flexible schedule: YES");

        // Cek apakah karyawan sudah memilih jam kerja untuk hari ini
        $query = "SELECT usc.*, jk.*
                  FROM user_schedule_choices usc
                  JOIN jam_kerja jk ON usc.jam_kerja_id = jk.id
                  WHERE usc.user_id = '$user_id' AND usc.tanggal = '$today'";
        $result = mysqli_query($conn, $query);
        error_log("Query selected schedule: $query");
        error_log("Query result rows: " . ($result ? mysqli_num_rows($result) : 'FAILED'));

        if ($result && mysqli_num_rows($result) > 0) {
            $selected_schedule = mysqli_fetch_assoc($result);
            $jam_kerja = $selected_schedule;
            error_log("Selected schedule found: " . json_encode($selected_schedule));
            error_log("Jam kerja set to selected schedule");
            error_log("Selected jam kerja details:");
            error_log("- ID: " . ($jam_kerja['id'] ?? 'N/A'));
            error_log("- Nama: " . ($jam_kerja['nama_jam_kerja'] ?? 'N/A'));
            error_log("- Awal masuk: " . ($jam_kerja['awal_jam_masuk'] ?? 'N/A'));
            error_log("- Akhir masuk: " . ($jam_kerja['akhir_jam_masuk'] ?? 'N/A'));
        } else {
            error_log("No selected schedule found for today");
            error_log("MySQL error (if any): " . mysqli_error($conn));
        }

        // Ambil semua jam kerja yang tersedia untuk bidang ini dari tabel jam_kerja
        if ($user['bidang_id']) {
            $query = "SELECT * FROM jam_kerja
                      WHERE bidang_id = '{$user['bidang_id']}'
                      ORDER BY nama_jam_kerja ASC";
            $result = mysqli_query($conn, $query);
            error_log("Query jam kerja options di scan barcode: $query");
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $jam_kerja_options[] = $row;
                    error_log("Found jam kerja di scan barcode: " . $row['nama_jam_kerja'] . " (ID: " . $row['id'] . ")");
                }
                error_log("Total jam kerja options di scan barcode: " . count($jam_kerja_options));
            } else {
                error_log("Query jam kerja options di scan barcode failed: " . mysqli_error($conn));
            }
        }
    } else {
        // Debug: Log non-flexible schedule
        error_log("=== DEBUG NON-FLEXIBLE SCHEDULE SCAN BARCODE ===");
        error_log("Allow flexible schedule: NO");
        error_log("Using default jam kerja for hari: $hari_name");

        // Gunakan fungsi getJamKerjaByUserId dari functions.php
        $jam_kerja = getJamKerjaByUserId($user_id);

        // Log untuk debugging
        error_log("Hari ini: $hari_en ($hari_name)");
        error_log("Jam kerja dari fungsi: " . ($jam_kerja ? json_encode($jam_kerja) : "tidak ditemukan"));

        // Jika tidak ada jam kerja dari fungsi, coba query langsung
        if (!$jam_kerja) {
            $query = "SELECT jkb.*, jk.awal_jam_masuk, jk.jam_masuk, jk.akhir_jam_masuk, jk.jam_pulang, jk.akhir_jam_pulang
                      FROM jam_kerja_bidang jkb
                      JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
                      WHERE jkb.bidang_id = '{$user['bidang_id']}' AND jkb.hari = '$hari_name'";
            $result = mysqli_query($conn, $query);
            $jam_kerja = mysqli_fetch_assoc($result);

            error_log("Query langsung: $query");
            error_log("Hasil query langsung: " . ($jam_kerja ? json_encode($jam_kerja) : "tidak ditemukan"));
        }
        error_log("Final jam kerja for non-flexible: " . ($jam_kerja ? json_encode($jam_kerja) : "NULL"));
        error_log("===============================================");
    }

    return [$jam_kerja, $selected_schedule, $jam_kerja_options];
}

// Cek login
if (!isLoggedIn() || $_SESSION['role'] != 'karyawan') {
    redirect('../index.php');
}

// Ambil data user
$user_id = $_SESSION['user_id'];
$query = "SELECT u.*, l.nama_lokasi, l.latitude, l.longitude, l.radius
          FROM users u
          LEFT JOIN lokasi l ON u.lokasi_id = l.id
          WHERE u.id = '$user_id'";
$result = mysqli_query($conn, $query);
$user = mysqli_fetch_assoc($result);

// Ambil data lokasi
$lokasi = null;
if (!empty($user['lokasi_id'])) {
    $query = "SELECT * FROM lokasi WHERE id = '{$user['lokasi_id']}'";
    $result = mysqli_query($conn, $query);
    $lokasi = mysqli_fetch_assoc($result);
}

// Ambil data barcode untuk lokasi user
$query = "SELECT bc.*, l.nama_lokasi, l.latitude, l.longitude
          FROM barcode_config bc
          JOIN lokasi l ON bc.lokasi_id = l.id
          WHERE bc.lokasi_id = '{$user['lokasi_id']}' AND bc.is_active = 1";
$result = mysqli_query($conn, $query);
$barcode_config = mysqli_fetch_assoc($result);

// Cek apakah ada konfigurasi barcode untuk lokasi user
$has_barcode = ($result && mysqli_num_rows($result) > 0);

// Cek apakah user diizinkan menggunakan absensi barcode
$allow_barcode = isset($user['allow_barcode']) && $user['allow_barcode'] == 1;
$allow_flexible_schedule = isset($user['allow_flexible_schedule']) && $user['allow_flexible_schedule'] == 1;

// Ambil data presensi hari ini
$today = date('Y-m-d');
$query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result = mysqli_query($conn, $query);
$presensi_hari_ini = mysqli_fetch_assoc($result);

// Ambil parameter type dari URL (masuk/pulang)
$jenis_absen = isset($_GET['type']) ? clean($_GET['type']) : 'masuk';
if (!in_array($jenis_absen, ['masuk', 'pulang'])) {
    $jenis_absen = 'masuk';
}

// Cek apakah karyawan sedang dalam perjalanan dinas
$query = "SELECT * FROM izin_dinas
          WHERE user_id = '$user_id'
          AND status = 'Approved'
          AND tanggal_mulai <= '$today'
          AND tanggal_selesai >= '$today'";
$result = mysqli_query($conn, $query);
$izin_dinas_aktif = mysqli_fetch_assoc($result);

// Cek apakah hari ini hari libur
$query = "SELECT * FROM hari_libur WHERE tanggal = '$today'";
$result = mysqli_query($conn, $query);
$hari_libur = mysqli_fetch_assoc($result);

// Ambil jam kerja berdasarkan bidang dan hari
$hari_map = [
    'Monday' => 'Senin',
    'Tuesday' => 'Selasa',
    'Wednesday' => 'Rabu',
    'Thursday' => 'Kamis',
    'Friday' => 'Jumat',
    'Saturday' => 'Sabtu',
    'Sunday' => 'Minggu'
];
$hari_en = date('l'); // Mendapatkan nama hari dalam bahasa Inggris
$hari_name = $hari_map[$hari_en]; // Konversi ke bahasa Indonesia

// Ambil jam kerja berdasarkan apakah user memiliki izin jam kerja fleksibel
list($jam_kerja, $selected_schedule, $jam_kerja_options) = getJamKerjaForScanBarcode($conn, $user_id, $user, $allow_flexible_schedule, $today, $hari_name, $hari_en);

$sudah_absen_masuk = ($presensi_hari_ini && !empty($presensi_hari_ini['jam_masuk']));
$sudah_absen_pulang = ($presensi_hari_ini && !empty($presensi_hari_ini['jam_pulang']));

// Proses pilihan jam kerja fleksibel
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['pilih_jam_kerja'])) {
    $jam_kerja_id = clean($_POST['jam_kerja_id']);

    // Validasi jam kerja ID - hanya dari tabel jam_kerja (sama seperti di presensi.php)
    $query = "SELECT * FROM jam_kerja
              WHERE id = '$jam_kerja_id' AND bidang_id = '{$user['bidang_id']}'";
    $result = mysqli_query($conn, $query);

    error_log("Validating jam kerja ID $jam_kerja_id for bidang {$user['bidang_id']} di scan barcode: " . mysqli_num_rows($result) . " results");

    if (!$result || mysqli_num_rows($result) == 0) {
        error_log("Jam kerja validation failed for ID $jam_kerja_id di scan barcode");
        $_SESSION['error_message'] = 'Jam kerja yang dipilih tidak valid untuk bidang Anda!';
        redirect('scan_barcode.php');
    }

    $jam_kerja_data = mysqli_fetch_assoc($result);

    // Cek apakah sudah ada pilihan untuk hari ini
    $query = "SELECT * FROM user_schedule_choices WHERE user_id = '$user_id' AND tanggal = '$today'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        // Update pilihan yang sudah ada
        $query = "UPDATE user_schedule_choices
                  SET jam_kerja_id = '$jam_kerja_id', updated_at = NOW()
                  WHERE user_id = '$user_id' AND tanggal = '$today'";
    } else {
        // Insert pilihan baru
        $query = "INSERT INTO user_schedule_choices (user_id, jam_kerja_id, tanggal, created_at)
                  VALUES ('$user_id', '$jam_kerja_id', '$today', NOW())";
    }

    if (mysqli_query($conn, $query)) {
        $_SESSION['success_message'] = 'Jam kerja berhasil dipilih: ' . $jam_kerja_data['nama_jam_kerja'];

        // PENTING: Refresh jam kerja data setelah pilihan jam kerja berhasil disimpan
        // Ini memastikan bahwa jika ada proses absensi dalam request yang sama,
        // akan menggunakan jam kerja yang baru dipilih
        error_log("=== REFRESH JAM KERJA AFTER SELECTION ===");
        list($jam_kerja, $selected_schedule, $jam_kerja_options) = getJamKerjaForScanBarcode($conn, $user_id, $user, $allow_flexible_schedule, $today, $hari_name, $hari_en);
        error_log("Jam kerja refreshed after selection: " . ($jam_kerja ? json_encode($jam_kerja) : 'NULL'));
        error_log("==========================================");

        redirect('scan_barcode.php');
    } else {
        $_SESSION['error_message'] = 'Gagal menyimpan pilihan jam kerja: ' . mysqli_error($conn);
        redirect('scan_barcode.php');
    }
}

// Proses absensi dengan barcode
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['scan_result'])) {
    // Set header untuk respons JSON
    header('Content-Type: application/json; charset=utf-8');

    // PENTING: Ambil ulang jam kerja untuk memastikan data terbaru saat POST request
    // Ini sangat penting untuk jam kerja fleksibel karena user mungkin baru saja memilih jam kerja
    error_log("=== REFRESH JAM KERJA FOR POST REQUEST ===");
    list($jam_kerja, $selected_schedule, $jam_kerja_options) = getJamKerjaForScanBarcode($conn, $user_id, $user, $allow_flexible_schedule, $today, $hari_name, $hari_en);
    error_log("Jam kerja refreshed for POST request: " . ($jam_kerja ? json_encode($jam_kerja) : 'NULL'));

    // Tambahan: Cek apakah ada pilihan jam kerja terbaru yang belum ter-load
    if ($allow_flexible_schedule) {
        $check_query = "SELECT usc.*, jk.*
                        FROM user_schedule_choices usc
                        JOIN jam_kerja jk ON usc.jam_kerja_id = jk.id
                        WHERE usc.user_id = '$user_id' AND usc.tanggal = '$today'
                        ORDER BY usc.created_at DESC LIMIT 1";
        $check_result = mysqli_query($conn, $check_query);
        error_log("Double-check query: $check_query");
        error_log("Double-check result rows: " . ($check_result ? mysqli_num_rows($check_result) : 'FAILED'));

        if ($check_result && mysqli_num_rows($check_result) > 0) {
            $latest_schedule = mysqli_fetch_assoc($check_result);
            error_log("Latest schedule from double-check: " . json_encode($latest_schedule));

            if (!$jam_kerja || $jam_kerja['id'] != $latest_schedule['id']) {
                error_log("Found newer schedule selection, updating jam_kerja");
                error_log("Old jam_kerja ID: " . ($jam_kerja ? $jam_kerja['id'] : 'NULL'));
                error_log("New jam_kerja ID: " . $latest_schedule['id']);
                $jam_kerja = $latest_schedule;
                $selected_schedule = $latest_schedule;
            } else {
                error_log("Jam kerja already up to date, no change needed");
            }
        } else {
            error_log("No schedule found in double-check");
        }
    }
    error_log("Final jam kerja for validation: " . ($jam_kerja ? json_encode($jam_kerja) : 'NULL'));
    error_log("==========================================");
    // Cek apakah hari ini hari libur
    if ($hari_libur) {
        $response = [
            'status' => 'error',
            'message' => 'Hari ini adalah hari libur: ' . $hari_libur['nama_libur']
        ];
        echo json_encode($response);
        exit;
    }

    // Cek apakah karyawan sedang dalam perjalanan dinas
    if ($izin_dinas_aktif) {
        $response = [
            'status' => 'error',
            'message' => 'Anda sedang dalam perjalanan dinas. Absensi otomatis tercatat.'
        ];
        echo json_encode($response);
        exit;
    }

    // Debug: Log jam kerja yang akan digunakan untuk validasi
    error_log("=== DEBUG SCAN BARCODE VALIDATION ===");
    error_log("Allow flexible schedule: " . ($allow_flexible_schedule ? 'YES' : 'NO'));
    error_log("Selected schedule: " . ($selected_schedule ? 'YES' : 'NO'));
    error_log("Jam kerja data: " . ($jam_kerja ? json_encode($jam_kerja) : 'NULL'));
    if ($jam_kerja) {
        error_log("Jam kerja details:");
        error_log("- Nama: " . ($jam_kerja['nama_jam_kerja'] ?? 'N/A'));
        error_log("- Awal masuk: " . ($jam_kerja['awal_jam_masuk'] ?? 'N/A'));
        error_log("- Jam masuk: " . ($jam_kerja['jam_masuk'] ?? 'N/A'));
        error_log("- Akhir masuk: " . ($jam_kerja['akhir_jam_masuk'] ?? 'N/A'));
        error_log("- Jam pulang: " . ($jam_kerja['jam_pulang'] ?? 'N/A'));
        error_log("- Akhir pulang: " . ($jam_kerja['akhir_jam_pulang'] ?? 'N/A'));
    }
    error_log("Current time: " . date('H:i:s'));
    error_log("Jenis absen: " . ($jenis_absen ?? 'N/A'));
    error_log("Validation rules applied: SAME AS PRESENSI.PHP");
    error_log("=====================================");

    // Cek apakah jam kerja sudah diatur
    if (!$jam_kerja) {
        $response = [
            'status' => 'error',
            'message' => 'Jam kerja belum diatur untuk bidang Anda. Silakan hubungi administrator.'
        ];
        echo json_encode($response);
        exit;
    }

    // Validasi dan sanitasi input
    $scan_result = isset($_POST['scan_result']) ? clean($_POST['scan_result']) : '';
    $latitude = isset($_POST['latitude']) ? clean($_POST['latitude']) : 0;
    $longitude = isset($_POST['longitude']) ? clean($_POST['longitude']) : 0;
    $accuracy = isset($_POST['accuracy']) ? clean($_POST['accuracy']) : 0;
    $jenis_absen = isset($_POST['jenis_absen']) ? clean($_POST['jenis_absen']) : 'masuk';
    $jam_sekarang = date('H:i:s');

    // Log data untuk debugging
    error_log("Scan data received: " . json_encode([
        'scan_result' => $scan_result,
        'latitude' => $latitude,
        'longitude' => $longitude,
        'accuracy' => $accuracy,
        'jenis_absen' => $jenis_absen
    ]));

    // Validasi hasil scan
    if (empty($scan_result)) {
        $response = [
            'status' => 'error',
            'message' => 'Hasil scan barcode kosong!'
        ];
        echo json_encode($response);
        exit;
    }

    // Validasi izin barcode
    if (!$allow_barcode) {
        $response = [
            'status' => 'error',
            'message' => 'Anda tidak diizinkan menggunakan fitur absensi barcode. Silakan hubungi administrator.'
        ];
        echo json_encode($response);
        exit;
    }

    // Validasi lokasi
    if (empty($latitude) || empty($longitude)) {
        $response = [
            'status' => 'error',
            'message' => 'Lokasi tidak terdeteksi! Pastikan GPS aktif dan izin lokasi diberikan.'
        ];
        echo json_encode($response);
        exit;
    }

    // Cek apakah barcode valid
    try {
        // Sanitasi input untuk query
        $scan_result_safe = mysqli_real_escape_string($conn, $scan_result);

        $query = "SELECT bc.*, l.nama_lokasi, l.latitude, l.longitude, l.radius
                FROM barcode_config bc
                JOIN lokasi l ON bc.lokasi_id = l.id
                WHERE bc.barcode_value = '$scan_result_safe' AND bc.is_active = 1";

        $result = mysqli_query($conn, $query);

        if (!$result) {
            throw new Exception("Database error: " . mysqli_error($conn));
        }

        if (mysqli_num_rows($result) == 0) {
            $response = [
                'status' => 'error',
                'message' => 'Barcode tidak valid atau tidak aktif!'
            ];
            echo json_encode($response);
            exit;
        }

        $barcode = mysqli_fetch_assoc($result);

        // Log barcode data untuk debugging
        error_log("Barcode data found: " . json_encode($barcode));
    } catch (Exception $e) {
        $response = [
            'status' => 'error',
            'message' => 'Terjadi kesalahan saat memvalidasi barcode: ' . $e->getMessage()
        ];
        echo json_encode($response);
        exit;
    }

    // Hitung jarak antara lokasi user dan lokasi barcode
    try {
        // Pastikan semua nilai adalah angka yang valid
        $lat1 = floatval($latitude);
        $lon1 = floatval($longitude);
        $lat2 = floatval($barcode['latitude']);
        $lon2 = floatval($barcode['longitude']);

        // Cek apakah nilai valid
        if (!is_numeric($lat1) || !is_numeric($lon1) || !is_numeric($lat2) || !is_numeric($lon2)) {
            throw new Exception("Koordinat lokasi tidak valid");
        }

        $jarak = hitungJarak($lat1, $lon1, $lat2, $lon2);

        // Log jarak untuk debugging
        error_log("Jarak dihitung: $jarak meter");
    } catch (Exception $e) {
        $response = [
            'status' => 'error',
            'message' => 'Terjadi kesalahan saat menghitung jarak: ' . $e->getMessage()
        ];
        echo json_encode($response);
        exit;
    }

    // Validasi radius
    if ($jarak > $barcode['radius']) {
        $response = [
            'status' => 'error',
            'message' => 'Anda berada di luar radius yang diizinkan! Jarak Anda: ' . round($jarak) . ' meter, Radius: ' . $barcode['radius'] . ' meter'
        ];
        echo json_encode($response);
        exit;
    }

    // Proses absensi
    if ($jenis_absen == 'masuk') {
        // Cek apakah sudah absen masuk
        if ($sudah_absen_masuk) {
            $response = [
                'status' => 'error',
                'message' => 'Anda sudah melakukan absensi masuk hari ini!'
            ];
            echo json_encode($response);
            exit;
        }

        // Validasi waktu absensi masuk (sama seperti di presensi.php)
        // Debug: Log detail validasi
        error_log("=== VALIDASI ABSEN MASUK ===");
        error_log("Jam sekarang: $jam_sekarang");
        error_log("Jam kerja yang digunakan untuk validasi:");
        error_log("- ID: " . ($jam_kerja['id'] ?? 'N/A'));
        error_log("- Nama: " . ($jam_kerja['nama_jam_kerja'] ?? 'N/A'));
        error_log("- Awal masuk: " . ($jam_kerja['awal_jam_masuk'] ?? 'N/A'));
        error_log("- Jam masuk: " . ($jam_kerja['jam_masuk'] ?? 'N/A'));
        error_log("- Akhir masuk: " . ($jam_kerja['akhir_jam_masuk'] ?? 'N/A'));
        error_log("============================");

        // Cek apakah sudah melewati jam akhir masuk
        if ($jam_sekarang > $jam_kerja['akhir_jam_masuk']) {
            error_log("VALIDATION FAILED: Jam sekarang ($jam_sekarang) > Akhir jam masuk ({$jam_kerja['akhir_jam_masuk']})");
            $response = [
                'status' => 'error',
                'message' => 'Anda tidak dapat melakukan absensi masuk karena sudah melewati jam akhir masuk (' . $jam_kerja['akhir_jam_masuk'] . '). Jam kerja yang digunakan: ' . ($jam_kerja['nama_jam_kerja'] ?? 'N/A')
            ];
            echo json_encode($response);
            exit;
        }

        // Cek apakah sudah melewati jam awal masuk
        if ($jam_sekarang < $jam_kerja['awal_jam_masuk']) {
            $response = [
                'status' => 'warning',
                'message' => 'Anda belum dapat melakukan absensi masuk karena belum memasuki jam awal masuk (' . $jam_kerja['awal_jam_masuk'] . ').'
            ];
            echo json_encode($response);
            exit;
        }

        // Tentukan status berdasarkan waktu (sama seperti di presensi.php)
        $status = 'Tepat Waktu';
        if ($jam_sekarang > $jam_kerja['jam_masuk']) {
            $status = 'Terlambat';
        }

        // Proses foto barcode jika ada (untuk konsistensi dengan process_barcode.php)
        $foto_filename = null;
        if (isset($_POST['foto_barcode']) && !empty($_POST['foto_barcode'])) {
            $foto_data = $_POST['foto_barcode'];

            try {
                // Hapus header data URI
                $foto_data = str_replace('data:image/jpeg;base64,', '', $foto_data);
                $foto_data = str_replace(' ', '+', $foto_data);

                // Decode base64 ke binary
                $foto_binary = base64_decode($foto_data);

                // Buat nama file unik
                $foto_filename = 'barcode_masuk_' . $user_id . '_' . date('Ymd_His') . '.jpg';

                // Buat direktori uploads jika belum ada
                $upload_dir = '../uploads/';
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                // Simpan file
                $file_path = $upload_dir . $foto_filename;
                if (!file_put_contents($file_path, $foto_binary)) {
                    $foto_filename = null;
                }
            } catch (Exception $e) {
                $foto_filename = null;
            }
        }

        // Buat lokasi masuk dalam format string
        $lokasi_masuk = "Barcode: {$barcode['nama_lokasi']}, Lat: $latitude, Long: $longitude, Akurasi: $accuracy m";

        // Simpan data presensi dengan foto barcode
        $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, status, keterangan)
                  VALUES ('$user_id', '$today', '$jam_sekarang', " .
                  ($foto_filename ? "'$foto_filename'" : "NULL") . ", " .
                  "'$lokasi_masuk', '$status', 'Absensi dengan barcode')";

        if (mysqli_query($conn, $query)) {
            $response = [
                'status' => 'success',
                'message' => 'Absensi masuk berhasil! Status: ' . $status
            ];
            echo json_encode($response);
            exit;
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Gagal menyimpan data absensi: ' . mysqli_error($conn)
            ];
            echo json_encode($response);
            exit;
        }
    } else if ($jenis_absen == 'pulang') {
        // Cek apakah sudah absen masuk
        if (!$sudah_absen_masuk) {
            $response = [
                'status' => 'error',
                'message' => 'Anda belum melakukan absensi masuk hari ini!'
            ];
            echo json_encode($response);
            exit;
        }

        // Cek apakah sudah absen pulang
        if ($sudah_absen_pulang) {
            $response = [
                'status' => 'error',
                'message' => 'Anda sudah melakukan absensi pulang hari ini!'
            ];
            echo json_encode($response);
            exit;
        }

        // Validasi waktu absensi pulang (sama seperti di presensi.php)
        // Cek apakah sudah melewati jam akhir pulang
        if ($jam_sekarang > $jam_kerja['akhir_jam_pulang']) {
            $response = [
                'status' => 'error',
                'message' => 'Anda tidak dapat melakukan absensi pulang karena sudah melewati jam akhir pulang (' . $jam_kerja['akhir_jam_pulang'] . ').'
            ];
            echo json_encode($response);
            exit;
        }

        // Cek apakah sudah mencapai jam pulang (sama seperti di presensi.php)
        if ($jam_sekarang < $jam_kerja['jam_pulang']) {
            $response = [
                'status' => 'error',
                'message' => 'Anda tidak dapat melakukan absensi pulang karena belum mencapai jam pulang (' . $jam_kerja['jam_pulang'] . ').'
            ];
            echo json_encode($response);
            exit;
        }

        // Tentukan status berdasarkan waktu (sama seperti di presensi.php)
        $status = $presensi_hari_ini['status']; // Pertahankan status sebelumnya
        // Catatan: Kita sudah memvalidasi bahwa waktu saat ini >= jam_pulang, jadi tidak perlu lagi status 'Pulang Awal'

        // Proses foto barcode jika ada (untuk konsistensi dengan process_barcode.php)
        $foto_filename = null;
        if (isset($_POST['foto_barcode']) && !empty($_POST['foto_barcode'])) {
            $foto_data = $_POST['foto_barcode'];

            try {
                // Hapus header data URI
                $foto_data = str_replace('data:image/jpeg;base64,', '', $foto_data);
                $foto_data = str_replace(' ', '+', $foto_data);

                // Decode base64 ke binary
                $foto_binary = base64_decode($foto_data);

                // Buat nama file unik
                $foto_filename = 'barcode_pulang_' . $user_id . '_' . date('Ymd_His') . '.jpg';

                // Buat direktori uploads jika belum ada
                $upload_dir = '../uploads/';
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                // Simpan file
                $file_path = $upload_dir . $foto_filename;
                if (!file_put_contents($file_path, $foto_binary)) {
                    $foto_filename = null;
                }
            } catch (Exception $e) {
                $foto_filename = null;
            }
        }

        // Buat lokasi pulang dalam format string
        $lokasi_pulang = "Barcode: {$barcode['nama_lokasi']}, Lat: $latitude, Long: $longitude, Akurasi: $accuracy m";

        // Update data absensi dengan foto barcode
        $query = "UPDATE presensi SET
                  jam_pulang = '$jam_sekarang',
                  lokasi_pulang = '$lokasi_pulang',
                  status = '$status'" .
                  ($foto_filename ? ", foto_pulang = '$foto_filename'" : "") .
                  " WHERE id = '{$presensi_hari_ini['id']}'";

        if (mysqli_query($conn, $query)) {
            $response = [
                'status' => 'success',
                'message' => 'Absensi pulang berhasil! Status: ' . $status
            ];
            echo json_encode($response);
            exit;
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Gagal menyimpan data absensi: ' . mysqli_error($conn)
            ];
            echo json_encode($response);
            exit;
        }
    }

    // Jika sampai di sini, berarti ada kesalahan
    $response = [
        'status' => 'error',
        'message' => 'Terjadi kesalahan yang tidak diketahui!'
    ];
    echo json_encode($response);
    exit;
}

// Jika ini adalah request AJAX, hentikan eksekusi di sini
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
    exit;
}

// Fungsi hitungJarak() sudah ada di includes/functions.php

// Include header
include_once '../includes/header.php';

// Cek apakah ada pesan error atau success dari process_barcode.php
$error_message = isset($_SESSION['error_message']) ? $_SESSION['error_message'] : '';
$success_message = isset($_SESSION['success_message']) ? $_SESSION['success_message'] : '';

// Hapus pesan dari session setelah diambil
unset($_SESSION['error_message']);
unset($_SESSION['success_message']);
?>

<!-- CSS sudah dipindahkan ke file mobile-scan-barcode.css -->

<div class="mobile-profile-container">
    <!-- Header Section -->
    <div class="profile-header">
        <div class="user-info">
            <div class="user-avatar">
                <?php if (!empty($user['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $user['foto_profil']; ?>" alt="Foto Profil" style="width: 100px; height: 100px; ">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo $user['nama']; ?></div>
                <div class="user-position"><?php echo $user['bidang'] ?? 'Karyawan'; ?></div>
            </div>
        </div>
        <div class="date-info">
            <div><i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?></div>
            <div><i class="fas fa-map-marker-alt"></i> <?php echo $user['nama_lokasi'] ? $user['nama_lokasi'] : 'Belum diatur'; ?></div>
        </div>
    </div>


    <div class="scan-container">
        <div class="section-title">
            <h4><i class="fas fa-qrcode me-2"></i> Scan Barcode Absensi</h4>
            <p class="text-muted">Pindai barcode untuk melakukan absensi</p>
        </div>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>Error:</strong> <?php echo $error_message; ?>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Berhasil!</strong> <?php echo $success_message; ?>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (!$allow_barcode): ?>
            <div class="alert alert-warning">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle me-3 fa-2x"></i>
                    <div>
                        <h5 class="mb-1">Akses Ditolak</h5>
                        <p class="mb-0">Anda tidak diizinkan menggunakan fitur absensi barcode. Silakan hubungi administrator.</p>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <a href="presensi.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i> Kembali ke Presensi
                </a>
            </div>
        <?php elseif (!$has_barcode): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i> Tidak ada konfigurasi barcode untuk lokasi Anda.
            </div>
        <?php elseif ($allow_flexible_schedule && !$selected_schedule): ?>
            <div class="alert alert-warning">
                <div class="d-flex align-items-center">
                    <i class="fas fa-clock me-3 fa-2x"></i>
                    <div>
                        <h5 class="mb-1">Pilih Jam Kerja Terlebih Dahulu</h5>
                        <p class="mb-0">Anda perlu memilih jam kerja fleksibel sebelum melakukan absensi barcode.</p>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <a href="presensi.php" class="btn btn-primary">
                    <i class="fas fa-clock me-2"></i> Pilih Jam Kerja
                </a>
            </div>
        <?php elseif ($sudah_absen_masuk && $sudah_absen_pulang): ?>
            <div class="alert alert-secondary">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle me-3 fa-2x"></i>
                    <div>
                        <h5 class="mb-1">Absensi Hari Ini Selesai</h5>
                        <p class="mb-0">Anda telah melakukan absensi masuk dan pulang hari ini.</p>
                    </div>
                </div>
                <div class="mt-3">
                    <p><strong>Jam Masuk:</strong> <?php echo $presensi_hari_ini['jam_masuk']; ?></p>
                    <p class="mb-0"><strong>Jam Pulang:</strong> <?php echo $presensi_hari_ini['jam_pulang']; ?></p>
                </div>
            </div>
            <div class="text-center mt-3">
                <a href="presensi.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i> Kembali ke Presensi
                </a>
            </div>
        <?php else: ?>
            <!-- Status Jangkauan di atas kamera -->
            <div id="radiusStatusTop" class="radius-status-top mb-3" style="display: none;">
                <div class="alert alert-info text-center mb-0">
                    <div class="d-flex align-items-center justify-content-center position-relative">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <span id="radiusStatusText">Mengecek lokasi...</span>
                        <span class="live-indicator position-absolute" style="top: -8px; right: -10px;">
                            <span class="badge bg-danger">
                                <i class="fas fa-circle" style="font-size: 6px; animation: blink 0.5s infinite;"></i>
                                LIVE
                            </span>
                        </span>
                    </div>
                </div>
            </div>

            <div id="scanner" class="scanner-container">
                <div id="scanner-overlay" class="scanner-overlay">
                    <div class="spinner-border text-primary mb-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>Memuat kamera...</div>
                </div>
                <div class="scanner-guide"></div>
                <div class="scanner-laser"></div>
                <div id="preview" class="preview-container"></div>
            </div>

            <div class="scan-actions">
                <div class="scan-status">
                    <div class="scanning-indicator">
                        <div class="spinner-border text-primary spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span>Memindai...</span>
                    </div>

                    <button id="toggleCamera" class="btn btn-danger btn-sm ms-2" style="display: none;">
                        <i class="fas fa-stop me-1"></i> Berhenti
                    </button>
                </div>

                <div class="absen-status">
                    <?php if (!$sudah_absen_masuk): ?>
                        <span class="badge bg-success"><i class="fas fa-sign-in-alt me-1"></i> Absen Masuk</span>
                    <?php elseif (!$sudah_absen_pulang): ?>
                        <span class="badge bg-danger"><i class="fas fa-sign-out-alt me-1"></i> Absen Pulang</span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="scan-result" id="scanResult">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i> Hasil Scan</h5>
                </div>
                <div id="resultContent"></div>
            </div>

            <div class="location-info">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i> Lokasi Anda</h5>
                </div>

                <div id="locationStatus">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span>Mendapatkan lokasi Anda...</span>
                    </div>
                </div>

                <div id="locationDetails" class="mt-3" style="display: none;">
                    <div class="card bg-light mb-2">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-5 text-muted">Lokasi</div>
                                <div class="col-7 fw-bold"><?php echo $user['nama_lokasi']; ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="card bg-light mb-2">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-5 text-muted">Jarak</div>
                                <div class="col-7 fw-bold">
                                    <span id="distance">Menghitung...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card bg-light">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-5 text-muted">Jam Kerja</div>
                                <div class="col-7 fw-bold">
                                    <?php if ($allow_flexible_schedule && $selected_schedule): ?>
                                        <div class="text-success mb-1">
                                            <i class="fas fa-check-circle"></i> <?php echo $selected_schedule['nama_jam_kerja']; ?>
                                        </div>
                                        <div>Masuk: <?php echo $jam_kerja['awal_jam_masuk']; ?> - <?php echo $jam_kerja['akhir_jam_masuk']; ?></div>
                                        <div>Pulang: <?php echo $jam_kerja['jam_pulang']; ?> - <?php echo $jam_kerja['akhir_jam_pulang']; ?></div>
                                    <?php elseif ($allow_flexible_schedule && !$selected_schedule): ?>
                                        <div class="text-warning">
                                            <i class="fas fa-clock"></i> Jam Kerja Fleksibel
                                        </div>
                                        <div class="small">Silakan pilih jam kerja di halaman presensi</div>
                                    <?php elseif ($jam_kerja): ?>
                                        <div>Masuk: <?php echo $jam_kerja['awal_jam_masuk']; ?> - <?php echo $jam_kerja['akhir_jam_masuk']; ?></div>
                                        <div>Pulang: <?php echo $jam_kerja['jam_pulang']; ?> - <?php echo $jam_kerja['akhir_jam_pulang']; ?></div>
                                    <?php else: ?>
                                        <span class="text-danger">Belum diatur</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Menyimpan data yang diperlukan tapi tidak ditampilkan -->
                    <div style="display: none;">
                        <span id="coordinates"></span>
                        <span id="accuracy"></span>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Floating button to go back to presensi page -->
<a href="index.php" class="floating-button">
    <i class="fas fa-arrow-left"></i>
</a>

<!-- Floating Map Button -->
<?php if ($has_barcode && $barcode_config): ?>
<div id="floating-map-btn" onclick="showLeafletMap()">
    <i class="fas fa-map-marked-alt"></i>
</div>
<?php else: ?>
<!-- Debug info -->
<script>
console.log('Floating map button not shown. Debug info:', {
    has_barcode: <?php echo $has_barcode ? 'true' : 'false'; ?>,
    barcode_config: <?php echo $barcode_config ? 'true' : 'false'; ?>,
    allow_barcode: <?php echo $allow_barcode ? 'true' : 'false'; ?>
});
</script>
<?php endif; ?>

<!-- Map functionality - Always load regardless of conditions -->
<script>
// Debug function untuk testing
function testMapFunction() {
    console.log('Map function test called');
    console.log('Current position:', currentPosition);
    console.log('Office location:', typeof officeLocation !== 'undefined' ? officeLocation : 'undefined');
    console.log('Leaflet available:', typeof L !== 'undefined');
}

// Fungsi showLeafletMap yang selalu tersedia
function showLeafletMap() {
    console.log('🗺️ showLeafletMap called');

    // Debug: Cek semua elemen yang diperlukan
    console.log('🔍 Debug check:');
    console.log('- currentPosition:', currentPosition);
    console.log('- currentPosition type:', typeof currentPosition);
    console.log('- officeLocation:', officeLocation);
    console.log('- Leaflet available:', typeof L !== 'undefined');
    console.log('- SweetAlert available:', typeof Swal !== 'undefined');

    // Cek apakah modal element ada
    const modal = document.getElementById('map-modal');
    console.log('- Modal element found:', modal !== null);

    if (!modal) {
        console.error('❌ Map modal element not found!');
        alert('Error: Modal map tidak ditemukan di halaman ini.');
        return;
    }

    <?php if ($has_barcode && $barcode_config): ?>
    // Tampilkan modal terlebih dahulu
    console.log('✅ Showing map modal');
    modal.style.display = 'block';
    modal.style.visibility = 'visible';
    modal.style.opacity = '1';
    modal.style.zIndex = '9999';
    modal.classList.add('show');
    isMapModalOpen = true;

    // Cek apakah lokasi tersedia
    if (!currentPosition || !currentPosition.coords) {
        console.log('⚠️ No current position available - showing placeholder');

        // Tampilkan placeholder di modal
        const mapBody = modal.querySelector('.map-modal-body');
        if (mapBody) {
            mapBody.innerHTML = '<div style="padding: 50px; text-align: center; color: #666;">' +
                '<i class="fas fa-map-marker-alt" style="font-size: 48px; color: #3498db; margin-bottom: 20px; animation: pulse 2s infinite;"></i>' +
                '<h4>Mendapatkan Lokasi...</h4>' +
                '<p>Mohon tunggu sementara kami mendapatkan lokasi Anda.</p>' +
                '<div class="spinner-border text-primary mt-3" role="status"></div>' +
                '<br><br>' +
                '<button class="btn btn-secondary mt-3" onclick="closeLeafletMap()">Tutup</button>' +
                '</div>';
        }

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'info',
                title: 'Mendapatkan Lokasi',
                text: 'Mohon tunggu sementara kami mendapatkan lokasi Anda. Modal map sudah terbuka.',
                confirmButtonColor: '#3085d6'
            });
        }
        return;
    }

    console.log('✅ Location available - initializing map');

    // Inisialisasi peta jika belum ada
    if (typeof leafletMap === 'undefined' || !leafletMap) {
        console.log('Initializing new map');

        // Buat peta
        leafletMap = L.map('leaflet-map').setView([currentPosition.coords.latitude, currentPosition.coords.longitude], 15);

        // Tambahkan layer peta
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(leafletMap);

        // Tambahkan marker untuk lokasi kantor
        var officeIcon = L.icon({
            iconUrl: '<?php echo BASE_URL; ?>assets/img/office-marker.png',
            iconSize: [32, 32],
            iconAnchor: [16, 32],
            popupAnchor: [0, -32]
        });

        if (typeof officeLocation !== 'undefined' && officeLocation.latitude && officeLocation.longitude) {
            officeMarker = L.marker([officeLocation.latitude, officeLocation.longitude], {
                icon: officeIcon
            }).addTo(leafletMap);
            officeMarker.bindPopup('<b>' + officeLocation.name + '</b><br>' + officeLocation.latitude + ', ' + officeLocation.longitude);

            // Tambahkan lingkaran radius
            radiusCircle = L.circle([officeLocation.latitude, officeLocation.longitude], {
                radius: officeLocation.radius,
                color: '#4e73df',
                fillColor: '#4e73df',
                fillOpacity: 0.2
            }).addTo(leafletMap);
        }

        // Tambahkan marker untuk lokasi pengguna
        var userIcon = L.icon({
            iconUrl: '<?php echo BASE_URL; ?>assets/img/user-marker.png',
            iconSize: [32, 32],
            iconAnchor: [16, 32],
            popupAnchor: [0, -32]
        });

        userMarker = L.marker([currentPosition.coords.latitude, currentPosition.coords.longitude], {
            icon: userIcon
        }).addTo(leafletMap);
        userMarker.bindPopup('<b>Lokasi Anda</b><br>' + currentPosition.coords.latitude + ', ' + currentPosition.coords.longitude);

        // Tambahkan garis antara lokasi pengguna dan kantor
        if (typeof officeLocation !== 'undefined' && officeLocation.latitude && officeLocation.longitude) {
            mapLine = L.polyline([
                [currentPosition.coords.latitude, currentPosition.coords.longitude],
                [officeLocation.latitude, officeLocation.longitude]
            ], {
                color: '#36b9cc',
                weight: 3,
                opacity: 0.7,
                dashArray: '10, 10'
            }).addTo(leafletMap);

            // Tambahkan informasi jarak
            var distance = calculateDistance(
                currentPosition.coords.latitude,
                currentPosition.coords.longitude,
                officeLocation.latitude,
                officeLocation.longitude
            );

            var distanceInfo = L.control({position: 'bottomleft'});
            distanceInfo.onAdd = function(map) {
                this._div = L.DomUtil.create('div', 'distance-info');
                this.update();
                return this._div;
            };
            distanceInfo.update = function() {
                this._div.innerHTML = '<div style="background: white; padding: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">' +
                    '<strong>Jarak: ' + Math.round(distance) + ' meter</strong><br>' +
                    '<small>Radius: ' + officeLocation.radius + ' meter</small>' +
                    '</div>';
            };
            distanceInfo.addTo(leafletMap);

            // Sesuaikan tampilan agar semua marker terlihat
            var bounds = L.latLngBounds([
                [currentPosition.coords.latitude, currentPosition.coords.longitude],
                [officeLocation.latitude, officeLocation.longitude]
            ]);
            leafletMap.fitBounds(bounds.pad(0.3));
        } else {
            // Jika tidak ada office location, center ke user location
            leafletMap.setView([currentPosition.coords.latitude, currentPosition.coords.longitude], 15);
        }
    } else {
        console.log('Updating existing map');
        // Update posisi marker pengguna jika peta sudah ada
        if (typeof userMarker !== 'undefined' && userMarker) {
            userMarker.setLatLng([currentPosition.coords.latitude, currentPosition.coords.longitude]);
            userMarker.bindPopup('<b>Lokasi Anda</b><br>' + currentPosition.coords.latitude + ', ' + currentPosition.coords.longitude);
        }

        // Update garis
        if (typeof mapLine !== 'undefined' && mapLine && typeof officeLocation !== 'undefined') {
            mapLine.setLatLngs([
                [currentPosition.coords.latitude, currentPosition.coords.longitude],
                [officeLocation.latitude, officeLocation.longitude]
            ]);
        }
    }

    <?php else: ?>
    console.log('⚠️ Map not available - barcode config missing');

    // Tampilkan modal sederhana untuk testing
    console.log('🧪 Showing test modal anyway for debugging');
    modal.style.display = 'block';
    modal.style.visibility = 'visible';
    modal.style.opacity = '1';
    modal.style.zIndex = '9999';
    modal.classList.add('show');

    // Tampilkan pesan error di modal
    const mapBody = modal.querySelector('.map-modal-body');
    if (mapBody) {
        mapBody.innerHTML = '<div style="padding: 50px; text-align: center; color: #666;">' +
            '<i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #f39c12; margin-bottom: 20px;"></i>' +
            '<h4>Map Tidak Tersedia</h4>' +
            '<p>Konfigurasi barcode belum diatur untuk lokasi Anda.</p>' +
            '<button class="btn btn-secondary" onclick="closeLeafletMap()">Tutup</button>' +
            '</div>';
    }

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Map Tidak Tersedia',
            text: 'Konfigurasi barcode belum diatur untuk lokasi Anda.',
            confirmButtonColor: '#3085d6'
        });
    }
    return;
    <?php endif; ?>
}

// Variabel global untuk map dan location tracking
var leafletMap = null;
var userMarker = null;
var officeMarker = null;
var radiusCircle = null;
var mapLine = null;
var isMapModalOpen = false;
var currentPosition = null; // Global variable untuk current position

// Data lokasi untuk map
<?php if ($has_barcode && $barcode_config): ?>
var officeLocation = {
    latitude: <?php echo isset($barcode_config['latitude']) ? $barcode_config['latitude'] : 'null'; ?>,
    longitude: <?php echo isset($barcode_config['longitude']) ? $barcode_config['longitude'] : 'null'; ?>,
    radius: <?php echo isset($lokasi['radius']) ? $lokasi['radius'] : '0'; ?>,
    name: '<?php echo addslashes(isset($barcode_config['nama_lokasi']) ? $barcode_config['nama_lokasi'] : 'Lokasi Kantor'); ?>'
};
<?php else: ?>
var officeLocation = null;
<?php endif; ?>

// Fungsi untuk menutup peta Leaflet
function closeLeafletMap() {
    console.log('🚪 Closing map modal');
    const modal = document.getElementById('map-modal');
    if (modal) {
        modal.style.display = 'none';
        modal.style.visibility = 'hidden';
        modal.style.opacity = '0';
        modal.classList.remove('show');
        console.log('✅ Modal closed');
    } else {
        console.error('❌ Modal element not found when trying to close');
    }
    isMapModalOpen = false;
}

// Fungsi untuk membuka peta lokasi di Google Maps
function openLocationMap() {
    if (!currentPosition || !currentPosition.coords) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'warning',
                title: 'Lokasi Belum Tersedia',
                text: 'Mohon tunggu sementara kami mendapatkan lokasi Anda.',
                confirmButtonColor: '#3085d6'
            });
        } else {
            alert('Lokasi Anda belum tersedia. Mohon tunggu beberapa saat.');
        }
        return;
    }

    if (!officeLocation || !officeLocation.latitude || !officeLocation.longitude) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'Lokasi Kantor Tidak Tersedia',
                text: 'Data lokasi kantor belum dikonfigurasi.',
                confirmButtonColor: '#3085d6'
            });
        } else {
            alert('Lokasi kantor tidak tersedia.');
        }
        return;
    }

    // Buat URL untuk Google Maps dengan lokasi pengguna dan lokasi kantor
    var mapUrl = 'https://www.google.com/maps/dir/?api=1&origin=' +
                 currentPosition.coords.latitude + ',' + currentPosition.coords.longitude +
                 '&destination=' + officeLocation.latitude + ',' + officeLocation.longitude;

    // Buka di tab baru
    window.open(mapUrl, '_blank');
}

// Tutup modal jika user klik di luar modal
window.onclick = function(event) {
    var modal = document.getElementById('map-modal');
    if (event.target == modal) {
        closeLeafletMap();
    }
}

// Fungsi untuk update modal map ketika lokasi tersedia
function updateMapModal() {
    if (isMapModalOpen && currentPosition && currentPosition.coords) {
        console.log('🔄 Updating map modal with new location');
        showLeafletMap(); // Re-initialize map dengan lokasi baru
    }
}

// Test function untuk debugging
window.testMap = testMapFunction;
window.updateMapModal = updateMapModal;

// Tambahkan tombol test untuk debugging
document.addEventListener('DOMContentLoaded', function() {
    // Tambahkan tombol test di pojok kiri atas
    const testButton = document.createElement('button');
    
    testButton.onclick = function() {
        console.log('🧪 Test button clicked');
        showLeafletMap();
    };
    document.body.appendChild(testButton);

    // Log info saat halaman dimuat
    console.log('🚀 Page loaded - Map debugging info:');
    console.log('- Floating button exists:', document.getElementById('floating-map-btn') !== null);
    console.log('- Modal exists:', document.getElementById('map-modal') !== null);
    console.log('- showLeafletMap function:', typeof showLeafletMap);
    console.log('- closeLeafletMap function:', typeof closeLeafletMap);
});
</script>

<!-- Bottom navigation removed -->

<!-- Memuat library HTML5-QR-Code dari beberapa sumber untuk keandalan -->
<script>
    // Fungsi untuk memeriksa apakah library HTML5QrCode sudah dimuat
    function isHtml5QrCodeLoaded() {
        return typeof Html5Qrcode !== 'undefined';
    }

    // Fungsi untuk memuat script dari URL
    function loadScript(url, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load script from:', url);
            if (callback) callback(new Error('Failed to load script'));
        };
        document.head.appendChild(script);
    }

    // Daftar sumber library HTML5-QR-Code
    var scriptSources = [
        '../assets/js/html5-qrcode.min.js', // Prioritaskan file lokal
        'https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js',
        'https://cdn.jsdelivr.net/npm/html5-qrcode@2.3.8/html5-qrcode.min.js'
    ];

    // Fungsi untuk mencoba memuat script dari sumber berikutnya
    function tryLoadingFromNextSource(index) {
        if (isHtml5QrCodeLoaded()) {
            console.log('HTML5QrCode library already loaded');
            return;
        }

        if (index >= scriptSources.length) {
            console.error('Failed to load HTML5QrCode library from all sources');
            alert('Gagal memuat library pemindai barcode. Silakan muat ulang halaman atau coba browser lain.');
            return;
        }

        loadScript(scriptSources[index], function(error) {
            if (!error && isHtml5QrCodeLoaded()) {
                console.log('HTML5QrCode library loaded successfully from:', scriptSources[index]);
            } else {
                console.warn('Trying next source...');
                tryLoadingFromNextSource(index + 1);
            }
        });
    }

    // Mulai memuat library
    tryLoadingFromNextSource(0);
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update jam secara real-time
    function updateJam() {
        var now = new Date();
        var hours = now.getHours().toString().padStart(2, '0');
        var minutes = now.getMinutes().toString().padStart(2, '0');
        var seconds = now.getSeconds().toString().padStart(2, '0');

        // Update semua elemen dengan id jam-sekarang
        var elements = document.querySelectorAll('#jam-sekarang');
        elements.forEach(function(element) {
            element.textContent = hours + ':' + minutes + ':' + seconds;
        });
    }

    // Update jam setiap detik
    setInterval(updateJam, 1000);
    updateJam();
    const scanner = document.getElementById('scanner');
    const scannerOverlay = document.getElementById('scanner-overlay');
    const toggleButton = document.getElementById('toggleCamera');
    const scanResult = document.getElementById('scanResult');
    const resultContent = document.getElementById('resultContent');
    const locationStatus = document.getElementById('locationStatus');
    const locationDetails = document.getElementById('locationDetails');
    const coordinatesElement = document.getElementById('coordinates');
    const accuracyElement = document.getElementById('accuracy');

    let html5QrCode;
    let scanning = false;
    // currentPosition sudah didefinisikan sebagai global variable
    let lastRadiusStatus = null; // Untuk tracking perubahan status

    // Audio dan haptic feedback untuk perubahan status
    function playStatusFeedback(status) {
        // Audio feedback
        try {
            // Buat audio context untuk suara notifikasi
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // Frekuensi berbeda untuk setiap status
            if (status === 'in') {
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // Nada tinggi untuk masuk area
                oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
            } else if (status === 'out') {
                oscillator.frequency.setValueAtTime(400, audioContext.currentTime); // Nada rendah untuk keluar area
                oscillator.frequency.setValueAtTime(300, audioContext.currentTime + 0.1);
            } else if (status === 'warning') {
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime); // Nada sedang untuk warning
            }

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        } catch (e) {
            console.log('Audio feedback tidak tersedia:', e);
        }

        // Haptic feedback (vibration) untuk perangkat mobile
        try {
            if (navigator.vibrate) {
                if (status === 'in') {
                    navigator.vibrate([100, 50, 100]); // Pola vibration untuk masuk area
                } else if (status === 'out') {
                    navigator.vibrate([200, 100, 200, 100, 200]); // Pola vibration untuk keluar area
                } else if (status === 'warning') {
                    navigator.vibrate([150]); // Vibration singkat untuk warning
                }
            }
        } catch (e) {
            console.log('Haptic feedback tidak tersedia:', e);
        }
    }

    // Initialize location tracking dengan real-time monitoring super responsif
    let watchId = null;
    let lastUpdateTime = 0;
    const UPDATE_INTERVAL = 500; // Update setiap 0.5 detik untuk responsivitas maksimal

    if (navigator.geolocation) {
        // Konfigurasi untuk tracking real-time yang sangat responsif
        const geoOptions = {
            enableHighAccuracy: true,
            maximumAge: 0, // Tidak menggunakan cache, selalu data fresh
            timeout: 5000 // Timeout lebih cepat 5 detik
        };

        // Mulai tracking lokasi real-time
        watchId = navigator.geolocation.watchPosition(
            function(position) {
                const now = Date.now();

                // Update langsung tanpa throttling untuk responsivitas maksimal
                // Hanya throttle jika update terlalu cepat (kurang dari interval minimum)
                if (now - lastUpdateTime >= UPDATE_INTERVAL) {
                    currentPosition = position;
                    updateLocationInfo(position);
                    lastUpdateTime = now;

                    console.log('🚀 High-speed location update:', {
                        lat: position.coords.latitude.toFixed(6),
                        lng: position.coords.longitude.toFixed(6),
                        accuracy: Math.round(position.coords.accuracy) + 'm',
                        speed: position.coords.speed ? Math.round(position.coords.speed * 3.6) + 'km/h' : 'N/A',
                        timestamp: new Date(position.timestamp).toLocaleTimeString()
                    });
                } else {
                    // Update posisi tanpa UI update untuk tracking internal
                    currentPosition = position;
                }
            },
            function(error) {
                console.error('Geolocation error:', error);
                locationStatus.innerHTML = `<div class="alert alert-danger">Error: ${getLocationErrorMessage(error)}</div>`;

                // Tampilkan status error di atas kamera juga
                const radiusStatusTop = document.getElementById('radiusStatusTop');
                const radiusStatusText = document.getElementById('radiusStatusText');
                if (radiusStatusTop && radiusStatusText) {
                    radiusStatusTop.style.display = 'block';
                    const alertElement = radiusStatusTop.querySelector('.alert');
                    alertElement.className = 'alert alert-danger text-center mb-0';
                    radiusStatusText.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Error GPS';
                }
            },
            geoOptions
        );

        // Backup: Continuous tracking dengan getCurrentPosition untuk memastikan update terus berjalan
        const fallbackLocationUpdate = setInterval(() => {
            if (!currentPosition || (Date.now() - lastUpdateTime) > 3000) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        currentPosition = position;
                        updateLocationInfo(position);
                        lastUpdateTime = Date.now();
                        console.log('⚡ Fallback high-speed update');
                    },
                    function(error) {
                        console.warn('Fallback geolocation failed:', error);
                    },
                    geoOptions
                );
            }
        }, 1000); // Cek setiap 1 detik untuk backup yang lebih cepat

        // Continuous tracking tambahan untuk memastikan tidak ada gap
        const continuousTracking = setInterval(() => {
            if (currentPosition) {
                // Force update UI jika ada perubahan signifikan
                const now = Date.now();
                if (now - lastUpdateTime >= UPDATE_INTERVAL) {
                    updateLocationInfo(currentPosition);
                    lastUpdateTime = now;
                }
            }
        }, 250); // Update UI setiap 250ms untuk smooth tracking

        // Cleanup saat halaman ditutup
        window.addEventListener('beforeunload', function() {
            if (watchId) {
                navigator.geolocation.clearWatch(watchId);
            }
            clearInterval(fallbackLocationUpdate);
            clearInterval(continuousTracking);
            console.log('🛑 High-speed tracking stopped');
        });

        // Cleanup saat halaman tidak terlihat (tab switch, minimize, dll)
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                console.log('📱 Page hidden - reducing tracking frequency');
                // Bisa mengurangi frekuensi saat tidak terlihat untuk menghemat battery
            } else {
                console.log('📱 Page visible - resuming high-speed tracking');
                // Resume full speed tracking saat kembali terlihat
            }
        });

    } else {
        locationStatus.innerHTML = '<div class="alert alert-danger">Geolokasi tidak didukung oleh browser Anda.</div>';

        // Tampilkan status error di atas kamera juga
        const radiusStatusTop = document.getElementById('radiusStatusTop');
        const radiusStatusText = document.getElementById('radiusStatusText');
        if (radiusStatusTop && radiusStatusText) {
            radiusStatusTop.style.display = 'block';
            const alertElement = radiusStatusTop.querySelector('.alert');
            alertElement.className = 'alert alert-danger text-center mb-0';
            radiusStatusText.innerHTML = '<i class="fas fa-times-circle me-2"></i>GPS Tidak Didukung';
        }
    }

    // Function to update location info
    function updateLocationInfo(position) {
        const latitude = position.coords.latitude;
        const longitude = position.coords.longitude;
        const accuracy = Math.round(position.coords.accuracy);
        const timestamp = new Date().toLocaleTimeString();

        coordinatesElement.textContent = `${latitude}, ${longitude}`;
        accuracyElement.textContent = accuracy;

        // Update status dengan indikator real-time high-speed
        locationStatus.innerHTML = `
            <div class="alert alert-success">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <i class="fas fa-check-circle me-2"></i>Lokasi Terdeteksi
                        <span class="badge bg-success ms-2" style="font-size: 9px;">
                            <i class="fas fa-tachometer-alt me-1"></i>HIGH-SPEED
                        </span>
                    </div>
                    <div class="small">
                        <i class="fas fa-sync-alt me-1 text-success" style="animation: spin 0.5s linear infinite;"></i>
                        ${timestamp}
                    </div>
                </div>
                <div class="mt-2 small text-muted">
                    <i class="fas fa-bolt me-1"></i>Update setiap 0.5 detik | Akurasi: ${accuracy}m
                </div>
            </div>
        `;
        locationDetails.style.display = 'block';

        // Hitung jarak ke lokasi barcode
        <?php if ($has_barcode): ?>
        const barcodeLatitude = <?php echo $barcode_config['latitude']; ?>;
        const barcodeLongitude = <?php echo $barcode_config['longitude']; ?>;
        const allowedRadius = <?php echo $barcode_config['radius']; ?>;

        // Hitung jarak menggunakan Haversine formula
        const distance = calculateDistance(
            latitude,
            longitude,
            barcodeLatitude,
            barcodeLongitude
        );

        // Update elemen jarak
        const distanceElement = document.getElementById('distance');
        const radiusStatusTop = document.getElementById('radiusStatusTop');
        const radiusStatusText = document.getElementById('radiusStatusText');

        if (distanceElement) {
            distanceElement.textContent = `${Math.round(distance)} meter`;
        }

        // Update status radius di atas kamera dengan animasi smooth
        if (radiusStatusTop && radiusStatusText) {
            radiusStatusTop.style.display = 'block';

            const alertElement = radiusStatusTop.querySelector('.alert');
            const currentClass = alertElement.className;
            let newClass, newText, statusIcon, currentStatus;

            // Tentukan status berdasarkan jarak
            if (distance <= allowedRadius) {
                newClass = 'alert alert-success text-center mb-0';
                statusIcon = 'fas fa-check-circle';
                newText = `<i class="${statusIcon} me-2"></i>Dalam Jangkauan <small class="ms-2">(${Math.round(distance)}m)</small>`;
                currentStatus = 'in';
            } else if (distance <= allowedRadius * 1.2) {
                newClass = 'alert alert-warning text-center mb-0';
                statusIcon = 'fas fa-exclamation-triangle';
                newText = `<i class="${statusIcon} me-2"></i>Mendekati Batas <small class="ms-2">(${Math.round(distance)}m)</small>`;
                currentStatus = 'warning';
            } else {
                newClass = 'alert alert-danger text-center mb-0';
                statusIcon = 'fas fa-times-circle';
                newText = `<i class="${statusIcon} me-2"></i>Di Luar Jangkauan <small class="ms-2">(${Math.round(distance)}m)</small>`;
                currentStatus = 'out';
            }

            // Play audio dan haptic feedback jika status berubah
            if (lastRadiusStatus !== null && lastRadiusStatus !== currentStatus) {
                playStatusFeedback(currentStatus);

                // Tampilkan notifikasi visual untuk perubahan status
                if (currentStatus === 'in' && lastRadiusStatus === 'out') {
                    console.log('🟢 Masuk ke dalam jangkauan!');
                } else if (currentStatus === 'out' && lastRadiusStatus === 'in') {
                    console.log('🔴 Keluar dari jangkauan!');
                } else if (currentStatus === 'warning') {
                    console.log('🟡 Mendekati batas jangkauan!');
                }
            }

            lastRadiusStatus = currentStatus;

            // Animasi transisi jika status berubah
            if (currentClass !== newClass) {
                // Fade out
                alertElement.style.opacity = '0.5';
                alertElement.style.transform = 'scale(0.95)';

                setTimeout(() => {
                    // Update class dan text
                    alertElement.className = newClass;
                    radiusStatusText.innerHTML = newText;

                    // Fade in
                    alertElement.style.opacity = '1';
                    alertElement.style.transform = 'scale(1)';

                    // Tambahkan efek bounce cepat untuk perubahan status
                    alertElement.style.animation = 'quickStatusChange 0.4s ease-out';
                    setTimeout(() => {
                        alertElement.style.animation = '';
                    }, 400);
                }, 200);
            } else {
                // Update text saja jika status sama (untuk update jarak)
                radiusStatusText.innerHTML = newText;
            }
        }

        // Update map jika modal sedang terbuka
        if (isMapModalOpen && leafletMap && userMarker) {
            // Update posisi marker pengguna di map
            userMarker.setLatLng([latitude, longitude]);
            userMarker.bindPopup('<b>Lokasi Anda</b><br>' + latitude.toFixed(6) + ', ' + longitude.toFixed(6));

            // Update garis
            if (mapLine) {
                mapLine.setLatLngs([
                    [latitude, longitude],
                    [officeLocation.latitude, officeLocation.longitude]
                ]);
            }

            // Update informasi jarak di map
            const distance = calculateDistance(
                latitude,
                longitude,
                officeLocation.latitude,
                officeLocation.longitude
            );

            // Update distance info control jika ada
            if (leafletMap._container.querySelector('.distance-info')) {
                const distanceDiv = leafletMap._container.querySelector('.distance-info div');
                if (distanceDiv) {
                    distanceDiv.innerHTML = '<strong>Jarak: ' + Math.round(distance) + ' meter</strong><br>' +
                        '<small>Radius: ' + officeLocation.radius + ' meter</small>';
                }
            }
        } else if (isMapModalOpen && !leafletMap) {
            // Jika modal terbuka tapi map belum diinisialisasi, coba inisialisasi ulang
            console.log('🔄 Modal open but map not initialized, trying to reinitialize...');
            updateMapModal();
        }
        <?php endif; ?>
    }

    // Function to calculate distance between two coordinates in meters (Haversine formula)
    function calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371000; // Radius of the earth in meters
        const dLat = deg2rad(lat2 - lat1);
        const dLon = deg2rad(lon2 - lon1);
        const a =
            Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
            Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        const distance = R * c; // Distance in meters
        return distance;
    }

    // Helper function to convert degrees to radians
    function deg2rad(deg) {
        return deg * (Math.PI/180);
    }

    // Function to get location error message
    function getLocationErrorMessage(error) {
        switch(error.code) {
            case error.PERMISSION_DENIED:
                return "Izin akses lokasi ditolak. Silakan aktifkan GPS dan izinkan akses lokasi.";
            case error.POSITION_UNAVAILABLE:
                return "Informasi lokasi tidak tersedia. Pastikan GPS Anda aktif.";
            case error.TIMEOUT:
                return "Waktu permintaan lokasi habis. Silakan coba lagi.";
            case error.UNKNOWN_ERROR:
                return "Terjadi kesalahan yang tidak diketahui.";
            default:
                return "Terjadi kesalahan saat mendapatkan lokasi.";
        }
    }

    // Function to handle scan failure
    function onScanFailure(error) {
        // Tidak perlu melakukan apa-apa, ini normal selama pemindaian
        // console.log('Scan error: ', error);
    }

    // Toggle camera button (now only for stopping)
    toggleButton.addEventListener('click', function() {
        if (scanning) {
            stopScanner();
            // Setelah berhenti, tampilkan pesan dan tombol untuk memulai kembali
            scannerOverlay.innerHTML = `
                <div class="text-center">
                    <div class="mb-3">Kamera dimatikan</div>
                    <button class="btn btn-primary" onclick="startScannerAgain()">
                        <i class="fas fa-camera me-2"></i> Mulai Scan Lagi
                    </button>
                </div>
            `;
            toggleButton.style.display = 'none';
        }
    });

    // Function to restart scanner
    window.startScannerAgain = function() {
        // Bersihkan instance sebelumnya jika ada
        if (html5QrCode && html5QrCode.isScanning) {
            try {
                html5QrCode.stop().then(() => {
                    console.log("Stopped previous scanner instance");
                    html5QrCode = null;
                    startScanner();
                }).catch(err => {
                    console.error("Error stopping previous scanner:", err);
                    html5QrCode = null;
                    startScanner();
                });
            } catch (e) {
                console.error("Error in cleanup:", e);
                html5QrCode = null;
                startScanner();
            }
        } else {
            startScanner();
        }
    };

    // Start scanner
    function startScanner() {
        // Periksa apakah library HTML5-QR-Code sudah dimuat
        if (!isHtml5QrCodeLoaded()) {
            console.error("HTML5QrCode library not loaded yet");
            scannerOverlay.innerHTML = `
                <div class="alert alert-danger mb-3">
                    <i class="fas fa-exclamation-circle me-2"></i> Library Tidak Dimuat
                </div>
                <p class="mb-3">Library pemindai barcode belum dimuat. Silakan tunggu beberapa saat atau muat ulang halaman.</p>
                <button class="btn btn-primary" onclick="checkAndStartScanner()">
                    <i class="fas fa-redo me-2"></i> Coba Lagi
                </button>
                <button class="btn btn-secondary ms-2" onclick="location.reload()">
                    <i class="fas fa-sync me-2"></i> Muat Ulang Halaman
                </button>
            `;
            return;
        }

        // Tampilkan pesan loading yang lebih informatif
        scannerOverlay.innerHTML = `
            <div class="d-flex flex-column align-items-center justify-content-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="mb-2">Memuat kamera...</div>
                <div class="small text-muted">Mohon tunggu beberapa saat</div>
            </div>
        `;

        // Tambahkan timeout untuk memuat kamera dengan waktu yang lebih lama
        let cameraTimeout = setTimeout(() => {
            scannerOverlay.innerHTML = `
                <div class="alert alert-warning mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i> Waktu memuat kamera habis
                </div>
                <p class="mb-3">Kamera tidak dapat dimuat dalam waktu yang ditentukan.</p>
                <button class="btn btn-primary" onclick="startScannerAgain()">
                    <i class="fas fa-redo me-2"></i> Coba Lagi
                </button>
            `;
        }, 20000); // 20 detik timeout

        try {
            // Pastikan elemen preview ada
            const previewElement = document.getElementById("preview");
            if (!previewElement) {
                throw new Error("Elemen preview tidak ditemukan");
            }

            // Bersihkan elemen preview terlebih dahulu
            previewElement.innerHTML = '';

            // Tambahkan kelas untuk styling
            previewElement.classList.add('preview-container');

            // Buat instance baru dengan konfigurasi yang lebih baik
            html5QrCode = new Html5Qrcode("preview");

            // Konfigurasi scanner dengan nilai yang lebih sederhana
            const config = {
                fps: 10,
                qrbox: 250,
                formatsToSupport: ['CODE_128', 'CODE_39', 'QR_CODE', 'EAN_13']
            };

            // Konfigurasi kamera - gunakan pendekatan yang lebih sederhana
            const cameraConfig = {
                facingMode: "environment"
            };

            console.log("Starting camera with config:", config);

            // Coba dapatkan daftar kamera terlebih dahulu
            Html5Qrcode.getCameras().then(devices => {
                console.log("Kamera tersedia:", devices);

                // Gunakan kamera belakang jika tersedia
                let cameraId = null;
                if (devices && devices.length) {
                    // Prioritaskan kamera belakang
                    for (const device of devices) {
                        if (device.label.toLowerCase().includes("back") ||
                            device.label.toLowerCase().includes("belakang")) {
                            cameraId = device.id;
                            break;
                        }
                    }
                    // Jika tidak ada kamera belakang, gunakan kamera pertama
                    if (!cameraId) {
                        cameraId = devices[0].id;
                    }
                }

                // Debug: Log event handlers
                console.log('🔧 Setting up scanner with event handlers:');
                console.log('- onScanSuccess:', typeof onScanSuccess);
                console.log('- onScanFailure:', typeof onScanFailure);

                // Mulai scanner dengan kamera yang dipilih atau konfigurasi default
                return html5QrCode.start(
                    cameraId || cameraConfig,
                    config,
                    onScanSuccess,
                    onScanFailure
                );
            }).then(() => {
                // Berhasil memulai kamera, hapus timeout
                clearTimeout(cameraTimeout);
                console.log("✅ Camera started successfully");
                console.log("📷 Scanner is now ready to scan barcodes");
                console.log("🎯 onScanSuccess function is registered and waiting for scans");
                scanning = true;
                scannerOverlay.style.display = 'none';
                toggleButton.style.display = 'block';

                // Tambahkan kelas untuk styling video dan canvas
                setTimeout(() => {
                    const videoElements = document.querySelectorAll('#preview video');
                    const canvasElements = document.querySelectorAll('#preview canvas');

                    videoElements.forEach(video => {
                        video.style.width = '100%';
                        video.style.height = '100%';
                        video.style.objectFit = 'cover';
                    });

                    canvasElements.forEach(canvas => {
                        canvas.style.width = '100%';
                        canvas.style.height = '100%';
                        canvas.style.objectFit = 'cover';
                    });
                }, 500);
            }).catch((err) => {
                // Gagal memulai kamera, hapus timeout
                clearTimeout(cameraTimeout);
                console.error("Camera start error:", err);

                // Tampilkan pesan error yang lebih informatif
                let errorMessage = "Tidak dapat mengakses kamera";

                if (err.toString().includes("NotAllowedError")) {
                    errorMessage = "Izin kamera ditolak. Silakan izinkan akses kamera di pengaturan browser Anda.";
                } else if (err.toString().includes("NotFoundError")) {
                    errorMessage = "Kamera tidak ditemukan. Pastikan perangkat Anda memiliki kamera yang berfungsi.";
                } else if (err.toString().includes("NotReadableError")) {
                    errorMessage = "Kamera sedang digunakan oleh aplikasi lain. Tutup aplikasi lain yang mungkin menggunakan kamera.";
                } else if (err.toString().includes("OverconstrainedError")) {
                    errorMessage = "Kamera tidak mendukung resolusi yang diminta. Coba gunakan kamera lain.";
                } else if (err.toString().includes("TypeError")) {
                    errorMessage = "Terjadi kesalahan saat mengakses kamera. Coba muat ulang halaman atau gunakan browser lain.";
                }

                // Coba fallback ke metode lain jika gagal dengan getCameras
                if (err.toString().includes("TypeError") || err.toString().includes("undefined")) {
                    console.log("Mencoba metode fallback...");
                    try {
                        // Coba langsung dengan facingMode tanpa getCameras
                        html5QrCode.start(
                            { facingMode: "environment" },
                            config,
                            onScanSuccess,
                            onScanFailure
                        ).then(() => {
                            clearTimeout(cameraTimeout);
                            console.log("Camera started with fallback method");
                            scanning = true;
                            scannerOverlay.style.display = 'none';
                            toggleButton.style.display = 'block';
                            return;
                        }).catch(fallbackErr => {
                            console.error("Fallback method failed:", fallbackErr);
                            // Lanjutkan ke tampilan error di bawah
                        });
                    } catch (fallbackErr) {
                        console.error("Error in fallback method:", fallbackErr);
                    }
                }

                scannerOverlay.innerHTML = `
                    <div class="alert alert-danger mb-3">
                        <i class="fas fa-camera-slash me-2"></i> Error Kamera
                    </div>
                    <p class="mb-3">${errorMessage}</p>
                    <button class="btn btn-primary" onclick="startScannerAgain()">
                        <i class="fas fa-redo me-2"></i> Coba Lagi
                    </button>
                `;
            });
        } catch (error) {
            // Error saat inisialisasi, hapus timeout
            clearTimeout(cameraTimeout);
            console.error("Scanner initialization error:", error);

            scannerOverlay.innerHTML = `
                <div class="alert alert-danger mb-3">
                    <i class="fas fa-exclamation-circle me-2"></i> Error Inisialisasi
                </div>
                <p class="mb-3">Terjadi kesalahan saat menginisialisasi pemindai: ${error.message}</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-redo me-2"></i> Muat Ulang Halaman
                </button>
            `;
        }
    }

    // Fungsi untuk memeriksa library dan memulai scanner
    window.checkAndStartScanner = function() {
        if (isHtml5QrCodeLoaded()) {
            startScanner();
        } else {
            scannerOverlay.innerHTML = `
                <div class="alert alert-info mb-3">
                    <i class="fas fa-spinner fa-spin me-2"></i> Memuat Library
                </div>
                <p class="mb-3">Sedang memuat library pemindai barcode. Silakan tunggu...</p>
            `;

            // Coba lagi dalam 2 detik
            setTimeout(function() {
                if (isHtml5QrCodeLoaded()) {
                    startScanner();
                } else {
                    // Coba muat library lagi
                    tryLoadingFromNextSource(0);

                    // Coba lagi dalam 3 detik
                    setTimeout(function() {
                        if (isHtml5QrCodeLoaded()) {
                            startScanner();
                        } else {
                            scannerOverlay.innerHTML = `
                                <div class="alert alert-danger mb-3">
                                    <i class="fas fa-exclamation-circle me-2"></i> Gagal Memuat Library
                                </div>
                                <p class="mb-3">Tidak dapat memuat library pemindai barcode. Silakan coba lagi nanti.</p>
                                <button class="btn btn-primary" onclick="location.reload()">
                                    <i class="fas fa-sync me-2"></i> Muat Ulang Halaman
                                </button>
                            `;
                        }
                    }, 3000);
                }
            }, 2000);
        }
    }

    // Stop scanner
    function stopScanner() {
        if (html5QrCode) {
            try {
                if (html5QrCode.isScanning) {
                    console.log("Stopping camera...");
                    html5QrCode.stop().then(() => {
                        console.log("Camera stopped successfully");
                        scanning = false;

                        // Bersihkan elemen preview
                        const previewElement = document.getElementById("preview");
                        if (previewElement) {
                            previewElement.innerHTML = '';
                        }

                        // Tampilkan overlay
                        scannerOverlay.style.display = 'flex';
                        scannerOverlay.innerHTML = `
                            <div class="text-center">
                                <div class="mb-3">Kamera dimatikan</div>
                                <button class="btn btn-primary" onclick="startScannerAgain()">
                                    <i class="fas fa-camera me-2"></i> Mulai Scan Lagi
                                </button>
                            </div>
                        `;

                        // Lepaskan sumber daya kamera
                        setTimeout(() => {
                            try {
                                // Hapus semua elemen video dan canvas yang mungkin masih ada
                                const videoElements = document.querySelectorAll('video');
                                const canvasElements = document.querySelectorAll('canvas');

                                videoElements.forEach(video => {
                                    if (video.srcObject) {
                                        const tracks = video.srcObject.getTracks();
                                        tracks.forEach(track => track.stop());
                                        video.srcObject = null;
                                    }
                                    if (video.parentNode) {
                                        video.parentNode.removeChild(video);
                                    }
                                });

                                canvasElements.forEach(canvas => {
                                    if (canvas.parentNode && canvas.parentNode.id === 'preview') {
                                        canvas.parentNode.removeChild(canvas);
                                    }
                                });

                                html5QrCode = null;
                            } catch (e) {
                                console.error('Error releasing camera resources:', e);
                            }
                        }, 500);
                    }).catch((err) => {
                        console.error('Error stopping scanner:', err);
                        scanning = false;
                        scannerOverlay.style.display = 'flex';
                        scannerOverlay.innerHTML = `
                            <div class="alert alert-warning mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i> Gagal mematikan kamera
                            </div>
                            <button class="btn btn-primary" onclick="location.reload()">
                                <i class="fas fa-redo me-2"></i> Muat Ulang Halaman
                            </button>
                        `;
                    });
                } else {
                    // Jika tidak scanning, pastikan overlay ditampilkan
                    scanning = false;
                    scannerOverlay.style.display = 'flex';
                    scannerOverlay.innerHTML = `
                        <div class="text-center">
                            <div class="mb-3">Kamera tidak aktif</div>
                            <button class="btn btn-primary" onclick="startScannerAgain()">
                                <i class="fas fa-camera me-2"></i> Mulai Scan
                            </button>
                        </div>
                    `;

                    // Coba lepaskan sumber daya kamera
                    try {
                        html5QrCode = null;
                    } catch (e) {
                        console.error('Error releasing camera resources:', e);
                    }
                }
            } catch (error) {
                console.error('Error in stopScanner:', error);
                scanning = false;
                scannerOverlay.style.display = 'flex';
                scannerOverlay.innerHTML = `
                    <div class="alert alert-warning mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i> Terjadi kesalahan
                    </div>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo me-2"></i> Muat Ulang Halaman
                    </button>
                `;
            }
        }
    }

    // Mulai scanner secara otomatis saat halaman dimuat
    // Tunggu library dimuat sebelum memulai scanner
    if (isHtml5QrCodeLoaded()) {
        // Library sudah dimuat, mulai scanner
        startScanner();
    } else {
        // Tunggu library dimuat
        scannerOverlay.innerHTML = `
            <div class="d-flex flex-column align-items-center justify-content-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div>Memuat library pemindai barcode...</div>
            </div>
        `;

        // Cek setiap 500ms apakah library sudah dimuat
        let libraryCheckInterval = setInterval(function() {
            if (isHtml5QrCodeLoaded()) {
                clearInterval(libraryCheckInterval);
                startScanner();
            }
        }, 500);

        // Timeout setelah 10 detik jika library tidak dimuat
        setTimeout(function() {
            clearInterval(libraryCheckInterval);
            if (!isHtml5QrCodeLoaded()) {
                scannerOverlay.innerHTML = `
                    <div class="alert alert-danger mb-3">
                        <i class="fas fa-exclamation-circle me-2"></i> Gagal Memuat Library
                    </div>
                    <p class="mb-3">Tidak dapat memuat library pemindai barcode dalam waktu yang ditentukan.</p>
                    <button class="btn btn-primary" onclick="checkAndStartScanner()">
                        <i class="fas fa-redo me-2"></i> Coba Lagi
                    </button>
                    <button class="btn btn-secondary ms-2" onclick="location.reload()">
                        <i class="fas fa-sync me-2"></i> Muat Ulang Halaman
                    </button>
                `;
            }
        }, 10000);
    }

    // On scan success
    function onScanSuccess(decodedText, decodedResult) {
        console.log('🎯 onScanSuccess CALLED!');
        console.log(`Code matched = ${decodedText}`, decodedResult);
        console.log('Function parameters:', { decodedText, decodedResult });

        // PENTING: Deklarasi variabel di scope function
        let barcodePhoto = null;

        // PENTING: Ambil foto SEBELUM stop scanning
        try {
            console.log('=== DEBUG FOTO BARCODE CAPTURE (BEFORE STOP) ===');

            // Ambil video element dari scanner (Html5Qrcode menggunakan #preview)
            let video = document.querySelector('#preview video');
            console.log('Video element found in #preview:', video ? 'YES' : 'NO');

            // Jika tidak ditemukan di #preview, coba cari di tempat lain
            if (!video) {
                console.log('Video not found in #preview, searching elsewhere...');
                const allVideos = document.querySelectorAll('video');
                console.log('Total video elements found:', allVideos.length);
                allVideos.forEach((v, index) => {
                    console.log(`Video ${index}:`, v, 'parent:', v.parentElement?.id, 'readyState:', v.readyState);
                });

                // Gunakan video pertama yang ditemukan jika ada
                if (allVideos.length > 0) {
                    video = allVideos[0];
                    console.log('Using first video element found');
                }
            }

            if (video) {
                console.log('Video properties:');
                console.log('- videoWidth:', video.videoWidth);
                console.log('- videoHeight:', video.videoHeight);
                console.log('- readyState:', video.readyState);
                console.log('- currentTime:', video.currentTime);

                // Pastikan video sudah ready
                if (video.readyState >= 2) { // HAVE_CURRENT_DATA
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');

                    // Set ukuran canvas sesuai video
                    canvas.width = video.videoWidth || 640;
                    canvas.height = video.videoHeight || 480;

                    console.log('Canvas size:', canvas.width, 'x', canvas.height);

                    // Draw video frame ke canvas
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);

                    // Convert ke base64
                    barcodePhoto = canvas.toDataURL('image/jpeg', 0.8);
                    console.log('Foto barcode berhasil diambil, size:', barcodePhoto.length, 'chars');
                    console.log('Foto header:', barcodePhoto.substring(0, 50));
                } else {
                    console.warn('Video not ready, readyState:', video.readyState);
                }
            } else {
                console.error('Video element tidak ditemukan!');

                // Coba cari dengan selector yang berbeda
                const allVideos = document.querySelectorAll('video');
                console.log('Total video elements found:', allVideos.length);
                allVideos.forEach((v, index) => {
                    console.log(`Video ${index}:`, v, 'readyState:', v.readyState);
                });
            }

            console.log('=== END DEBUG FOTO BARCODE CAPTURE ===');
        } catch (error) {
            console.error('Error mengambil foto barcode:', error);
            console.error('Error stack:', error.stack);
        }

        // SETELAH foto diambil, baru stop scanning
        stopScanner();
        toggleButton.style.display = 'none';

        // Show result
        scanResult.style.display = 'block';
        resultContent.innerHTML = `
            <div class="alert alert-info">
                <p><strong>Barcode terdeteksi!</strong></p>
                <p>Memproses absensi...</p>
            </div>
        `;

        // Check if location is available
        if (!currentPosition) {
            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <p><strong>Error:</strong> Lokasi tidak tersedia.</p>
                    <p>Pastikan GPS aktif dan izin lokasi diberikan.</p>
                </div>
            `;
            return;
        }

        // Cek apakah karyawan berada dalam radius yang diizinkan
        <?php if ($has_barcode): ?>
        const barcodeLatitude = <?php echo $barcode_config['latitude']; ?>;
        const barcodeLongitude = <?php echo $barcode_config['longitude']; ?>;
        const allowedRadius = <?php echo $barcode_config['radius']; ?>;

        const distance = calculateDistance(
            currentPosition.coords.latitude,
            currentPosition.coords.longitude,
            barcodeLatitude,
            barcodeLongitude
        );

        if (distance > allowedRadius) {
            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-map-marker-alt me-3 fa-2x text-danger"></i>
                        <h5 class="mb-0">Di Luar Jangkauan</h5>
                    </div>
                    <p>Anda berada di luar jangkauan lokasi absensi!</p>
                    <p>Jarak Anda: <strong>${Math.round(distance)} meter</strong></p>
                    <p>Silakan mendekati lokasi absensi dan coba lagi.</p>
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo me-2"></i> Coba Lagi
                    </button>
                </div>
            `;
            return;
        }
        <?php endif; ?>

        // Validasi jenis absen berdasarkan status presensi
        <?php if ($sudah_absen_masuk && $sudah_absen_pulang): ?>
        // Jika sudah absen masuk dan pulang
        resultContent.innerHTML = `
            <div class="alert alert-secondary">
                <p><strong>Informasi:</strong></p>
                <p>Anda sudah melakukan absensi masuk dan pulang hari ini.</p>
            </div>
            <div class="text-center mt-3">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i> Kembali ke Dashboard
                </a>
            </div>
        `;
        return;
        <?php endif; ?>

        // Send to server
        const formData = new FormData();
        formData.append('scan_result', decodedText);

        // Pastikan data lokasi valid sebelum dikirim
        if (currentPosition && currentPosition.coords) {
            formData.append('latitude', currentPosition.coords.latitude);
            formData.append('longitude', currentPosition.coords.longitude);
            formData.append('accuracy', currentPosition.coords.accuracy);
        } else {
            // Jika tidak ada data lokasi, gunakan nilai default
            formData.append('latitude', 0);
            formData.append('longitude', 0);
            formData.append('accuracy', 0);
            console.warn('Location data not available, using default values');
        }

        // Parameter jenis_absen tidak lagi diperlukan karena akan ditentukan otomatis di server

        // Tambahkan foto barcode yang sudah diambil di atas (jika ada)
        console.log('=== DEBUG FOTO BARCODE APPEND ===');
        console.log('barcodePhoto status:', barcodePhoto ? 'EXISTS' : 'NULL');
        if (barcodePhoto) {
            console.log('barcodePhoto length:', barcodePhoto.length);
            console.log('barcodePhoto type:', typeof barcodePhoto);
            console.log('barcodePhoto header:', barcodePhoto.substring(0, 50));

            formData.append('foto_barcode', barcodePhoto);
            console.log('✅ Foto barcode ditambahkan ke FormData, size:', barcodePhoto.length, 'chars');

            // Verifikasi FormData setelah append
            const hasPhoto = formData.has('foto_barcode');
            console.log('FormData has foto_barcode:', hasPhoto);
            if (hasPhoto) {
                const photoFromFormData = formData.get('foto_barcode');
                console.log('Photo from FormData length:', photoFromFormData ? photoFromFormData.length : 'NULL');
            }
        } else {
            console.warn('❌ Foto barcode tidak tersedia - akan disimpan tanpa foto');
        }
        console.log('=== END DEBUG FOTO BARCODE APPEND ===');

        // Tambahkan timestamp untuk mencegah caching
        const timestamp = new Date().getTime();

        // Tampilkan indikator loading
        resultContent.innerHTML = `
            <div class="d-flex justify-content-center my-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Memproses absensi...</span>
            </div>
        `;

        // Tampilkan indikator loading
        resultContent.innerHTML = `
            <div class="d-flex justify-content-center my-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Memproses absensi...</span>
            </div>
        `;

        // Debug: Log data yang akan dikirim
        console.log('=== DEBUG FORM DATA ===');
        console.log('FormData entries count:', Array.from(formData.entries()).length);
        for (const [key, value] of formData.entries()) {
            if (key === 'foto_barcode') {
                console.log(`${key}: ${value ? 'DATA TERSEDIA (' + value.length + ' chars)' : 'NULL'}`);
                if (value) {
                    console.log('Foto header check:', value.substring(0, 30));
                }
            } else {
                console.log(`${key}: ${value}`);
            }
        }
        console.log('barcodePhoto variable:', barcodePhoto ? 'HAS DATA (' + barcodePhoto.length + ' chars)' : 'NULL');
        console.log('=======================');

        // Kirim data menggunakan fetch API untuk mendukung file upload yang besar
        fetch('process_barcode.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.text();
        })
        .then(data => {
            console.log('Response received:', data);

            // Coba parse sebagai JSON dulu
            try {
                const jsonData = JSON.parse(data);
                if (jsonData.status === 'success') {
                    // Redirect ke dashboard dengan pesan sukses
                    window.location.href = 'index.php?absen_status=success&message=' + encodeURIComponent(jsonData.message);
                } else {
                    // Tampilkan error
                    resultContent.innerHTML = `
                        <div class="alert alert-danger">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-exclamation-circle me-3 fa-2x text-danger"></i>
                                <h5 class="mb-0">Error</h5>
                            </div>
                            <p>${jsonData.message}</p>
                        </div>
                        <div class="text-center mt-3">
                            <button class="btn btn-primary" onclick="location.reload()">
                                <i class="fas fa-redo me-2"></i> Coba Lagi
                            </button>
                        </div>
                    `;
                }
            } catch (e) {
                // Jika bukan JSON, mungkin redirect HTML
                console.log('Response is not JSON, might be redirect HTML');

                // Cek apakah ada redirect dalam response
                if (data.includes('Location:') || data.includes('index.php')) {
                    window.location.href = 'index.php?absen_status=success';
                } else {
                    // Tampilkan response sebagai HTML
                    document.body.innerHTML = data;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-exclamation-circle me-3 fa-2x text-danger"></i>
                        <h5 class="mb-0">Error Koneksi</h5>
                    </div>
                    <p>Terjadi kesalahan saat mengirim data: ${error.message}</p>
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo me-2"></i> Coba Lagi
                    </button>
                </div>
            `;
        });
    }

    // On scan failure - sudah didefinisikan di atas
    // function onScanFailure(error) {
    //     // Tidak perlu melakukan apa-apa, ini normal selama pemindaian
    // }

    // Debug: Log nilai variabel untuk troubleshooting
    console.log('Debug Flexible Schedule:');
    console.log('allow_flexible_schedule: <?php echo $allow_flexible_schedule ? 'true' : 'false'; ?>');
    console.log('selected_schedule: <?php echo $selected_schedule ? 'true' : 'false'; ?>');
    console.log('jam_kerja_options count: <?php echo count($jam_kerja_options); ?>');

    // Cek apakah perlu menampilkan pop-up pemilihan jam kerja
    <?php if ($allow_flexible_schedule && !$selected_schedule && !empty($jam_kerja_options)): ?>
    console.log('Kondisi terpenuhi - menampilkan pop-up pemilihan jam kerja');
    // Tampilkan pop-up pemilihan jam kerja
    setTimeout(function() {
        showScheduleSelectionModal();
    }, 2000); // Delay 2 detik untuk memastikan SweetAlert sudah dimuat
    <?php else: ?>
    console.log('Kondisi tidak terpenuhi untuk menampilkan pop-up');
    console.log('Alasan:');
    console.log('- Allow flexible: <?php echo $allow_flexible_schedule ? 'YES' : 'NO'; ?>');
    console.log('- Selected schedule: <?php echo $selected_schedule ? 'YES' : 'NO'; ?>');
    console.log('- Jam kerja options: <?php echo count($jam_kerja_options); ?>');
    <?php endif; ?>
});

// Fungsi untuk menampilkan modal pemilihan jam kerja
function showScheduleSelectionModal() {
    if (typeof Swal !== 'undefined') {
        const scheduleOptions = {};
        <?php foreach ($jam_kerja_options as $option): ?>
        scheduleOptions['<?php echo $option['id']; ?>'] = '<?php echo $option['nama_jam_kerja']; ?> (<?php echo $option['jam_masuk']; ?> - <?php echo $option['jam_pulang']; ?>)';
        <?php endforeach; ?>

        Swal.fire({
            title: 'Pilih Jam Kerja Anda',
            text: 'Silakan pilih jam kerja yang sesuai untuk hari ini',
            input: 'select',
            inputOptions: scheduleOptions,
            inputPlaceholder: 'Pilih jam kerja...',
            showCancelButton: false,
            allowOutsideClick: false,
            allowEscapeKey: false,
            confirmButtonText: 'Pilih',
            inputValidator: (value) => {
                if (!value) {
                    return 'Anda harus memilih jam kerja!'
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Submit pilihan jam kerja
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '';

                const jamKerjaInput = document.createElement('input');
                jamKerjaInput.type = 'hidden';
                jamKerjaInput.name = 'jam_kerja_id';
                jamKerjaInput.value = result.value;

                const submitInput = document.createElement('input');
                submitInput.type = 'hidden';
                submitInput.name = 'pilih_jam_kerja';
                submitInput.value = '1';

                form.appendChild(jamKerjaInput);
                form.appendChild(submitInput);
                document.body.appendChild(form);
                form.submit();
            }
        });
    } else {
        // Fallback jika SweetAlert tidak tersedia
        alert('Silakan refresh halaman untuk memilih jam kerja Anda.');
    }
}
</script>

<!-- Modal untuk Maps Leaflet -->
<?php if ($has_barcode && $barcode_config): ?>
<div id="map-modal" class="map-modal">
    <div class="map-modal-content">
        <div class="map-modal-header">
            <h5><i class="fas fa-map-marker-alt"></i> Lokasi Anda</h5>
            <span class="map-close" onclick="closeLeafletMap()">&times;</span>
        </div>
        <div class="map-modal-body">
            <div id="leaflet-map"></div>
        </div>
        <div class="map-modal-footer">
            <div class="map-legend">
                <div class="legend-item">
                    <img src="<?php echo BASE_URL; ?>assets/img/user-marker.png" alt="User" width="20">
                    <span>Lokasi Anda</span>
                </div>
                <div class="legend-item">
                    <img src="<?php echo BASE_URL; ?>assets/img/office-marker.png" alt="Office" width="20">
                    <span>Lokasi Kantor</span>
                </div>
                <div class="legend-item">
                    <span class="circle-icon"></span>
                    <span>Radius Absensi</span>
                </div>
            </div>
            <button class="btn btn-primary btn-sm" onclick="openLocationMap()">
                <i class="fas fa-external-link-alt"></i> Buka di Google Maps
            </button>
        </div>
    </div>
</div>


<?php endif; ?>

<?php
// Include footer
include_once '../includes/footer.php';
?>
