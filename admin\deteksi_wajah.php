<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses tambah data wajah
if (isset($_POST['tambah'])) {
    $user_id = clean($_POST['user_id']);

    // Upload foto wajah
    if (isset($_FILES['foto_wajah']) && $_FILES['foto_wajah']['error'] == 0) {
        $file_tmp = $_FILES['foto_wajah']['tmp_name'];
        $file_name = $_FILES['foto_wajah']['name'];
        $file_size = $_FILES['foto_wajah']['size'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Cek ekstensi file
        if (in_array($file_ext, ALLOWED_EXTENSIONS)) {
            // Cek ukuran file
            if ($file_size <= MAX_UPLOAD_SIZE) {
                $foto_wajah = 'face_' . time() . '_' . $user_id . '.' . $file_ext;
                $upload_path = UPLOAD_PATH . $foto_wajah;

                if (move_uploaded_file($file_tmp, $upload_path)) {
                    // Insert data wajah
                    $query = "INSERT INTO deteksi_wajah (user_id, foto_wajah) VALUES ('$user_id', '$foto_wajah')";

                    if (mysqli_query($conn, $query)) {
                        setMessage('success', 'Data wajah berhasil ditambahkan!');
                    } else {
                        setMessage('danger', 'Gagal menambahkan data wajah!');
                    }
                } else {
                    setMessage('danger', 'Gagal mengupload foto wajah!');
                }
            } else {
                setMessage('danger', 'Ukuran file terlalu besar! Maksimal ' . (MAX_UPLOAD_SIZE / 1024 / 1024) . 'MB');
            }
        } else {
            setMessage('danger', 'Ekstensi file tidak diizinkan! Hanya ' . implode(', ', ALLOWED_EXTENSIONS) . ' yang diizinkan');
        }
    } else {
        setMessage('danger', 'Foto wajah harus diupload!');
    }

    redirect('admin/deteksi_wajah.php');
}

// Proses hapus data wajah
if (isset($_GET['hapus'])) {
    $id = clean($_GET['hapus']);

    // Ambil data foto
    $query = "SELECT foto_wajah FROM deteksi_wajah WHERE id = '$id'";
    $result = mysqli_query($conn, $query);
    $row = mysqli_fetch_assoc($result);

    if (!empty($row['foto_wajah'])) {
        $file = UPLOAD_PATH . $row['foto_wajah'];
        if (file_exists($file)) {
            unlink($file);
        }
    }

    // Hapus data wajah
    $query = "DELETE FROM deteksi_wajah WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Data wajah berhasil dihapus!');
    } else {
        setMessage('danger', 'Gagal menghapus data wajah!');
    }

    redirect('admin/deteksi_wajah.php');
}

// Ambil data karyawan untuk dropdown
$query = "SELECT * FROM users WHERE role = 'karyawan' ORDER BY nama ASC";
$result = mysqli_query($conn, $query);

$karyawan = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $karyawan[] = $row;
    }
}

// Ambil data deteksi wajah
$query = "SELECT dw.*, u.nik, u.nama
          FROM deteksi_wajah dw
          JOIN users u ON dw.user_id = u.id
          ORDER BY dw.created_at DESC";
$result = mysqli_query($conn, $query);

$deteksi_wajah = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $deteksi_wajah[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Pendaftaran Deteksi Wajah</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#tambahWajahModal">
            <i class="fas fa-plus"></i> Tambah Data Wajah
        </button>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Data Deteksi Wajah</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>NIK</th>
                            <th>Nama Karyawan</th>
                            <th>Foto Wajah</th>
                            <th>Tanggal Pendaftaran</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($deteksi_wajah)): ?>
                            <tr>
                                <td colspan="5" class="text-center">Tidak ada data deteksi wajah</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($deteksi_wajah as $dw): ?>
                                <tr>
                                    <td><?php echo $dw['nik']; ?></td>
                                    <td><?php echo $dw['nama']; ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL . 'uploads/' . $dw['foto_wajah']; ?>" target="_blank" class="btn btn-sm btn-info">
                                            <i class="fas fa-image"></i> Lihat
                                        </a>
                                    </td>
                                    <td><?php echo date('d-m-Y H:i', strtotime($dw['created_at'])); ?></td>
                                    <td>
                                        <a href="deteksi_wajah.php?hapus=<?php echo $dw['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data wajah ini?')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Informasi Deteksi Wajah</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5 class="alert-heading">Petunjuk Pendaftaran Wajah:</h5>
                <ol>
                    <li>Pilih karyawan yang akan didaftarkan data wajahnya.</li>
                    <li>Upload foto wajah karyawan dengan posisi menghadap ke depan.</li>
                    <li>Pastikan wajah terlihat jelas dan tidak tertutup.</li>
                    <li>Foto diambil dengan pencahayaan yang baik.</li>
                    <li>Untuk hasil deteksi yang lebih baik, tambahkan beberapa foto wajah dengan ekspresi berbeda.</li>
                </ol>
                <hr>
                <p class="mb-0">Data wajah ini akan digunakan untuk verifikasi saat karyawan melakukan absensi.</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Data Wajah -->
<div class="modal fade" id="tambahWajahModal" tabindex="-1" aria-labelledby="tambahWajahModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tambahWajahModalLabel">Tambah Data Wajah</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="user_id" class="form-label">Karyawan</label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="">Pilih Karyawan</option>
                            <?php foreach ($karyawan as $k): ?>
                                <option value="<?php echo $k['id']; ?>"><?php echo $k['nik'] . ' - ' . $k['nama']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="foto_wajah" class="form-label">Foto Wajah</label>
                        <input type="file" class="form-control" id="foto_wajah" name="foto_wajah" required>
                        <small class="text-muted">Format: JPG, JPEG, PNG. Maksimal <?php echo MAX_UPLOAD_SIZE / 1024 / 1024; ?>MB</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="tambah" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
