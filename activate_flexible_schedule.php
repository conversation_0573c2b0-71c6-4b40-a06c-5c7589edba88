<?php
// Script untuk mengaktifkan flexible schedule untuk karyawan
require_once 'config/database.php';

// Aktifkan flexible schedule untuk karyawan dengan ID tertentu
$user_ids = [1, 2, 3]; // Ganti dengan ID karyawan yang ingin diaktifkan

echo "<h2>Mengaktifkan Flexible Schedule</h2>";

foreach ($user_ids as $user_id) {
    // Cek apakah user ada
    $query = "SELECT nama, nik FROM users WHERE id = '$user_id' AND role = 'karyawan'";
    $result = mysqli_query($conn, $query);
    $user = mysqli_fetch_assoc($result);
    
    if ($user) {
        // Update allow_flexible_schedule
        $query = "UPDATE users SET allow_flexible_schedule = 1 WHERE id = '$user_id'";
        if (mysqli_query($conn, $query)) {
            echo "✅ Flexible schedule diaktifkan untuk: {$user['nama']} (NIK: {$user['nik']})<br>";
        } else {
            echo "❌ Gagal mengaktifkan untuk: {$user['nama']} - " . mysqli_error($conn) . "<br>";
        }
    } else {
        echo "❌ User dengan ID $user_id tidak ditemukan atau bukan karyawan<br>";
    }
}

echo "<br><a href='test_flexible_schedule.php'>Test ulang kondisi</a>";
echo "<br><a href='admin/karyawan.php'>Lihat di admin panel</a>";
?>
