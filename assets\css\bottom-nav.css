/* Bottom Navigation Bar Styles */

.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 10px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #6c757d;
    font-size: 12px;
    padding: 5px 0;
    width: 20%;
    transition: all 0.3s ease;
}

.bottom-nav-item:hover {
    color: #4e73df;
}

.bottom-nav-item.active {
    color: #4e73df;
}

.bottom-nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.bottom-nav-text {
    font-size: 10px;
    text-align: center;
}

/* Add padding to the bottom of containers to prevent content from being hidden behind the nav bar */
.mobile-app-container,
.mobile-presensi-container,
.mobile-riwayat-container {
    padding-bottom: 70px !important;
}

/* Floating button adjustment to avoid overlap with bottom nav */
.attendance-button {
    bottom: 80px !important;
}

/* Adjust attendance button in presensi page */
.attendance-btn {
    bottom: 80px !important;
}

/* Adjust floating map button to avoid overlap with bottom nav */
#floating-map-btn {
    bottom: 80px !important;
    right: 20px !important;
}

/* Responsive adjustments */
@media (min-width: 768px) {
    .bottom-nav {
        max-width: 600px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 20px 20px 0 0;
    }
}

@media (min-width: 992px) {
    .bottom-nav {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .bottom-nav {
        max-width: 840px;
    }
}

/* Animation for active item */
.bottom-nav-item.active .bottom-nav-icon {
    animation: bounce 0.5s;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-2px);
    }
}
