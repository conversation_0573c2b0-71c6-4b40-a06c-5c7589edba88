<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Filter data
$filter_user_id = isset($_GET['user_id']) ? clean($_GET['user_id']) : '';
$filter_tanggal_awal = isset($_GET['tanggal_awal']) ? clean($_GET['tanggal_awal']) : date('Y-m-d', strtotime('-7 days'));
$filter_tanggal_akhir = isset($_GET['tanggal_akhir']) ? clean($_GET['tanggal_akhir']) : date('Y-m-d');
$filter_jenis_absen = isset($_GET['jenis_absen']) ? clean($_GET['jenis_absen']) : '';
$filter_status = isset($_GET['status']) ? clean($_GET['status']) : '';

// Ambil data karyawan untuk filter
$query_karyawan = "SELECT id, nik, nama FROM users WHERE role = 'karyawan' ORDER BY nama";
$result_karyawan = mysqli_query($conn, $query_karyawan);
$karyawan = [];
while ($row = mysqli_fetch_assoc($result_karyawan)) {
    $karyawan[] = $row;
}

// Proses persetujuan atau penolakan
if (isset($_POST['action']) && isset($_POST['id'])) {
    $id = clean($_POST['id']);
    $action = clean($_POST['action']);
    $keterangan = isset($_POST['keterangan']) ? clean($_POST['keterangan']) : '';
    
    if ($action === 'approve') {
        $query = "UPDATE absensi_offline SET status = 'approved', keterangan = '$keterangan' WHERE id = '$id'";
        $message = "Absensi offline berhasil disetujui";
    } else if ($action === 'reject') {
        $query = "UPDATE absensi_offline SET status = 'rejected', keterangan = '$keterangan' WHERE id = '$id'";
        $message = "Absensi offline berhasil ditolak";
    }
    
    if (mysqli_query($conn, $query)) {
        $_SESSION['success'] = $message;
    } else {
        $_SESSION['error'] = "Gagal memproses absensi offline: " . mysqli_error($conn);
    }
    
    // Redirect untuk menghindari resubmission
    header("Location: absensi_offline.php");
    exit;
}

// Query untuk mengambil data absensi offline
$query = "SELECT ao.*, u.nik, u.nama 
          FROM absensi_offline ao
          LEFT JOIN users u ON ao.user_id = u.id
          WHERE 1=1";

// Tambahkan filter
if (!empty($filter_user_id)) {
    $query .= " AND ao.user_id = '$filter_user_id'";
}

if (!empty($filter_tanggal_awal)) {
    $query .= " AND ao.tanggal >= '$filter_tanggal_awal'";
}

if (!empty($filter_tanggal_akhir)) {
    $query .= " AND ao.tanggal <= '$filter_tanggal_akhir'";
}

if (!empty($filter_jenis_absen)) {
    $query .= " AND ao.jenis_absen = '$filter_jenis_absen'";
}

if (!empty($filter_status)) {
    $query .= " AND ao.status = '$filter_status'";
}

$query .= " ORDER BY ao.tanggal DESC, ao.jam DESC";
$result = mysqli_query($conn, $query);
$absensi_offline = [];
while ($row = mysqli_fetch_assoc($result)) {
    $absensi_offline[] = $row;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Absensi Offline</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Absensi Offline</li>
    </ol>
    
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php 
            echo $_SESSION['success'];
            unset($_SESSION['success']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php 
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Data
        </div>
        <div class="card-body">
            <form method="get" action="" class="row g-3">
                <div class="col-md-4">
                    <label for="user_id" class="form-label">Karyawan</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">Semua Karyawan</option>
                        <?php foreach ($karyawan as $k): ?>
                            <option value="<?php echo $k['id']; ?>" <?php echo $filter_user_id == $k['id'] ? 'selected' : ''; ?>>
                                <?php echo $k['nik'] . ' - ' . $k['nama']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="jenis_absen" class="form-label">Jenis Absen</label>
                    <select class="form-select" id="jenis_absen" name="jenis_absen">
                        <option value="">Semua Jenis</option>
                        <option value="masuk" <?php echo $filter_jenis_absen == 'masuk' ? 'selected' : ''; ?>>Masuk</option>
                        <option value="keluar" <?php echo $filter_jenis_absen == 'keluar' ? 'selected' : ''; ?>>Keluar</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Semua Status</option>
                        <option value="pending" <?php echo $filter_status == 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="approved" <?php echo $filter_status == 'approved' ? 'selected' : ''; ?>>Disetujui</option>
                        <option value="rejected" <?php echo $filter_status == 'rejected' ? 'selected' : ''; ?>>Ditolak</option>
                    </select>
                </div>
                <div class="col-12 mt-3" id="dateRangeContainer">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-5">
                                    <label for="tanggal_awal" class="form-label">Tanggal Awal</label>
                                    <input type="date" class="form-control" id="tanggal_awal" name="tanggal_awal" value="<?php echo $filter_tanggal_awal; ?>">
                                </div>
                                <div class="col-md-5">
                                    <label for="tanggal_akhir" class="form-label">Tanggal Akhir</label>
                                    <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?php echo $filter_tanggal_akhir; ?>">
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-filter me-1"></i> Terapkan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-table me-1"></i>
                Data Absensi Offline
                <?php if (!empty($filter_tanggal_awal) && !empty($filter_tanggal_akhir)): ?>
                <small class="text-muted ms-2">
                    (<?php echo date('d/m/Y', strtotime($filter_tanggal_awal)); ?> - <?php echo date('d/m/Y', strtotime($filter_tanggal_akhir)); ?>)
                </small>
                <?php endif; ?>
            </div>
            <div>
                <a href="export_absensi_offline.php<?php 
                    $params = [];
                    if (!empty($filter_user_id)) $params[] = "user_id=$filter_user_id";
                    if (!empty($filter_tanggal_awal)) $params[] = "tanggal_awal=$filter_tanggal_awal";
                    if (!empty($filter_tanggal_akhir)) $params[] = "tanggal_akhir=$filter_tanggal_akhir";
                    if (!empty($filter_jenis_absen)) $params[] = "jenis_absen=$filter_jenis_absen";
                    if (!empty($filter_status)) $params[] = "status=$filter_status";
                    echo !empty($params) ? '?' . implode('&', $params) : '';
                ?>" class="btn btn-success btn-sm">
                    <i class="fas fa-file-export me-1"></i> Export Data
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-datatable" id="absensiOfflineTable" data-order='[[2, "desc"], [3, "desc"]]'>
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Tanggal</th>
                            <th>Jam</th>
                            <th>Jenis Absen</th>
                            <th>Status</th>
                            <th>Keterangan</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $no = 1; foreach ($absensi_offline as $a): ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo $a['nik']; ?></td>
                                <td><?php echo $a['nama']; ?></td>
                                <td><?php echo date('d/m/Y', strtotime($a['tanggal'])); ?></td>
                                <td><?php echo date('H:i:s', strtotime($a['jam'])); ?></td>
                                <td>
                                    <?php if ($a['jenis_absen'] == 'masuk'): ?>
                                        <span class="badge bg-primary">Masuk</span>
                                    <?php else: ?>
                                        <span class="badge bg-info">Keluar</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($a['status'] == 'pending'): ?>
                                        <span class="badge bg-warning">Pending</span>
                                    <?php elseif ($a['status'] == 'approved'): ?>
                                        <span class="badge bg-success">Disetujui</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Ditolak</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $a['keterangan'] ?? '-'; ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info view-detail"
                                            data-id="<?php echo $a['id']; ?>"
                                            data-nik="<?php echo $a['nik']; ?>"
                                            data-nama="<?php echo $a['nama']; ?>"
                                            data-tanggal="<?php echo date('d/m/Y', strtotime($a['tanggal'])); ?>"
                                            data-jam="<?php echo date('H:i:s', strtotime($a['jam'])); ?>"
                                            data-jenis-absen="<?php echo $a['jenis_absen']; ?>"
                                            data-status="<?php echo $a['status']; ?>"
                                            data-keterangan="<?php echo $a['keterangan'] ?? ''; ?>"
                                            data-foto="<?php echo $a['foto']; ?>">
                                        <i class="fas fa-info-circle"></i> Detail
                                    </button>
                                    
                                    <?php if ($a['status'] == 'pending'): ?>
                                    <button type="button" class="btn btn-sm btn-success approve-btn"
                                            data-id="<?php echo $a['id']; ?>"
                                            data-nik="<?php echo $a['nik']; ?>"
                                            data-nama="<?php echo $a['nama']; ?>"
                                            data-tanggal="<?php echo date('d/m/Y', strtotime($a['tanggal'])); ?>"
                                            data-jam="<?php echo date('H:i:s', strtotime($a['jam'])); ?>"
                                            data-jenis-absen="<?php echo $a['jenis_absen']; ?>">
                                        <i class="fas fa-check"></i> Setujui
                                    </button>
                                    
                                    <button type="button" class="btn btn-sm btn-danger reject-btn"
                                            data-id="<?php echo $a['id']; ?>"
                                            data-nik="<?php echo $a['nik']; ?>"
                                            data-nama="<?php echo $a['nama']; ?>"
                                            data-tanggal="<?php echo date('d/m/Y', strtotime($a['tanggal'])); ?>"
                                            data-jam="<?php echo date('H:i:s', strtotime($a['jam'])); ?>"
                                            data-jenis-absen="<?php echo $a['jenis_absen']; ?>">
                                        <i class="fas fa-times"></i> Tolak
                                    </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk menampilkan detail -->
<div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">Detail Absensi Offline</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-subtitle mb-3 text-muted">Informasi Karyawan</h6>
                                <p><strong>NIK:</strong> <span id="detailNik"></span></p>
                                <p><strong>Nama:</strong> <span id="detailNama"></span></p>
                                <p><strong>Tanggal:</strong> <span id="detailTanggal"></span></p>
                                <p><strong>Jam:</strong> <span id="detailJam"></span></p>
                                <p><strong>Jenis Absen:</strong> <span id="detailJenisAbsen"></span></p>
                                <p><strong>Status:</strong> <span id="detailStatus"></span></p>
                                <p><strong>Keterangan:</strong> <span id="detailKeterangan"></span></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-subtitle mb-3 text-muted">Foto</h6>
                                <img id="detailFoto" src="" alt="Foto Absensi" class="img-fluid rounded">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk persetujuan -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="">
                <div class="modal-header">
                    <h5 class="modal-title" id="approveModalLabel">Setujui Absensi Offline</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Anda yakin ingin menyetujui absensi offline ini?</p>
                    <p><strong>NIK:</strong> <span id="approveNik"></span></p>
                    <p><strong>Nama:</strong> <span id="approveNama"></span></p>
                    <p><strong>Tanggal:</strong> <span id="approveTanggal"></span></p>
                    <p><strong>Jam:</strong> <span id="approveJam"></span></p>
                    <p><strong>Jenis Absen:</strong> <span id="approveJenisAbsen"></span></p>
                    
                    <div class="mb-3">
                        <label for="approveKeterangan" class="form-label">Keterangan (opsional)</label>
                        <textarea class="form-control" id="approveKeterangan" name="keterangan" rows="3"></textarea>
                    </div>
                    
                    <input type="hidden" name="id" id="approveId">
                    <input type="hidden" name="action" value="approve">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Setujui</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal untuk penolakan -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="">
                <div class="modal-header">
                    <h5 class="modal-title" id="rejectModalLabel">Tolak Absensi Offline</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Anda yakin ingin menolak absensi offline ini?</p>
                    <p><strong>NIK:</strong> <span id="rejectNik"></span></p>
                    <p><strong>Nama:</strong> <span id="rejectNama"></span></p>
                    <p><strong>Tanggal:</strong> <span id="rejectTanggal"></span></p>
                    <p><strong>Jam:</strong> <span id="rejectJam"></span></p>
                    <p><strong>Jenis Absen:</strong> <span id="rejectJenisAbsen"></span></p>
                    
                    <div class="mb-3">
                        <label for="rejectKeterangan" class="form-label">Keterangan (wajib)</label>
                        <textarea class="form-control" id="rejectKeterangan" name="keterangan" rows="3" required></textarea>
                    </div>
                    
                    <input type="hidden" name="id" id="rejectId">
                    <input type="hidden" name="action" value="reject">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Tolak</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Tampilkan detail saat tombol diklik
        $('.view-detail').on('click', function() {
            const id = $(this).data('id');
            const nik = $(this).data('nik');
            const nama = $(this).data('nama');
            const tanggal = $(this).data('tanggal');
            const jam = $(this).data('jam');
            const jenisAbsen = $(this).data('jenis-absen');
            const status = $(this).data('status');
            const keterangan = $(this).data('keterangan');
            const foto = $(this).data('foto');

            // Set informasi di modal
            $('#detailNik').text(nik);
            $('#detailNama').text(nama);
            $('#detailTanggal').text(tanggal);
            $('#detailJam').text(jam);
            
            if (jenisAbsen === 'masuk') {
                $('#detailJenisAbsen').html('<span class="badge bg-primary">Masuk</span>');
            } else {
                $('#detailJenisAbsen').html('<span class="badge bg-info">Keluar</span>');
            }
            
            if (status === 'pending') {
                $('#detailStatus').html('<span class="badge bg-warning">Pending</span>');
            } else if (status === 'approved') {
                $('#detailStatus').html('<span class="badge bg-success">Disetujui</span>');
            } else {
                $('#detailStatus').html('<span class="badge bg-danger">Ditolak</span>');
            }
            
            $('#detailKeterangan').text(keterangan || '-');
            $('#detailFoto').attr('src', '../' + foto);

            // Tampilkan modal
            $('#detailModal').modal('show');
        });
        
        // Tampilkan modal persetujuan
        $('.approve-btn').on('click', function() {
            const id = $(this).data('id');
            const nik = $(this).data('nik');
            const nama = $(this).data('nama');
            const tanggal = $(this).data('tanggal');
            const jam = $(this).data('jam');
            const jenisAbsen = $(this).data('jenis-absen');
            
            // Set informasi di modal
            $('#approveId').val(id);
            $('#approveNik').text(nik);
            $('#approveNama').text(nama);
            $('#approveTanggal').text(tanggal);
            $('#approveJam').text(jam);
            
            if (jenisAbsen === 'masuk') {
                $('#approveJenisAbsen').html('<span class="badge bg-primary">Masuk</span>');
            } else {
                $('#approveJenisAbsen').html('<span class="badge bg-info">Keluar</span>');
            }
            
            // Tampilkan modal
            $('#approveModal').modal('show');
        });
        
        // Tampilkan modal penolakan
        $('.reject-btn').on('click', function() {
            const id = $(this).data('id');
            const nik = $(this).data('nik');
            const nama = $(this).data('nama');
            const tanggal = $(this).data('tanggal');
            const jam = $(this).data('jam');
            const jenisAbsen = $(this).data('jenis-absen');
            
            // Set informasi di modal
            $('#rejectId').val(id);
            $('#rejectNik').text(nik);
            $('#rejectNama').text(nama);
            $('#rejectTanggal').text(tanggal);
            $('#rejectJam').text(jam);
            
            if (jenisAbsen === 'masuk') {
                $('#rejectJenisAbsen').html('<span class="badge bg-primary">Masuk</span>');
            } else {
                $('#rejectJenisAbsen').html('<span class="badge bg-info">Keluar</span>');
            }
            
            // Tampilkan modal
            $('#rejectModal').modal('show');
        });
    });
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
