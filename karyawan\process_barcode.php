<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Aktifkan error reporting untuk debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Fungsi untuk menangani error dan redirect
function handleError($message) {
    $_SESSION['error_message'] = $message;
    header('Location: scan_barcode.php');
    exit;
}

// Fungsi untuk menangani sukses dan redirect ke dashboard
function handleSuccess($message, $type = 'masuk') {
    $_SESSION['success_message'] = $message;
    // Redirect ke dashboard dengan parameter sukses
    header('Location: index.php?absen_status=success&type=' . $type . '&message=' . urlencode($message));
    exit;
}

// Cek login
if (!isLoggedIn() || $_SESSION['role'] != 'karyawan') {
    handleError('Anda tidak memiliki akses ke halaman ini');
}

// Cek apakah ini adalah request POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    handleError('Metode request tidak valid');
}

// Cek apakah ada data scan_result
if (!isset($_POST['scan_result'])) {
    handleError('Data scan barcode tidak ditemukan');
}

// Ambil data user
$user_id = $_SESSION['user_id'];
$query = "SELECT u.*, l.nama_lokasi, l.latitude, l.longitude, l.radius
          FROM users u
          LEFT JOIN lokasi l ON u.lokasi_id = l.id
          WHERE u.id = '$user_id'";
$result = mysqli_query($conn, $query);
$user = mysqli_fetch_assoc($result);

// Ambil data presensi hari ini
$today = date('Y-m-d');
$query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result = mysqli_query($conn, $query);
$presensi_hari_ini = mysqli_fetch_assoc($result);

// Cek apakah karyawan sedang dalam perjalanan dinas
$query = "SELECT * FROM izin_dinas
          WHERE user_id = '$user_id'
          AND status = 'Approved'
          AND tanggal_mulai <= '$today'
          AND tanggal_selesai >= '$today'";
$result = mysqli_query($conn, $query);
$izin_dinas_aktif = mysqli_fetch_assoc($result);

// Cek apakah hari ini hari libur
$query = "SELECT * FROM hari_libur WHERE tanggal = '$today'";
$result = mysqli_query($conn, $query);
$hari_libur = mysqli_fetch_assoc($result);

// Ambil jam kerja berdasarkan bidang dan hari
$hari_map = [
    'Monday' => 'Senin',
    'Tuesday' => 'Selasa',
    'Wednesday' => 'Rabu',
    'Thursday' => 'Kamis',
    'Friday' => 'Jumat',
    'Saturday' => 'Sabtu',
    'Sunday' => 'Minggu'
];
$hari_en = date('l'); // Mendapatkan nama hari dalam bahasa Inggris
$hari_name = $hari_map[$hari_en]; // Konversi ke bahasa Indonesia

// Cek apakah user memiliki izin jam kerja fleksibel
$allow_flexible_schedule = isset($user['allow_flexible_schedule']) && $user['allow_flexible_schedule'] == 1;

// Ambil jam kerja berdasarkan apakah user memiliki izin jam kerja fleksibel
$jam_kerja = null;
$selected_schedule = null;

if ($allow_flexible_schedule) {
    // Debug: Log flexible schedule
    error_log("=== DEBUG FLEXIBLE SCHEDULE PROCESS BARCODE ===");
    error_log("User ID: $user_id");
    error_log("Today: $today");
    error_log("Allow flexible schedule: YES");

    // Cek apakah karyawan sudah memilih jam kerja untuk hari ini
    $query = "SELECT usc.*, jk.*
              FROM user_schedule_choices usc
              JOIN jam_kerja jk ON usc.jam_kerja_id = jk.id
              WHERE usc.user_id = '$user_id' AND usc.tanggal = '$today'";
    $result = mysqli_query($conn, $query);
    error_log("Query selected schedule: $query");
    error_log("Query result rows: " . ($result ? mysqli_num_rows($result) : 'FAILED'));

    if ($result && mysqli_num_rows($result) > 0) {
        $selected_schedule = mysqli_fetch_assoc($result);
        $jam_kerja = $selected_schedule;
        error_log("Selected schedule found: " . json_encode($selected_schedule));
        error_log("Jam kerja set to selected schedule");
        error_log("Selected jam kerja details:");
        error_log("- ID: " . ($jam_kerja['id'] ?? 'N/A'));
        error_log("- Nama: " . ($jam_kerja['nama_jam_kerja'] ?? 'N/A'));
        error_log("- Awal masuk: " . ($jam_kerja['awal_jam_masuk'] ?? 'N/A'));
        error_log("- Akhir masuk: " . ($jam_kerja['akhir_jam_masuk'] ?? 'N/A'));
    } else {
        error_log("No selected schedule found for today");
        error_log("MySQL error (if any): " . mysqli_error($conn));
    }
    error_log("===============================================");
} else {
    // Debug: Log non-flexible schedule
    error_log("=== DEBUG NON-FLEXIBLE SCHEDULE PROCESS BARCODE ===");
    error_log("Allow flexible schedule: NO");
    error_log("Using default jam kerja for hari: $hari_name");

    // Gunakan fungsi getJamKerjaByUserId dari functions.php
    $jam_kerja = getJamKerjaByUserId($user_id);

    // Log untuk debugging
    error_log("Hari ini: $hari_en ($hari_name)");
    error_log("Jam kerja dari fungsi: " . ($jam_kerja ? json_encode($jam_kerja) : "tidak ditemukan"));

    // Jika tidak ada jam kerja dari fungsi, coba query langsung
    if (!$jam_kerja) {
        $query = "SELECT jkb.*, jk.awal_jam_masuk, jk.jam_masuk, jk.akhir_jam_masuk, jk.jam_pulang, jk.akhir_jam_pulang
                  FROM jam_kerja_bidang jkb
                  JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
                  WHERE jkb.bidang_id = '{$user['bidang_id']}' AND jkb.hari = '$hari_name'";
        $result = mysqli_query($conn, $query);
        $jam_kerja = mysqli_fetch_assoc($result);

        error_log("Query langsung: $query");
        error_log("Hasil query langsung: " . ($jam_kerja ? json_encode($jam_kerja) : "tidak ditemukan"));
    }
    error_log("Final jam kerja for non-flexible: " . ($jam_kerja ? json_encode($jam_kerja) : "NULL"));
    error_log("===================================================");
}

$sudah_absen_masuk = ($presensi_hari_ini && !empty($presensi_hari_ini['jam_masuk']));
$sudah_absen_pulang = ($presensi_hari_ini && !empty($presensi_hari_ini['jam_pulang']));

// Cek apakah user diizinkan menggunakan absensi barcode
$allow_barcode = isset($user['allow_barcode']) && $user['allow_barcode'] == 1;

// Cek apakah hari ini hari libur
if ($hari_libur) {
    handleError('Hari ini adalah hari libur: ' . $hari_libur['nama_libur']);
}

// Cek apakah karyawan sedang dalam perjalanan dinas
if ($izin_dinas_aktif) {
    handleError('Anda sedang dalam perjalanan dinas. Absensi otomatis tercatat.');
}

// Cek apakah jam kerja sudah diatur
if (!$jam_kerja) {
    handleError('Jam kerja belum diatur untuk bidang Anda. Silakan hubungi administrator.');
}

// Validasi dan sanitasi input
$scan_result = isset($_POST['scan_result']) ? clean($_POST['scan_result']) : '';
$latitude = isset($_POST['latitude']) ? clean($_POST['latitude']) : 0;
$longitude = isset($_POST['longitude']) ? clean($_POST['longitude']) : 0;
$accuracy = isset($_POST['accuracy']) ? clean($_POST['accuracy']) : 0;
$foto_barcode = isset($_POST['foto_barcode']) ? $_POST['foto_barcode'] : null;
$jam_sekarang = date('H:i:s');

// Fungsi untuk menyimpan foto barcode
function saveFotoBarcode($foto_data, $user_id, $jenis_absen) {
    error_log("=== SAVE FOTO BARCODE START ===");
    error_log("User ID: $user_id, Jenis: $jenis_absen");
    error_log("Foto data empty: " . (empty($foto_data) ? 'YES' : 'NO'));

    if (!$foto_data) {
        error_log("Foto data kosong, return null");
        error_log("=== SAVE FOTO BARCODE END ===");
        return null;
    }

    try {
        error_log("Original foto data length: " . strlen($foto_data));
        error_log("Original foto header: " . substr($foto_data, 0, 50));

        // Hapus header data URI
        $foto_data = str_replace('data:image/jpeg;base64,', '', $foto_data);
        $foto_data = str_replace(' ', '+', $foto_data);

        error_log("After header removal length: " . strlen($foto_data));

        // Decode base64 ke binary
        $foto_binary = base64_decode($foto_data);
        error_log("Binary data length: " . strlen($foto_binary));

        // Buat nama file unik
        $foto_filename = 'barcode_' . $jenis_absen . '_' . $user_id . '_' . date('Ymd_His') . '.jpg';
        error_log("Generated filename: $foto_filename");

        // Buat direktori uploads jika belum ada
        $upload_dir = '../uploads/barcode/';
        if (!file_exists($upload_dir)) {
            error_log("Creating directory: $upload_dir");
            mkdir($upload_dir, 0777, true);
        }

        // Simpan file
        $file_path = $upload_dir . $foto_filename;
        error_log("Saving to path: $file_path");

        $bytes_written = file_put_contents($file_path, $foto_binary);
        if ($bytes_written !== false) {
            error_log("Foto barcode berhasil disimpan: $foto_filename ($bytes_written bytes)");
            error_log("=== SAVE FOTO BARCODE END ===");
            return $foto_filename;
        } else {
            error_log("Gagal menyimpan foto barcode - file_put_contents returned false");
            error_log("=== SAVE FOTO BARCODE END ===");
            return null;
        }
    } catch (Exception $e) {
        error_log("Error menyimpan foto barcode: " . $e->getMessage());
        error_log("=== SAVE FOTO BARCODE END ===");
        return null;
    }
}

// Tentukan jenis absen secara otomatis berdasarkan status absensi hari ini
if (!$sudah_absen_masuk) {
    $jenis_absen = 'masuk';
} else if (!$sudah_absen_pulang) {
    $jenis_absen = 'pulang';
} else {
    handleError('Anda sudah melakukan absensi masuk dan pulang hari ini!');
}

// Log jenis absen yang ditentukan
error_log("Jenis absen otomatis: $jenis_absen (sudah_absen_masuk: " . ($sudah_absen_masuk ? 'ya' : 'tidak') . ", sudah_absen_pulang: " . ($sudah_absen_pulang ? 'ya' : 'tidak') . ")");

// Log data untuk debugging
error_log("Scan data received: " . json_encode([
    'scan_result' => $scan_result,
    'latitude' => $latitude,
    'longitude' => $longitude,
    'accuracy' => $accuracy,
    'jenis_absen' => $jenis_absen,
    'foto_barcode_received' => !empty($foto_barcode) ? 'YES' : 'NO',
    'foto_barcode_length' => !empty($foto_barcode) ? strlen($foto_barcode) : 0
]));

// Debug foto barcode
if (!empty($foto_barcode)) {
    error_log("=== DEBUG FOTO BARCODE ===");
    error_log("Foto barcode diterima dengan panjang: " . strlen($foto_barcode));
    error_log("Header foto: " . substr($foto_barcode, 0, 50));
    error_log("==========================");
} else {
    error_log("=== WARNING: FOTO BARCODE KOSONG ===");
    error_log("POST data keys: " . implode(', ', array_keys($_POST)));
    error_log("===================================");
}

// Validasi hasil scan
if (empty($scan_result)) {
    handleError('Hasil scan barcode kosong!');
}

// Validasi izin barcode
if (!$allow_barcode) {
    handleError('Anda tidak diizinkan menggunakan fitur absensi barcode. Silakan hubungi administrator.');
}

// Validasi lokasi
if (empty($latitude) || empty($longitude)) {
    handleError('Lokasi tidak terdeteksi! Pastikan GPS aktif dan izin lokasi diberikan.');
}

// Cek apakah barcode valid
try {
    // Sanitasi input untuk query
    $scan_result_safe = mysqli_real_escape_string($conn, $scan_result);

    $query = "SELECT bc.*, l.nama_lokasi, l.latitude, l.longitude, l.radius
            FROM barcode_config bc
            JOIN lokasi l ON bc.lokasi_id = l.id
            WHERE bc.barcode_value = '$scan_result_safe' AND bc.is_active = 1";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        throw new Exception("Database error: " . mysqli_error($conn));
    }

    if (mysqli_num_rows($result) == 0) {
        handleError('Barcode tidak valid atau tidak aktif!');
    }

    $barcode = mysqli_fetch_assoc($result);

    // Log barcode data untuk debugging
    error_log("Barcode data found: " . json_encode($barcode));
} catch (Exception $e) {
    handleError('Terjadi kesalahan saat memvalidasi barcode: ' . $e->getMessage());
}

// Hitung jarak antara lokasi user dan lokasi barcode
try {
    // Pastikan semua nilai adalah angka yang valid
    $lat1 = floatval($latitude);
    $lon1 = floatval($longitude);
    $lat2 = floatval($barcode['latitude']);
    $lon2 = floatval($barcode['longitude']);

    // Cek apakah nilai valid
    if (!is_numeric($lat1) || !is_numeric($lon1) || !is_numeric($lat2) || !is_numeric($lon2)) {
        throw new Exception("Koordinat lokasi tidak valid");
    }

    $jarak = hitungJarak($lat1, $lon1, $lat2, $lon2);

    // Log jarak untuk debugging
    error_log("Jarak dihitung: $jarak meter");
} catch (Exception $e) {
    handleError('Terjadi kesalahan saat menghitung jarak: ' . $e->getMessage());
}

// Validasi radius
if ($jarak > $barcode['radius']) {
    handleError('Anda berada di luar radius yang diizinkan! Jarak Anda: ' . round($jarak) . ' meter, Radius: ' . $barcode['radius'] . ' meter');
}

// Debug: Log jam kerja yang akan digunakan untuk validasi
error_log("=== DEBUG PROCESS BARCODE VALIDATION ===");
error_log("Allow flexible schedule: " . ($allow_flexible_schedule ? 'YES' : 'NO'));
error_log("Selected schedule: " . ($selected_schedule ? 'YES' : 'NO'));
error_log("Jam kerja data: " . ($jam_kerja ? json_encode($jam_kerja) : 'NULL'));
if ($jam_kerja) {
    error_log("Jam kerja details:");
    error_log("- Nama: " . ($jam_kerja['nama_jam_kerja'] ?? 'N/A'));
    error_log("- Awal masuk: " . ($jam_kerja['awal_jam_masuk'] ?? 'N/A'));
    error_log("- Jam masuk: " . ($jam_kerja['jam_masuk'] ?? 'N/A'));
    error_log("- Akhir masuk: " . ($jam_kerja['akhir_jam_masuk'] ?? 'N/A'));
    error_log("- Jam pulang: " . ($jam_kerja['jam_pulang'] ?? 'N/A'));
    error_log("- Akhir pulang: " . ($jam_kerja['akhir_jam_pulang'] ?? 'N/A'));
}
error_log("Current time: " . date('H:i:s'));
error_log("Jenis absen: " . ($jenis_absen ?? 'N/A'));
error_log("Validation rules applied: SAME AS PRESENSI.PHP");
error_log("=========================================");

// Proses absensi
if ($jenis_absen == 'masuk') {
    // Cek apakah sudah absen masuk
    if ($sudah_absen_masuk) {
        handleError('Anda sudah melakukan absensi masuk hari ini!');
    }

    // Validasi waktu absensi masuk (sama seperti di presensi.php)
    // Debug: Log detail validasi
    error_log("=== VALIDASI ABSEN MASUK PROCESS BARCODE ===");
    error_log("Jam sekarang: $jam_sekarang");
    error_log("Jam kerja yang digunakan untuk validasi:");
    error_log("- ID: " . ($jam_kerja['id'] ?? 'N/A'));
    error_log("- Nama: " . ($jam_kerja['nama_jam_kerja'] ?? 'N/A'));
    error_log("- Awal masuk: " . ($jam_kerja['awal_jam_masuk'] ?? 'N/A'));
    error_log("- Jam masuk: " . ($jam_kerja['jam_masuk'] ?? 'N/A'));
    error_log("- Akhir masuk: " . ($jam_kerja['akhir_jam_masuk'] ?? 'N/A'));
    error_log("============================================");

    // Cek apakah sudah melewati jam akhir masuk
    if ($jam_sekarang > $jam_kerja['akhir_jam_masuk']) {
        error_log("VALIDATION FAILED: Jam sekarang ($jam_sekarang) > Akhir jam masuk ({$jam_kerja['akhir_jam_masuk']})");
        handleError('Anda tidak dapat melakukan absensi masuk karena sudah melewati jam akhir masuk (' . $jam_kerja['akhir_jam_masuk'] . '). Jam kerja yang digunakan: ' . ($jam_kerja['nama_jam_kerja'] ?? 'N/A'));
    }

    // Cek apakah sudah melewati jam awal masuk
    if ($jam_sekarang < $jam_kerja['awal_jam_masuk']) {
        error_log("VALIDATION FAILED: Jam sekarang ($jam_sekarang) < Awal jam masuk ({$jam_kerja['awal_jam_masuk']})");
        handleError('Anda belum dapat melakukan absensi masuk karena belum memasuki jam awal masuk (' . $jam_kerja['awal_jam_masuk'] . '). Jam kerja yang digunakan: ' . ($jam_kerja['nama_jam_kerja'] ?? 'N/A'));
    }

    // Tentukan status berdasarkan waktu
    $status = 'Tepat Waktu';
    if ($jam_sekarang > $jam_kerja['jam_masuk']) {
        $status = 'Terlambat';
    }

    // Proses foto barcode jika ada
    $foto_filename = saveFotoBarcode($foto_barcode, $user_id, 'masuk');

    // Buat lokasi masuk dalam format string
    $lokasi_masuk = "Barcode: {$barcode['nama_lokasi']}, Lat: $latitude, Long: $longitude, Akurasi: $accuracy m";

    // Simpan data presensi dengan foto barcode
    $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, status, keterangan)
              VALUES ('$user_id', '$today', '$jam_sekarang', " .
              ($foto_filename ? "'$foto_filename'" : "NULL") . ", " .
              "'$lokasi_masuk', '$status', 'Absensi dengan barcode')";

    if (mysqli_query($conn, $query)) {
        $message = "Absensi masuk berhasil! Status: $status";
        if ($foto_filename) {
            $message .= " (Foto barcode tersimpan)";
        }
        handleSuccess($message, 'masuk');
    } else {
        handleError('Gagal menyimpan data absensi: ' . mysqli_error($conn));
    }
} else if ($jenis_absen == 'pulang') {
    // Cek apakah sudah absen masuk
    if (!$sudah_absen_masuk) {
        handleError('Anda belum melakukan absensi masuk hari ini!');
    }

    // Cek apakah sudah absen pulang
    if ($sudah_absen_pulang) {
        handleError('Anda sudah melakukan absensi pulang hari ini!');
    }

    // Cek apakah sudah melewati jam akhir pulang
    if ($jam_sekarang > $jam_kerja['akhir_jam_pulang']) {
        handleError('Anda tidak dapat melakukan absensi pulang karena sudah melewati jam akhir pulang (' . $jam_kerja['akhir_jam_pulang'] . ').');
    }

    // Cek apakah sudah mencapai jam pulang
    if ($jam_sekarang < $jam_kerja['jam_pulang']) {
        handleError('Anda tidak dapat melakukan absensi pulang karena belum mencapai jam pulang (' . $jam_kerja['jam_pulang'] . ').');
    }

    // Tentukan status berdasarkan waktu
    $status = $presensi_hari_ini['status']; // Pertahankan status sebelumnya
    // Catatan: Kita sudah memvalidasi bahwa waktu saat ini >= jam_pulang, jadi tidak perlu lagi status 'Pulang Awal'

    if ($jam_sekarang > $jam_kerja['akhir_jam_pulang']) {
        $status = 'Lembur';
    }

    // Proses foto barcode jika ada
    $foto_filename = saveFotoBarcode($foto_barcode, $user_id, 'pulang');

    // Buat lokasi pulang dalam format string
    $lokasi_pulang = "Barcode: {$barcode['nama_lokasi']}, Lat: $latitude, Long: $longitude, Akurasi: $accuracy m";

    // Update data absensi dengan foto barcode
    $query = "UPDATE presensi SET
              jam_pulang = '$jam_sekarang',
              lokasi_pulang = '$lokasi_pulang',
              status = '$status'" .
              ($foto_filename ? ", foto_pulang = '$foto_filename'" : "") .
              " WHERE id = '{$presensi_hari_ini['id']}'";

    if (mysqli_query($conn, $query)) {
        $message = "Absensi pulang berhasil! Status: $status";
        if ($foto_filename) {
            $message .= " (Foto barcode tersimpan)";
        }
        handleSuccess($message, 'pulang');
    } else {
        handleError('Gagal menyimpan data absensi: ' . mysqli_error($conn));
    }
} else {
    handleError('Jenis absensi tidak valid!');
}
