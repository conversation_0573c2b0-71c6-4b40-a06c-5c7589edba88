<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Cek apakah ada parameter id
if (!isset($_GET['id'])) {
    setMessage('danger', 'ID pengaduan tidak valid');
    redirect('gangguan_absensi.php');
}

$id = clean($_GET['id']);

// Ambil data pengaduan berdasarkan id
$query = "SELECT ga.*, u.nama, u.nik FROM gangguan_absensi ga
          JOIN users u ON ga.user_id = u.id
          WHERE ga.id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Pengaduan tidak ditemukan');
    redirect('gangguan_absensi.php');
}

$pengaduan = mysqli_fetch_assoc($result);

// Proses persetujuan atau penolakan pengaduan
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $catatan = $_POST['catatan'] ?? '';

    if ($action == 'approve' || $action == 'reject') {
        $status = ($action == 'approve') ? 'approved' : 'rejected';

        // Update status pengaduan
        $query = "UPDATE gangguan_absensi SET status = '$status', catatan_admin = '$catatan' WHERE id = '$id'";
        if (mysqli_query($conn, $query)) {

            // Jika disetujui, tambahkan data presensi
            if ($status == 'approved') {
                $user_id = $pengaduan['user_id'];
                $tanggal = $pengaduan['tanggal'];
                $jenis_absen = $pengaduan['jenis_absen'];
                $bukti_foto = $pengaduan['bukti_foto'];

                // Cek apakah sudah ada data presensi pada tanggal tersebut
                $query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$tanggal'";
                $result = mysqli_query($conn, $query);

                if (mysqli_num_rows($result) > 0) {
                    // Update data presensi yang sudah ada
                    $presensi = mysqli_fetch_assoc($result);

                    // Gunakan lokasi default tanpa query
                    $lokasi = 'Kantor';

                    // Gunakan bukti foto dari pengaduan jika ada, jika tidak gunakan default
                    $foto = !empty($bukti_foto) ? 'gangguan_absensi/' . $bukti_foto : 'default.jpg';

                    if ($jenis_absen == 'masuk') {
                        // Set jam masuk default (jam masuk normal)
                        $jam_kerja = getJamKerjaByUserId($user_id);
                        $jam_masuk = $jam_kerja ? $jam_kerja['jam_masuk'] : '08:00:00';

                        // Periksa apakah kolom keterangan sudah ada isinya
                        $keterangan_update = empty($presensi['keterangan']) ?
                            "'Gangguan absensi masuk'" :
                            "CONCAT(IFNULL(keterangan, ''), ' (Gangguan absensi masuk)')";

                        // Update dengan bukti foto dari pengaduan
                        $query = "UPDATE presensi SET
                                  jam_masuk = '$jam_masuk',
                                  foto_masuk = '$foto',
                                  lokasi_masuk = IFNULL(lokasi_masuk, '$lokasi'),
                                  status = 'Tepat Waktu',
                                  keterangan = $keterangan_update
                                  WHERE user_id = '$user_id' AND tanggal = '$tanggal'";
                        mysqli_query($conn, $query);
                    } elseif ($jenis_absen == 'pulang') {
                        // Set jam pulang default (jam pulang normal)
                        $jam_kerja = getJamKerjaByUserId($user_id);
                        $jam_pulang = $jam_kerja ? $jam_kerja['jam_pulang'] : '17:00:00';

                        // Periksa apakah kolom keterangan sudah ada isinya
                        $keterangan_update = empty($presensi['keterangan']) ?
                            "'Gangguan absensi pulang'" :
                            "CONCAT(IFNULL(keterangan, ''), ' (Gangguan absensi pulang)')";

                        // Update dengan bukti foto dari pengaduan
                        $query = "UPDATE presensi SET
                                  jam_pulang = '$jam_pulang',
                                  foto_pulang = '$foto',
                                  lokasi_pulang = '$lokasi',
                                  status = 'Tepat Waktu',
                                  keterangan = $keterangan_update
                                  WHERE user_id = '$user_id' AND tanggal = '$tanggal'";
                        mysqli_query($conn, $query);
                    }
                } else {
                    // Insert data presensi baru
                    $jam_kerja = getJamKerjaByUserId($user_id);
                    $jam_masuk = $jam_kerja ? $jam_kerja['jam_masuk'] : '08:00:00';
                    $jam_pulang = $jam_kerja ? $jam_kerja['jam_pulang'] : '17:00:00';

                    // Gunakan lokasi default tanpa query
                    $lokasi = 'Kantor';

                    // Gunakan bukti foto dari pengaduan jika ada, jika tidak gunakan default
                    $foto = !empty($bukti_foto) ? 'gangguan_absensi/' . $bukti_foto : 'default.jpg';

                    if ($jenis_absen == 'masuk') {
                        // Untuk absen masuk, semua kolom wajib harus diisi
                        $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, jam_pulang, foto_pulang, lokasi_pulang, status, keterangan)
                                  VALUES ('$user_id', '$tanggal', '$jam_masuk', '$foto', '$lokasi', NULL, NULL, NULL, 'Tepat Waktu', 'Gangguan absensi masuk')";
                    } else {
                        // Insert baru dengan semua kolom wajib
                        $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, jam_pulang, foto_pulang, lokasi_pulang, status, keterangan)
                                  VALUES ('$user_id', '$tanggal', '$jam_masuk', '$foto', '$lokasi', '$jam_pulang', '$foto', '$lokasi', 'Tepat Waktu', 'Gangguan absensi masuk dan pulang')";
                    }

                    mysqli_query($conn, $query);
                }
            }

            setMessage('success', 'Pengaduan gangguan absensi berhasil diproses!');
            redirect('gangguan_absensi.php');
        } else {
            setMessage('danger', 'Gagal memproses pengaduan gangguan absensi!');
            redirect("detail_gangguan_absensi.php?id=$id");
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Detail Pengaduan Gangguan Absensi</h1>
        <a href="gangguan_absensi.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Informasi Pengaduan</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th width="30%">Tanggal</th>
                            <td width="70%"><?php echo date('d/m/Y', strtotime($pengaduan['tanggal'])); ?></td>
                        </tr>
                        <tr>
                            <th>NIK</th>
                            <td><?php echo $pengaduan['nik']; ?></td>
                        </tr>
                        <tr>
                            <th>Nama</th>
                            <td><?php echo $pengaduan['nama']; ?></td>
                        </tr>
                        <tr>
                            <th>Jenis Absen</th>
                            <td><?php echo ucfirst($pengaduan['jenis_absen']); ?></td>
                        </tr>
                        <tr>
                            <th>Keterangan</th>
                            <td><?php echo $pengaduan['keterangan']; ?></td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <?php if ($pengaduan['status'] == 'pending'): ?>
                                    <span class="badge bg-warning text-dark">Menunggu</span>
                                <?php elseif ($pengaduan['status'] == 'approved'): ?>
                                    <span class="badge bg-success">Disetujui</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Ditolak</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php if (!empty($pengaduan['catatan_admin'])): ?>
                        <tr>
                            <th>Catatan Admin</th>
                            <td><?php echo $pengaduan['catatan_admin']; ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
                <div class="col-md-6">
                    <?php if (!empty($pengaduan['bukti_foto'])): ?>
                        <div class="text-center">
                            <h6 class="mb-3">Bukti Foto</h6>
                            <img src="<?php echo BASE_URL . 'uploads/gangguan_absensi/' . $pengaduan['bukti_foto']; ?>" alt="Bukti Foto" class="img-fluid rounded" style="max-height: 300px;">
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Tidak ada bukti foto yang diunggah.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($pengaduan['status'] == 'pending'): ?>
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h6 class="m-0">Setujui Pengaduan</h6>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3">
                                    <label for="catatan_approve" class="form-label">Catatan (opsional)</label>
                                    <textarea class="form-control" id="catatan_approve" name="catatan" rows="3"></textarea>
                                </div>
                                <input type="hidden" name="action" value="approve">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check"></i> Setujui Pengaduan
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h6 class="m-0">Tolak Pengaduan</h6>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3">
                                    <label for="catatan_reject" class="form-label">Catatan (opsional)</label>
                                    <textarea class="form-control" id="catatan_reject" name="catatan" rows="3"></textarea>
                                </div>
                                <input type="hidden" name="action" value="reject">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-times"></i> Tolak Pengaduan
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
