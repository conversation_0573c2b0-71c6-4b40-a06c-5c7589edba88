/* Mobile Scan Barcode CSS */

/* Base Styles */
.mobile-profile-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
    padding-bottom: 20px; /* Padding bottom tanpa bottom navigation */
}

@media (min-width: 768px) {
    .mobile-profile-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-profile-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-profile-container {
        max-width: 840px;
    }
}

/* Header Styles - <PERSON><PERSON> dengan presensi-header */
.profile-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.profile-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.profile-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    overflow: hidden;
    position: relative;
    padding: 2px;
}

.profile-header .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
    object-position: center;
    background-color: rgba(255, 255, 255, 0.2);
}

.profile-header .user-details {
    flex: 1;
}

.profile-header .user-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.profile-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.profile-header .date-info {
    display: flex;
    justify-content: space-between;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 15px;
    font-size: 14px;
}

.profile-header .date-info i {
    margin-right: 5px;
}

/* Clock Styles */
.clock-container {
    text-align: center;
    margin: 20px 0;
}

.clock {
    font-size: 48px;
    font-weight: bold;
    color: #343a40;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 5px;
}

.date {
    font-size: 16px;
    color: #6c757d;
}

/* Styling untuk container scan */
.scan-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 15px;
}

/* Styling untuk judul section */
.section-title {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e3e6f0;
}

.section-title h4 {
    color: #4e73df;
    font-weight: 600;
    margin-bottom: 5px;
}

.section-title p {
    font-size: 14px;
    margin-bottom: 0;
}

/* Styling untuk scanner */
#scanner {
    width: 100%;
    height: 300px;
    border: 2px solid #4e73df;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

#scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 10;
}

.scanner-guide {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border: 2px solid #fff;
    border-radius: 15px;
    box-shadow: 0 0 0 5000px rgba(0, 0, 0, 0.3);
    z-index: 5;
}

.scanner-laser {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #ff4757;
    z-index: 6;
    box-shadow: 0 0 8px #ff4757;
    animation: scan 2s infinite;
}

@keyframes scan {
    0% { top: 40%; }
    50% { top: 60%; }
    100% { top: 40%; }
}

/* Styling untuk hasil scan */
.scan-result {
    margin-top: 20px;
    padding: 15px;
    border-radius: 15px;
    background: white;
    display: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Styling untuk tombol aksi */
.scan-actions {
    margin: 20px 0;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.scan-actions button {
    border-radius: 10px;
    font-weight: 500;
}

/* Styling untuk status scan */
.scan-status {
    display: flex;
    align-items: center;
    justify-content: center;
}

.scanning-indicator {
    display: flex;
    align-items: center;
    background-color: #e8f4ff;
    padding: 8px 15px;
    border-radius: 8px;
    color: #4e73df;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.scanning-indicator .spinner-border {
    color: #4e73df !important;
}

/* Styling untuk status absensi */
.absen-status {
    display: flex;
    align-items: center;
    justify-content: center;
}

.absen-status .badge {
    font-size: 14px;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.badge.bg-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #858796 0%, #60616f 100%) !important;
}

.btn-group {
    border-radius: 10px;
    overflow: hidden;
}

.btn-check + .btn {
    padding: 10px 15px;
}

/* Styling untuk informasi lokasi */
.location-info {
    margin-top: 20px;
    padding: 15px;
    border-radius: 15px;
    background: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.location-info h5 {
    color: #4e73df;
    font-weight: 600;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Styling untuk tombol melayang */
.floating-button {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #4e73df;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    text-decoration: none;
    transition: all 0.3s ease;
}

.floating-button:hover, .floating-button:focus {
    background-color: #2e59d9;
    color: white;
    text-decoration: none;
    transform: scale(1.05);
}

/* Styling untuk alert */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Styling untuk indikator radius */
.radius-indicator {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
}

.radius-in {
    background-color: #d4edda;
    color: #155724;
}

.radius-out {
    background-color: #f8d7da;
    color: #721c24;
}

.radius-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* Responsif untuk layar kecil */
@media (max-width: 576px) {
    .scan-actions {
        flex-direction: column;
    }

    .scan-actions button, .btn-group {
        width: 100%;
        margin-bottom: 10px;
    }
}
