/* Mobile Scan Barcode CSS */

/* Base Styles */
.mobile-profile-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
    padding-bottom: 20px; /* Padding bottom tanpa bottom navigation */
}

@media (min-width: 768px) {
    .mobile-profile-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-profile-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-profile-container {
        max-width: 840px;
    }
}

/* Header Styles - <PERSON><PERSON> dengan presensi-header */
.profile-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.profile-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.profile-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    overflow: hidden;
    position: relative;
    padding: 2px;
}

.profile-header .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
    object-position: center;
    background-color: rgba(255, 255, 255, 0.2);
}

.profile-header .user-details {
    flex: 1;
}

.profile-header .user-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.profile-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.profile-header .date-info {
    display: flex;
    justify-content: space-between;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 15px;
    font-size: 14px;
}

.profile-header .date-info i {
    margin-right: 5px;
}

/* Clock Styles */
.clock-container {
    text-align: center;
    margin: 20px 0;
}

.clock {
    font-size: 48px;
    font-weight: bold;
    color: #343a40;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 5px;
}

.date {
    font-size: 16px;
    color: #6c757d;
}

/* Styling untuk container scan */
.scan-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 15px;
}

/* Styling untuk judul section */
.section-title {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e3e6f0;
}

.section-title h4 {
    color: #4e73df;
    font-weight: 600;
    margin-bottom: 5px;
}

.section-title p {
    font-size: 14px;
    margin-bottom: 0;
}

/* Styling untuk scanner */
#scanner {
    width: 100%;
    height: 300px;
    border: 2px solid #4e73df;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

#scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 10;
}

.scanner-guide {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border: 2px solid #fff;
    border-radius: 15px;
    box-shadow: 0 0 0 5000px rgba(0, 0, 0, 0.3);
    z-index: 5;
}

.scanner-laser {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #ff4757;
    z-index: 6;
    box-shadow: 0 0 8px #ff4757;
    animation: scan 2s infinite;
}

@keyframes scan {
    0% { top: 40%; }
    50% { top: 60%; }
    100% { top: 40%; }
}

/* Styling untuk hasil scan */
.scan-result {
    margin-top: 20px;
    padding: 15px;
    border-radius: 15px;
    background: white;
    display: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Styling untuk tombol aksi */
.scan-actions {
    margin: 20px 0;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.scan-actions button {
    border-radius: 10px;
    font-weight: 500;
}

/* Styling untuk status scan */
.scan-status {
    display: flex;
    align-items: center;
    justify-content: center;
}

.scanning-indicator {
    display: flex;
    align-items: center;
    background-color: #e8f4ff;
    padding: 8px 15px;
    border-radius: 8px;
    color: #4e73df;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.scanning-indicator .spinner-border {
    color: #4e73df !important;
}

/* Styling untuk status absensi */
.absen-status {
    display: flex;
    align-items: center;
    justify-content: center;
}

.absen-status .badge {
    font-size: 14px;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.badge.bg-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #858796 0%, #60616f 100%) !important;
}

.btn-group {
    border-radius: 10px;
    overflow: hidden;
}

.btn-check + .btn {
    padding: 10px 15px;
}

/* Styling untuk informasi lokasi */
.location-info {
    margin-top: 20px;
    padding: 15px;
    border-radius: 15px;
    background: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.location-info h5 {
    color: #4e73df;
    font-weight: 600;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Styling untuk tombol melayang */
.floating-button {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #4e73df;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    text-decoration: none;
    transition: all 0.3s ease;
}

.floating-button:hover, .floating-button:focus {
    background-color: #2e59d9;
    color: white;
    text-decoration: none;
    transform: scale(1.05);
}

/* Floating Map Button */
#floating-map-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    z-index: 1001;
    transition: all 0.3s ease;
    animation: pulse-map 2s infinite;
}

#floating-map-btn i {
    font-size: 24px;
}

#floating-map-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
    animation: none;
}

#floating-map-btn:active {
    transform: translateY(0) scale(0.95);
}

@keyframes pulse-map {
    0% {
        box-shadow: 0 0 0 0 rgba(54, 185, 204, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(54, 185, 204, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(54, 185, 204, 0);
    }
}

/* Styling untuk alert */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Styling untuk indikator radius */
.radius-indicator {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
}

.radius-in {
    background-color: #d4edda;
    color: #155724;
}

.radius-out {
    background-color: #f8d7da;
    color: #721c24;
}

.radius-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* Styling untuk status radius di atas kamera */
.radius-status-top {
    position: relative;
    z-index: 5;
}

.radius-status-top .alert {
    border: 2px solid transparent;
    font-weight: 600;
    font-size: 16px;
    padding: 12px 20px;
    margin-bottom: 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.radius-status-top .alert-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    color: white;
    border-color: #1cc88a;
    animation: pulse-success 2s infinite;
}

.radius-status-top .alert-warning {
    background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    color: white;
    border-color: #f6c23e;
    animation: pulse-warning 2s infinite;
}

.radius-status-top .alert-danger {
    background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);
    color: white;
    border-color: #e74a3b;
    animation: pulse-danger 2s infinite;
}

.radius-status-top .alert-info {
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
    color: white;
    border-color: #36b9cc;
}

/* Animasi pulse untuk status */
@keyframes pulse-success {
    0% { box-shadow: 0 4px 15px rgba(28, 200, 138, 0.3); }
    50% { box-shadow: 0 4px 25px rgba(28, 200, 138, 0.5); }
    100% { box-shadow: 0 4px 15px rgba(28, 200, 138, 0.3); }
}

@keyframes pulse-warning {
    0% { box-shadow: 0 4px 15px rgba(246, 194, 62, 0.3); }
    50% { box-shadow: 0 4px 25px rgba(246, 194, 62, 0.5); }
    100% { box-shadow: 0 4px 15px rgba(246, 194, 62, 0.3); }
}

@keyframes pulse-danger {
    0% { box-shadow: 0 4px 15px rgba(231, 74, 59, 0.3); }
    50% { box-shadow: 0 4px 25px rgba(231, 74, 59, 0.5); }
    100% { box-shadow: 0 4px 15px rgba(231, 74, 59, 0.3); }
}

/* Animasi bounce untuk perubahan status */
@keyframes statusBounce {
    0% { transform: scale(1); }
    25% { transform: scale(1.05); }
    50% { transform: scale(0.98); }
    75% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Animasi spin untuk indikator real-time */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animasi blink untuk indikator LIVE - lebih cepat untuk high-speed tracking */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.2; }
}

/* Animasi pulse cepat untuk high-speed mode */
@keyframes fastPulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

/* Animasi untuk indikator high-speed */
@keyframes highSpeedGlow {
    0% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.5); }
    50% { box-shadow: 0 0 15px rgba(40, 167, 69, 0.8); }
    100% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.5); }
}

/* Animasi pulse untuk loading */
@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

/* Styling untuk live indicator */
.live-indicator .badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: bold;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: fastPulse 1s infinite;
}

/* Styling untuk badge HIGH-SPEED */
.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    animation: highSpeedGlow 2s infinite;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Efek untuk status radius dengan high-speed tracking */
.radius-status-top .alert {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); /* Transisi lebih cepat untuk high-speed */
}

/* Animasi untuk perubahan status yang lebih responsif */
@keyframes quickStatusChange {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.02) rotate(1deg); }
    50% { transform: scale(0.98) rotate(-1deg); }
    75% { transform: scale(1.01) rotate(0.5deg); }
    100% { transform: scale(1) rotate(0deg); }
}

/* Efek hover untuk status radius */
.radius-status-top .alert:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Map Modal Styles */
.map-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999 !important;
    overflow: auto;
}

.map-modal.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.map-modal-content {
    position: relative;
    background-color: #fff;
    margin: 10% auto;
    width: 90%;
    max-width: 800px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-50px);}
    to {opacity: 1; transform: translateY(0);}
}

.map-modal-header {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
}

.map-modal-header h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.map-close {
    font-size: 28px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: color 0.3s ease;
}

.map-close:hover {
    color: white;
}

.map-modal-body {
    padding: 0;
    height: 400px;
}

#leaflet-map {
    width: 100%;
    height: 100%;
    border-radius: 0;
}

.map-modal-footer {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
    background-color: #f8f9fa;
}

.map-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #555;
}

.legend-item img, .legend-item .circle-icon {
    margin-right: 5px;
}

.circle-icon {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: rgba(78, 115, 223, 0.2);
    border: 2px solid #4e73df;
}

/* Responsif untuk layar kecil */
@media (max-width: 576px) {
    .scan-actions {
        flex-direction: column;
    }

    .scan-actions button, .btn-group {
        width: 100%;
        margin-bottom: 10px;
    }

    .radius-status-top .alert {
        font-size: 14px;
        padding: 10px 15px;
    }

    .radius-status-top {
        margin-bottom: 15px;
    }

    #floating-map-btn {
        bottom: 80px !important;
        right: 20px !important;
    }

    .floating-button {
        bottom: 80px !important;
    }

    .map-modal-content {
        margin: 5% auto;
        width: 95%;
    }

    .map-modal-body {
        height: 300px;
    }

    .map-legend {
        flex-direction: column;
        gap: 10px;
    }

    .map-modal-footer {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
}
