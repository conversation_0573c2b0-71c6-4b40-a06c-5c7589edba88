<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/pesan_functions.php';

// Cek akses admin
checkAccess('admin');

// Proses update status pesan
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $pesan_id = (int)$_POST['pesan_id'];
    $action = $_POST['action'];
    $admin_id = $_SESSION['user_id'];
    
    if ($action === 'selesai') {
        // Tandai pesan sebagai selesai
        $query = "UPDATE pesan_peringatan 
                  SET status = 'selesai', 
                      tanggal_dibaca = NOW(),
                      keterangan = CONCAT(IFNULL(keterangan, ''), '\n[DISELESAIKAN OLEH ADMIN] ', NOW(), ' - Admin ID: $admin_id')
                  WHERE id = $pesan_id";
        
        if (mysqli_query($conn, $query)) {
            $_SESSION['success_message'] = "Pesan peringatan berhasil ditandai sebagai selesai.";
        } else {
            $_SESSION['error_message'] = "Gagal menyelesaikan pesan peringatan.";
        }
    } elseif ($action === 'aktifkan') {
        // Aktifkan kembali pesan
        $query = "UPDATE pesan_peringatan 
                  SET status = 'aktif', 
                      tanggal_dibaca = NULL,
                      keterangan = CONCAT(IFNULL(keterangan, ''), '\n[DIAKTIFKAN KEMBALI] ', NOW(), ' - Admin ID: $admin_id')
                  WHERE id = $pesan_id";
        
        if (mysqli_query($conn, $query)) {
            $_SESSION['success_message'] = "Pesan peringatan berhasil diaktifkan kembali.";
        } else {
            $_SESSION['error_message'] = "Gagal mengaktifkan pesan peringatan.";
        }
    }
    
    header('Location: kelola_status_pesan.php');
    exit;
}

// Filter
$filter_status = isset($_GET['status']) ? $_GET['status'] : 'aktif';
$filter_tingkat = isset($_GET['tingkat']) ? $_GET['tingkat'] : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Query untuk mengambil data pesan peringatan
$query = "SELECT pp.*, u.nama, u.nik, u.bidang, admin.nama as admin_nama
          FROM pesan_peringatan pp
          JOIN users u ON pp.user_id = u.id
          JOIN users admin ON pp.dibuat_oleh = admin.id
          WHERE 1=1";

// Filter berdasarkan status
if (!empty($filter_status)) {
    $query .= " AND pp.status = '$filter_status'";
}

// Filter berdasarkan tingkat
if (!empty($filter_tingkat)) {
    $query .= " AND pp.tingkat_peringatan = '$filter_tingkat'";
}

// Filter pencarian
if (!empty($search)) {
    $search_escaped = mysqli_real_escape_string($conn, $search);
    $query .= " AND (u.nama LIKE '%$search_escaped%' OR u.nik LIKE '%$search_escaped%' OR pp.judul LIKE '%$search_escaped%')";
}

$query .= " ORDER BY pp.tingkat_peringatan DESC, pp.tanggal_dibuat DESC";

$result = mysqli_query($conn, $query);
$pesan_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $pesan_list[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<style>
.badge-tingkat-ringan { background-color: #ffc107; color: #000; }
.badge-tingkat-sedang { background-color: #fd7e14; color: #fff; }
.badge-tingkat-berat { background-color: #dc3545; color: #fff; }
.badge-status-aktif { background-color: #dc3545; color: #fff; }
.badge-status-dibaca { background-color: #6c757d; color: #fff; }
.badge-status-selesai { background-color: #198754; color: #fff; }

.pesan-berat-row {
    background-color: rgba(220, 53, 69, 0.05);
    border-left: 4px solid #dc3545;
}

.action-buttons .btn {
    margin-right: 5px;
    margin-bottom: 5px;
}
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">
            <i class="fas fa-cogs me-2"></i>
            Kelola Status Pesan Peringatan
        </h1>
        <a href="pesan_peringatan.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Kembali ke Daftar Pesan
        </a>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="m-0 font-weight-bold">Kelola Status Pesan Peringatan</h6>
                <div class="d-flex">
                    <a href="tambah_pesan_peringatan.php" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>
                        Buat Pesan Baru
                    </a>
                </div>
            </div>
            
            <!-- Filter dan Pencarian -->
            <div class="row g-3">
                <div class="col-md-3">
                    <form method="get" class="d-flex">
                        <select class="form-select me-2" name="status" onchange="this.form.submit()">
                            <option value="">-- Semua Status --</option>
                            <option value="aktif" <?php echo $filter_status === 'aktif' ? 'selected' : ''; ?>>Aktif</option>
                            <option value="dibaca" <?php echo $filter_status === 'dibaca' ? 'selected' : ''; ?>>Dibaca</option>
                            <option value="selesai" <?php echo $filter_status === 'selesai' ? 'selected' : ''; ?>>Selesai</option>
                        </select>
                        <?php if (!empty($filter_tingkat)): ?>
                            <input type="hidden" name="tingkat" value="<?php echo htmlspecialchars($filter_tingkat); ?>">
                        <?php endif; ?>
                        <?php if (!empty($search)): ?>
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                        <?php endif; ?>
                    </form>
                </div>
                <div class="col-md-3">
                    <form method="get" class="d-flex">
                        <select class="form-select me-2" name="tingkat" onchange="this.form.submit()">
                            <option value="">-- Semua Tingkat --</option>
                            <option value="ringan" <?php echo $filter_tingkat === 'ringan' ? 'selected' : ''; ?>>Ringan</option>
                            <option value="sedang" <?php echo $filter_tingkat === 'sedang' ? 'selected' : ''; ?>>Sedang</option>
                            <option value="berat" <?php echo $filter_tingkat === 'berat' ? 'selected' : ''; ?>>Berat</option>
                        </select>
                        <?php if (!empty($filter_status)): ?>
                            <input type="hidden" name="status" value="<?php echo htmlspecialchars($filter_status); ?>">
                        <?php endif; ?>
                        <?php if (!empty($search)): ?>
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                        <?php endif; ?>
                    </form>
                </div>
                <div class="col-md-4">
                    <form method="get" class="d-flex">
                        <input type="hidden" name="status" value="<?php echo $filter_status; ?>">
                        <input type="hidden" name="tingkat" value="<?php echo $filter_tingkat; ?>">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="Cari nama, NIK, atau judul..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="btn btn-outline-secondary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                <div class="col-md-2">
                    <?php if (!empty($search) || !empty($filter_tingkat) || $filter_status !== 'aktif'): ?>
                        <a href="kelola_status_pesan.php" class="btn btn-outline-danger">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Karyawan</th>
                            <th>Judul</th>
                            <th>Tingkat</th>
                            <th>Status</th>
                            <th>Tanggal Dibuat</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($pesan_list)): ?>
                            <tr>
                                <td colspan="7" class="text-center">
                                    <div class="py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">Tidak ada data</h5>
                                        <p class="text-muted">Tidak ada pesan peringatan yang sesuai dengan filter.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($pesan_list as $index => $pesan): ?>
                                <tr class="<?php echo $pesan['tingkat_peringatan'] === 'berat' ? 'pesan-berat-row' : ''; ?>">
                                    <td><?php echo $index + 1; ?></td>
                                    <td>
                                        <strong><?php echo $pesan['nama']; ?></strong><br>
                                        <small class="text-muted"><?php echo $pesan['nik']; ?> | <?php echo $pesan['bidang']; ?></small>
                                    </td>
                                    <td><?php echo $pesan['judul']; ?></td>
                                    <td>
                                        <span class="badge badge-tingkat-<?php echo $pesan['tingkat_peringatan']; ?>">
                                            <?php echo ucfirst($pesan['tingkat_peringatan']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-status-<?php echo $pesan['status']; ?>">
                                            <?php echo ucfirst($pesan['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($pesan['tanggal_dibuat'])); ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <?php if ($pesan['status'] === 'aktif'): ?>
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="pesan_id" value="<?php echo $pesan['id']; ?>">
                                                    <input type="hidden" name="action" value="selesai">
                                                    <button type="submit" class="btn btn-sm btn-success" 
                                                            onclick="return confirm('Apakah Anda yakin ingin menyelesaikan pesan ini?')">
                                                        <i class="fas fa-check"></i> Selesaikan
                                                    </button>
                                                </form>
                                            <?php elseif ($pesan['status'] === 'selesai'): ?>
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="pesan_id" value="<?php echo $pesan['id']; ?>">
                                                    <input type="hidden" name="action" value="aktifkan">
                                                    <button type="submit" class="btn btn-sm btn-warning" 
                                                            onclick="return confirm('Apakah Anda yakin ingin mengaktifkan kembali pesan ini?')">
                                                        <i class="fas fa-undo"></i> Aktifkan
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            
                                            <a href="detail_pesan_peringatan.php?id=<?php echo $pesan['id']; ?>" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Detail
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable jika ada data
    <?php if (!empty($pesan_list)): ?>
    $('#dataTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        },
        "order": [[ 5, "desc" ]], // Sort by tanggal dibuat
        "pageLength": 25,
        "columnDefs": [
            { "orderable": false, "targets": [6] } // Disable sorting for action column
        ]
    });
    <?php endif; ?>
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
