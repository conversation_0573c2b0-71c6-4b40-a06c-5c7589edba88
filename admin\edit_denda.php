<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Cek apakah ada parameter id
if (!isset($_GET['id'])) {
    setMessage('danger', 'ID denda tidak valid');
    redirect('denda.php');
}

$id = clean($_GET['id']);

// Ambil data denda berdasarkan id
$query = "SELECT * FROM denda WHERE id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Denda tidak ditemukan');
    redirect('denda.php');
}

$denda = mysqli_fetch_assoc($result);

// Proses edit denda
if (isset($_POST['edit'])) {
    $denda_masuk = clean($_POST['denda_masuk']);
    $denda_pulang = clean($_POST['denda_pulang']);
    $denda_tidak_absen = clean($_POST['denda_tidak_absen']);
    $keterangan = clean($_POST['keterangan']);
    
    // Validasi input
    if (empty($denda_masuk) || empty($denda_pulang) || empty($denda_tidak_absen)) {
        setMessage('danger', 'Semua field denda harus diisi');
        redirect("edit_denda.php?id=$id");
    }
    
    // Validasi nilai denda (harus angka)
    if (!is_numeric($denda_masuk) || !is_numeric($denda_pulang) || !is_numeric($denda_tidak_absen)) {
        setMessage('danger', 'Nilai denda harus berupa angka');
        redirect("edit_denda.php?id=$id");
    }
    
    // Update denda
    $query = "UPDATE denda SET 
              denda_masuk = '$denda_masuk', 
              denda_pulang = '$denda_pulang', 
              denda_tidak_absen = '$denda_tidak_absen', 
              keterangan = '$keterangan' 
              WHERE id = '$id'";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Denda berhasil diupdate');
        redirect('denda.php');
    } else {
        setMessage('danger', 'Gagal mengupdate denda: ' . mysqli_error($conn));
        redirect("edit_denda.php?id=$id");
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Edit Denda</h1>
        <a href="denda.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Form Edit Denda</h6>
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="denda_masuk" class="form-label">Denda Terlambat Masuk (Rp)</label>
                            <input type="number" class="form-control" id="denda_masuk" name="denda_masuk" value="<?php echo $denda['denda_masuk']; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="denda_pulang" class="form-label">Denda Pulang Awal (Rp)</label>
                            <input type="number" class="form-control" id="denda_pulang" name="denda_pulang" value="<?php echo $denda['denda_pulang']; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="denda_tidak_absen" class="form-label">Denda Tidak Absen (Rp)</label>
                            <input type="number" class="form-control" id="denda_tidak_absen" name="denda_tidak_absen" value="<?php echo $denda['denda_tidak_absen']; ?>" required>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="keterangan" class="form-label">Keterangan</label>
                    <textarea class="form-control" id="keterangan" name="keterangan" rows="3"><?php echo $denda['keterangan']; ?></textarea>
                </div>

                <div class="mt-3">
                    <button type="submit" name="edit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                    <a href="denda.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
