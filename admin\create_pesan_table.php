<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses admin
checkAccess('admin');

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        Buat Tabel Pesan Peringatan
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Script ini akan membuat tabel untuk menyimpan pesan peringatan karyawan.
                    </div>

                    <?php
                    // SQL untuk membuat tabel pesan_peringatan
                    $sql = "CREATE TABLE IF NOT EXISTS pesan_peringatan (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        judul VARCHAR(255) NOT NULL,
                        pesan TEXT NOT NULL,
                        jenis_pelanggaran ENUM('terlambat', 'tidak_absen', 'pulang_awal', 'lainnya') NOT NULL,
                        tingkat_peringatan ENUM('ringan', 'sedang', 'berat') NOT NULL DEFAULT 'ringan',
                        status ENUM('aktif', 'dibaca', 'selesai') NOT NULL DEFAULT 'aktif',
                        tanggal_dibuat DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        tanggal_dibaca DATETIME NULL,
                        dibuat_oleh INT NOT NULL,
                        keterangan TEXT NULL,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY (dibuat_oleh) REFERENCES users(id) ON DELETE CASCADE,
                        INDEX idx_user_status (user_id, status),
                        INDEX idx_tanggal (tanggal_dibuat)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

                    if (mysqli_query($conn, $sql)) {
                        echo "<div class='alert alert-success'>";
                        echo "<i class='fas fa-check-circle me-2'></i>";
                        echo "Tabel pesan_peringatan berhasil dibuat atau sudah ada.";
                        echo "</div>";

                        // Cek struktur tabel
                        $check_query = "DESCRIBE pesan_peringatan";
                        $result = mysqli_query($conn, $check_query);
                        
                        if ($result) {
                            echo "<div class='alert alert-info'>";
                            echo "<h6>Struktur Tabel pesan_peringatan:</h6>";
                            echo "<div class='table-responsive'>";
                            echo "<table class='table table-sm table-bordered'>";
                            echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
                            echo "<tbody>";
                            while ($row = mysqli_fetch_assoc($result)) {
                                echo "<tr>";
                                echo "<td>" . $row['Field'] . "</td>";
                                echo "<td>" . $row['Type'] . "</td>";
                                echo "<td>" . $row['Null'] . "</td>";
                                echo "<td>" . $row['Key'] . "</td>";
                                echo "<td>" . $row['Default'] . "</td>";
                                echo "<td>" . $row['Extra'] . "</td>";
                                echo "</tr>";
                            }
                            echo "</tbody></table>";
                            echo "</div>";
                            echo "</div>";
                        }
                    } else {
                        echo "<div class='alert alert-danger'>";
                        echo "<i class='fas fa-times-circle me-2'></i>";
                        echo "Error membuat tabel: " . mysqli_error($conn);
                        echo "</div>";
                    }
                    ?>
                    
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Kembali ke Dashboard
                        </a>
                        <a href="pesan_peringatan.php" class="btn btn-success">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Kelola Pesan Peringatan
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
