<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Filter data
$filter_tanggal_awal = isset($_GET['tanggal_awal']) ? clean($_GET['tanggal_awal']) : date('Y-m-01');
$filter_tanggal_akhir = isset($_GET['tanggal_akhir']) ? clean($_GET['tanggal_akhir']) : date('Y-m-t');
$filter_bidang_id = isset($_GET['bidang_id']) ? clean($_GET['bidang_id']) : '';
$filter_user_id = isset($_GET['user_id']) ? clean($_GET['user_id']) : '';
$filter_status = isset($_GET['status']) ? clean($_GET['status']) : '';

// Ambil data bidang untuk filter
$query_bidang = "SELECT * FROM bidang ORDER BY nama_bidang";
$result_bidang = mysqli_query($conn, $query_bidang);
$bidang = [];
while ($row = mysqli_fetch_assoc($result_bidang)) {
    $bidang[] = $row;
}

// Jika tombol export ditekan
if (isset($_POST['export'])) {
    // Set header untuk download file CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=aktivitas_karyawan_' . date('Y-m-d') . '.csv');

    // Buat file pointer untuk output
    $output = fopen('php://output', 'w');

    // Tambahkan BOM untuk UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Tambahkan header kolom
    fputcsv($output, ['No', 'NIK', 'Nama', 'Bidang', 'Tanggal', 'Total Aktivitas', 'Dalam Radius', 'Luar Radius', 'Persentase Dalam Radius (%)']);

    // Query untuk mendapatkan data karyawan
    $query_karyawan = "SELECT u.id, u.nik, u.nama, b.nama_bidang
                      FROM users u
                      LEFT JOIN bidang b ON u.bidang_id = b.id
                      WHERE u.role = 'karyawan'";

    // Tambahkan filter bidang jika ada
    if (!empty($filter_bidang_id)) {
        $query_karyawan .= " AND u.bidang_id = '$filter_bidang_id'";
    }

    $query_karyawan .= " ORDER BY u.nama";
    $result_karyawan = mysqli_query($conn, $query_karyawan);

    $no = 1;
    while ($karyawan = mysqli_fetch_assoc($result_karyawan)) {
        // Query untuk menghitung total aktivitas
        $query_total = "SELECT COUNT(*) as total FROM aktivitas_karyawan
                        WHERE user_id = '{$karyawan['id']}'
                        AND tanggal BETWEEN '$filter_tanggal_awal' AND '$filter_tanggal_akhir'";
        $result_total = mysqli_query($conn, $query_total);
        $total_aktivitas = mysqli_fetch_assoc($result_total)['total'];

        // Query untuk menghitung aktivitas dalam radius
        $query_dalam = "SELECT COUNT(*) as total FROM aktivitas_karyawan
                        WHERE user_id = '{$karyawan['id']}'
                        AND tanggal BETWEEN '$filter_tanggal_awal' AND '$filter_tanggal_akhir'
                        AND status = 'di dalam radius'";
        $result_dalam = mysqli_query($conn, $query_dalam);
        $dalam_radius = mysqli_fetch_assoc($result_dalam)['total'];

        // Hitung aktivitas luar radius dan persentase
        $luar_radius = $total_aktivitas - $dalam_radius;
        $persentase = ($total_aktivitas > 0) ? round(($dalam_radius / $total_aktivitas) * 100, 2) : 0;

        // Tambahkan data ke CSV
        fputcsv($output, [
            $no++,
            $karyawan['nik'],
            $karyawan['nama'],
            $karyawan['nama_bidang'],
            $filter_tanggal_awal . ' s/d ' . $filter_tanggal_akhir,
            $total_aktivitas,
            $dalam_radius,
            $luar_radius,
            $persentase
        ]);
    }

    fclose($output);
    exit;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Export Data Aktivitas Karyawan</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="aktivitas_karyawan.php">Aktivitas Karyawan</a></li>
        <li class="breadcrumb-item active">Export Data</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Data
        </div>
        <div class="card-body">
            <form method="get" action="" class="row g-3">
                <div class="col-md-3">
                    <label for="tanggal_awal" class="form-label">Tanggal Awal</label>
                    <input type="date" class="form-control" id="tanggal_awal" name="tanggal_awal" value="<?php echo $filter_tanggal_awal; ?>">
                </div>
                <div class="col-md-3">
                    <label for="tanggal_akhir" class="form-label">Tanggal Akhir</label>
                    <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?php echo $filter_tanggal_akhir; ?>">
                </div>
                <div class="col-md-3">
                    <label for="bidang_id" class="form-label">Bidang</label>
                    <select class="form-select" id="bidang_id" name="bidang_id">
                        <option value="">Semua Bidang</option>
                        <?php foreach ($bidang as $b): ?>
                            <option value="<?php echo $b['id']; ?>" <?php echo $filter_bidang_id == $b['id'] ? 'selected' : ''; ?>>
                                <?php echo $b['nama_bidang']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <a href="export_aktivitas.php" class="btn btn-secondary">
                        <i class="fas fa-sync-alt me-1"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Ringkasan Aktivitas Karyawan
        </div>
        <div class="card-body">
            <div class="mb-3">
                <form method="post" action="">
                    <input type="hidden" name="tanggal_awal" value="<?php echo $filter_tanggal_awal; ?>">
                    <input type="hidden" name="tanggal_akhir" value="<?php echo $filter_tanggal_akhir; ?>">
                    <input type="hidden" name="bidang_id" value="<?php echo $filter_bidang_id; ?>">
                    <input type="hidden" name="user_id" value="<?php echo $filter_user_id; ?>">
                    <input type="hidden" name="status" value="<?php echo $filter_status; ?>">
                    <button type="submit" name="export" class="btn btn-success">
                        <i class="fas fa-file-export me-1"></i> Export ke CSV
                    </button>
                </form>
            </div>

            <div class="alert alert-info">
                <p><strong>Periode:</strong> <?php echo date('d/m/Y', strtotime($filter_tanggal_awal)); ?> s/d <?php echo date('d/m/Y', strtotime($filter_tanggal_akhir)); ?></p>
                <p><strong>Bidang:</strong> <?php echo !empty($filter_bidang_id) ? getBidangName($conn, $filter_bidang_id) : 'Semua Bidang'; ?></p>
                <?php if (!empty($filter_user_id)): ?>
                <p><strong>Karyawan:</strong> <?php echo getUserName($conn, $filter_user_id); ?></p>
                <?php endif; ?>
                <?php if (!empty($filter_status)): ?>
                <p><strong>Status:</strong> <?php echo $filter_status == 'di dalam radius' ? 'Di Dalam Radius' : 'Di Luar Radius'; ?></p>
                <?php endif; ?>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-datatable" id="aktivitasTable">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Bidang</th>
                            <th>Total Aktivitas</th>
                            <th>Dalam Radius</th>
                            <th>Luar Radius</th>
                            <th>Persentase Dalam Radius</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Query untuk mendapatkan data karyawan
                        $query_karyawan = "SELECT u.id, u.nik, u.nama, b.nama_bidang
                                          FROM users u
                                          LEFT JOIN bidang b ON u.bidang_id = b.id
                                          WHERE u.role = 'karyawan'";

                        // Tambahkan filter bidang jika ada
                        if (!empty($filter_bidang_id)) {
                            $query_karyawan .= " AND u.bidang_id = '$filter_bidang_id'";
                        }

                        // Tambahkan filter user_id jika ada
                        if (!empty($filter_user_id)) {
                            $query_karyawan .= " AND u.id = '$filter_user_id'";
                        }

                        $query_karyawan .= " ORDER BY u.nama";
                        $result_karyawan = mysqli_query($conn, $query_karyawan);

                        $no = 1;
                        while ($karyawan = mysqli_fetch_assoc($result_karyawan)):
                            // Query untuk menghitung total aktivitas
                            $query_total = "SELECT COUNT(*) as total FROM aktivitas_karyawan
                                            WHERE user_id = '{$karyawan['id']}'
                                            AND tanggal BETWEEN '$filter_tanggal_awal' AND '$filter_tanggal_akhir'";

                            // Tambahkan filter status jika ada
                            if (!empty($filter_status)) {
                                $query_total .= " AND status = '$filter_status'";
                            }
                            $result_total = mysqli_query($conn, $query_total);
                            $total_aktivitas = mysqli_fetch_assoc($result_total)['total'];

                            // Query untuk menghitung aktivitas dalam radius
                            $query_dalam = "SELECT COUNT(*) as total FROM aktivitas_karyawan
                                            WHERE user_id = '{$karyawan['id']}'
                                            AND tanggal BETWEEN '$filter_tanggal_awal' AND '$filter_tanggal_akhir'";

                            // Jika ada filter status, gunakan filter tersebut, jika tidak, hitung yang di dalam radius
                            if (!empty($filter_status)) {
                                if ($filter_status == 'di dalam radius') {
                                    $query_dalam .= " AND status = 'di dalam radius'";
                                } else {
                                    $query_dalam .= " AND status = 'di luar radius'";
                                }
                            } else {
                                $query_dalam .= " AND status = 'di dalam radius'";
                            }
                            $result_dalam = mysqli_query($conn, $query_dalam);
                            $dalam_radius = mysqli_fetch_assoc($result_dalam)['total'];

                            // Hitung aktivitas luar radius dan persentase
                            $luar_radius = $total_aktivitas - $dalam_radius;
                            $persentase = ($total_aktivitas > 0) ? round(($dalam_radius / $total_aktivitas) * 100, 2) : 0;
                        ?>
                        <tr>
                            <td><?php echo $no++; ?></td>
                            <td><?php echo $karyawan['nik']; ?></td>
                            <td><?php echo $karyawan['nama']; ?></td>
                            <td><?php echo $karyawan['nama_bidang']; ?></td>
                            <td><?php echo $total_aktivitas; ?></td>
                            <td><?php echo $dalam_radius; ?></td>
                            <td><?php echo $luar_radius; ?></td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $persentase; ?>%;" aria-valuenow="<?php echo $persentase; ?>" aria-valuemin="0" aria-valuemax="100"><?php echo $persentase; ?>%</div>
                                </div>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';

// Fungsi untuk mendapatkan nama bidang berdasarkan ID
function getBidangName($conn, $bidang_id) {
    $query = "SELECT nama_bidang FROM bidang WHERE id = '$bidang_id'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result)['nama_bidang'];
    }
    return 'Tidak Ada';
}

// Fungsi untuk mendapatkan nama karyawan berdasarkan ID
function getUserName($conn, $user_id) {
    $query = "SELECT nik, nama FROM users WHERE id = '$user_id'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);
        return $user['nik'] . ' - ' . $user['nama'];
    }
    return 'Tidak Ada';
}
?>
