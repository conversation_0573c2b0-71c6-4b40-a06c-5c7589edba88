<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses tambah lokasi
if (isset($_POST['tambah'])) {
    $nama_lokasi = clean($_POST['nama_lokasi']);
    $longitude = clean($_POST['longitude']);
    $latitude = clean($_POST['latitude']);
    $radius = clean($_POST['radius']);

    // Cek apakah nama lokasi sudah terdaftar
    $query = "SELECT * FROM lokasi WHERE nama_lokasi = '$nama_lokasi'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Nama lokasi sudah terdaftar!');
        redirect('admin/lokasi.php');
    }

    // Insert data lokasi baru
    $query = "INSERT INTO lokasi (nama_lokasi, longitude, latitude, radius) VALUES ('$nama_lokasi', '$longitude', '$latitude', '$radius')";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Lokasi berhasil ditambahkan!');
    } else {
        setMessage('danger', 'Gagal menambahkan lokasi!');
    }

    redirect('admin/lokasi.php');
}

// Proses edit lokasi
if (isset($_POST['edit'])) {
    $id = clean($_POST['id']);
    $nama_lokasi = clean($_POST['nama_lokasi']);
    $longitude = clean($_POST['longitude']);
    $latitude = clean($_POST['latitude']);
    $radius = clean($_POST['radius']);

    // Cek apakah nama lokasi sudah terdaftar (selain lokasi ini)
    $query = "SELECT * FROM lokasi WHERE nama_lokasi = '$nama_lokasi' AND id != '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Nama lokasi sudah terdaftar!');
        redirect('admin/lokasi.php');
    }

    // Update data lokasi
    $query = "UPDATE lokasi SET nama_lokasi = '$nama_lokasi', longitude = '$longitude', latitude = '$latitude', radius = '$radius' WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Data lokasi berhasil diperbarui!');
    } else {
        setMessage('danger', 'Gagal memperbarui data lokasi!');
    }

    redirect('admin/lokasi.php');
}

// Proses hapus lokasi
if (isset($_GET['hapus'])) {
    $id = clean($_GET['hapus']);

    // Cek apakah lokasi digunakan oleh karyawan
    $query = "SELECT * FROM users WHERE lokasi_id = '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Lokasi tidak dapat dihapus karena digunakan oleh karyawan!');
        redirect('admin/lokasi.php');
    }

    // Hapus lokasi
    $query = "DELETE FROM lokasi WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Lokasi berhasil dihapus!');
    } else {
        setMessage('danger', 'Gagal menghapus lokasi!');
    }

    redirect('admin/lokasi.php');
}

// Ambil data lokasi
$query = "SELECT * FROM lokasi ORDER BY nama_lokasi ASC";
$result = mysqli_query($conn, $query);

$lokasi = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $lokasi[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Manajemen Lokasi</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#tambahLokasiModal">
            <i class="fas fa-plus"></i> Tambah Lokasi
        </button>
    </div>

    <!-- Peta Lokasi -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Peta Lokasi</h6>
        </div>
        <div class="card-body">
            <div id="map" style="height: 400px;"></div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Data Lokasi</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Nama Lokasi</th>
                            <th>Longitude</th>
                            <th>Latitude</th>
                            <th>Radius (meter)</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($lokasi)): ?>
                            <tr>
                                <td colspan="5" class="text-center">Tidak ada data lokasi</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($lokasi as $l): ?>
                                <tr>
                                    <td><?php echo $l['nama_lokasi']; ?></td>
                                    <td><?php echo $l['longitude']; ?></td>
                                    <td><?php echo $l['latitude']; ?></td>
                                    <td><?php echo $l['radius']; ?></td>
                                    <td>
                                        <a href="edit_lokasi.php?id=<?php echo $l['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="lokasi.php?hapus=<?php echo $l['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus lokasi ini?')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </a>
                                    </td>
                                </tr>


                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Lokasi -->
<div class="modal fade" id="tambahLokasiModal" tabindex="-1" aria-labelledby="tambahLokasiModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tambahLokasiModalLabel">Tambah Lokasi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nama_lokasi" class="form-label">Nama Lokasi</label>
                        <input type="text" class="form-control" id="nama_lokasi" name="nama_lokasi" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="text" class="form-control" id="longitude" name="longitude" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="text" class="form-control" id="latitude" name="latitude" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="radius" class="form-label">Radius (meter)</label>
                        <input type="number" class="form-control" id="radius" name="radius" value="100" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Pilih Lokasi di Peta</label>
                        <div id="map-tambah" style="height: 300px;"></div>
                        <small class="text-muted">Klik pada peta untuk menentukan lokasi</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="tambah" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<script>
    // Inisialisasi peta utama
    var map = L.map('map').setView([-6.2088, 106.8456], 13);

    // Tambahkan layer peta dasar
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Variabel untuk menyimpan marker dan circle
    var markers = [];
    var circles = [];

    // Tambahkan marker dan circle untuk setiap lokasi
    <?php foreach ($lokasi as $l): ?>
    (function() {
        var lat = <?php echo $l['latitude']; ?>;
        var lng = <?php echo $l['longitude']; ?>;
        var radius = <?php echo $l['radius']; ?>;
        var nama = "<?php echo $l['nama_lokasi']; ?>";

        // Tambahkan marker
        var marker = L.marker([lat, lng]).addTo(map);
        marker.bindPopup("<b>" + nama + "</b><br>Radius: " + radius + " meter");
        markers.push(marker);

        // Tambahkan circle untuk menunjukkan radius
        var circle = L.circle([lat, lng], {
            color: 'blue',
            fillColor: '#30f',
            fillOpacity: 0.2,
            radius: radius
        }).addTo(map);
        circles.push(circle);
    })();
    <?php endforeach; ?>

    // Jika ada lokasi, atur tampilan peta untuk menampilkan semua lokasi
    <?php if (!empty($lokasi)): ?>
    var group = new L.featureGroup(markers);
    map.fitBounds(group.getBounds().pad(0.5));
    <?php endif; ?>

    // Inisialisasi peta untuk modal tambah lokasi
    var mapTambah;
    var markerTambah;
    var circleTambah;

    // Fungsi untuk menginisialisasi peta tambah lokasi
    function initMapTambah() {
        // Hapus peta lama jika ada
        if (mapTambah) {
            mapTambah.remove();
        }

        // Inisialisasi peta baru
        mapTambah = L.map('map-tambah').setView([-6.2088, 106.8456], 13);

        // Tambahkan layer peta dasar
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(mapTambah);

        // Tambahkan event click pada peta
        mapTambah.on('click', function(e) {
            var lat = e.latlng.lat;
            var lng = e.latlng.lng;

            // Update nilai input
            document.getElementById('latitude').value = lat.toFixed(6);
            document.getElementById('longitude').value = lng.toFixed(6);

            // Hapus marker dan circle lama jika ada
            if (markerTambah) {
                mapTambah.removeLayer(markerTambah);
            }
            if (circleTambah) {
                mapTambah.removeLayer(circleTambah);
            }

            // Tambahkan marker baru
            markerTambah = L.marker([lat, lng]).addTo(mapTambah);

            // Tambahkan circle baru
            var radius = parseInt(document.getElementById('radius').value) || 100;
            circleTambah = L.circle([lat, lng], {
                color: 'blue',
                fillColor: '#30f',
                fillOpacity: 0.2,
                radius: radius
            }).addTo(mapTambah);
        });

        // Event listener untuk perubahan radius
        document.getElementById('radius').addEventListener('input', function() {
            var radius = parseInt(this.value) || 100;
            if (circleTambah) {
                circleTambah.setRadius(radius);
            }
        });
    }

    // Event listener untuk modal tambah lokasi
    $('#tambahLokasiModal').on('shown.bs.modal', function() {
        setTimeout(function() {
            initMapTambah();
        }, 300);
    });

    // Kode untuk edit lokasi telah dipindahkan ke halaman terpisah
</script>
