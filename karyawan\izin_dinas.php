<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('karyawan');

// Cek apakah kolom foto_surat_tugas dan foto_wajah sudah ada di tabel izin_dinas
$result = mysqli_query($conn, "SHOW COLUMNS FROM izin_dinas LIKE 'foto_surat_tugas'");
if (mysqli_num_rows($result) == 0) {
    // Tambahkan kolom foto_surat_tugas dan foto_wajah
    $query = "ALTER TABLE izin_dinas
              ADD COLUMN foto_surat_tugas VARCHAR(255) NULL AFTER keterangan,
              ADD COLUMN foto_wajah VARCHAR(255) NULL AFTER foto_surat_tugas";
    mysqli_query($conn, $query);
}

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Proses pembatalan izin dinas
if (isset($_POST['batalkan']) && isset($_POST['izin_id'])) {
    // Debug: Tampilkan data yang diterima
    // echo "Menerima permintaan pembatalan untuk izin ID: " . $_POST['izin_id'];
    $izin_id = clean($_POST['izin_id']);

    // Cek apakah izin dinas ada dan milik user yang login
    $query = "SELECT * FROM izin_dinas WHERE id = '$izin_id' AND user_id = '$user_id' AND status = 'Pending'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        $izin = mysqli_fetch_assoc($result);

        // Hapus foto surat tugas dan foto wajah jika ada
        if (!empty($izin['foto_surat_tugas'])) {
            $file_path = '../uploads/' . $izin['foto_surat_tugas'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }

        if (!empty($izin['foto_wajah'])) {
            $file_path = '../uploads/' . $izin['foto_wajah'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }

        // Hapus data izin dinas
        $query = "DELETE FROM izin_dinas WHERE id = '$izin_id' AND user_id = '$user_id' AND status = 'Pending'";

        // Debug: Tampilkan query
        // echo "Query: " . $query;

        $result = mysqli_query($conn, $query);

        if ($result && mysqli_affected_rows($conn) > 0) {
            // Simpan pesan sukses dalam session untuk ditampilkan sebagai SweetAlert
            $_SESSION['swal_type'] = 'success';
            $_SESSION['swal_title'] = 'Berhasil';
            $_SESSION['swal_text'] = 'Pengajuan izin perjalanan dinas berhasil dibatalkan!';
        } else {
            // Simpan pesan error dalam session untuk ditampilkan sebagai SweetAlert
            $_SESSION['swal_type'] = 'error';
            $_SESSION['swal_title'] = 'Gagal';
            $_SESSION['swal_text'] = 'Gagal membatalkan pengajuan izin perjalanan dinas: ' . mysqli_error($conn);
            // Debug: Tampilkan error
            // echo "Error: " . mysqli_error($conn);
        }
    } else {
        // Simpan pesan error dalam session untuk ditampilkan sebagai SweetAlert
        $_SESSION['swal_type'] = 'error';
        $_SESSION['swal_title'] = 'Gagal';
        $_SESSION['swal_text'] = 'Izin perjalanan dinas tidak ditemukan atau sudah diproses!';
    }

    redirect('karyawan/izin_dinas.php');
}

// Proses pengajuan izin dinas
if (isset($_POST['ajukan'])) {
    $tujuan = clean($_POST['tujuan']);
    $keterangan = clean($_POST['keterangan']);

    // Cek apakah ini pengajuan untuk hari ini (setelah absen masuk)
    $is_today = isset($_POST['is_today']) ? true : false;

    if ($is_today) {
        $tanggal_mulai = date('Y-m-d');
        $tanggal_selesai = date('Y-m-d');

        // Cek apakah hari ini sudah ada presensi masuk
        $query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$tanggal_mulai'";
        $result = mysqli_query($conn, $query);
        $presensi_hari_ini = mysqli_fetch_assoc($result);

        if (!$presensi_hari_ini || empty($presensi_hari_ini['jam_masuk'])) {
            // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
            $_SESSION['swal_type'] = 'error';
            $_SESSION['swal_title'] = 'Pengajuan Gagal';
            $_SESSION['swal_text'] = 'Anda belum melakukan absen masuk hari ini!';
            redirect('karyawan/izin_dinas.php');
        }

        if (!empty($presensi_hari_ini['jam_pulang'])) {
            // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
            $_SESSION['swal_type'] = 'error';
            $_SESSION['swal_title'] = 'Pengajuan Gagal';
            $_SESSION['swal_text'] = 'Anda sudah melakukan absen pulang hari ini!';
            redirect('karyawan/izin_dinas.php');
        }
    } else {
        $tanggal_mulai = clean($_POST['tanggal_mulai']);
        $tanggal_selesai = clean($_POST['tanggal_selesai']);

        // Validasi tanggal
        if (strtotime($tanggal_mulai) > strtotime($tanggal_selesai)) {
            setMessage('danger', 'Tanggal mulai tidak boleh lebih besar dari tanggal selesai!');
            redirect('karyawan/izin_dinas.php');
        }
    }

    // Cek apakah sudah ada izin dinas yang overlap
    $query = "SELECT * FROM izin_dinas
              WHERE user_id = '$user_id'
              AND status != 'Rejected'
              AND (
                  (tanggal_mulai <= '$tanggal_mulai' AND tanggal_selesai >= '$tanggal_mulai') OR
                  (tanggal_mulai <= '$tanggal_selesai' AND tanggal_selesai >= '$tanggal_selesai') OR
                  (tanggal_mulai >= '$tanggal_mulai' AND tanggal_selesai <= '$tanggal_selesai')
              )";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
        $_SESSION['swal_type'] = 'error';
        $_SESSION['swal_title'] = 'Pengajuan Gagal';
        $_SESSION['swal_text'] = 'Anda sudah memiliki izin dinas pada rentang tanggal tersebut!';
        redirect('karyawan/izin_dinas.php');
    }

    // Inisialisasi variabel untuk foto
    $foto_surat_tugas = null;
    $foto_wajah = null;

    // Proses foto surat tugas dari data base64
    if (isset($_POST['foto_surat_tugas']) && !empty($_POST['foto_surat_tugas'])) {
        $foto_data = $_POST['foto_surat_tugas'];

        // Hapus header data URI jika ada
        if (strpos($foto_data, 'data:image/jpeg;base64,') === 0) {
            $foto_data = substr($foto_data, strlen('data:image/jpeg;base64,'));
        } elseif (strpos($foto_data, 'data:image/png;base64,') === 0) {
            $foto_data = substr($foto_data, strlen('data:image/png;base64,'));
        }

        // Decode base64
        $foto_binary = base64_decode($foto_data);

        if ($foto_binary === false) {
            setMessage('danger', 'Format foto surat tugas tidak valid!');
            redirect('karyawan/izin_dinas.php');
        }

        // Simpan foto surat tugas
        $upload_dir = '../uploads/';
        $file_name = 'surat_tugas_' . $user_id . '_' . time() . '.jpg';
        $file_path = $upload_dir . $file_name;

        // Cek apakah direktori uploads ada, jika tidak buat direktori
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Simpan file
        if (file_put_contents($file_path, $foto_binary)) {
            $foto_surat_tugas = $file_name;
        } else {
            setMessage('danger', 'Gagal menyimpan foto surat tugas!');
            redirect('karyawan/izin_dinas.php');
        }
    } else {
        setMessage('danger', 'Foto surat tugas wajib diambil!');
        redirect('karyawan/izin_dinas.php');
    }

    // Proses foto wajah dari data base64
    if (isset($_POST['foto_wajah']) && !empty($_POST['foto_wajah'])) {
        $foto_data = $_POST['foto_wajah'];

        // Hapus header data URI jika ada
        if (strpos($foto_data, 'data:image/jpeg;base64,') === 0) {
            $foto_data = substr($foto_data, strlen('data:image/jpeg;base64,'));
        } elseif (strpos($foto_data, 'data:image/png;base64,') === 0) {
            $foto_data = substr($foto_data, strlen('data:image/png;base64,'));
        }

        // Decode base64
        $foto_binary = base64_decode($foto_data);

        if ($foto_binary === false) {
            setMessage('danger', 'Format foto wajah tidak valid!');
            redirect('karyawan/izin_dinas.php');
        }

        // Simpan foto wajah
        $upload_dir = '../uploads/';
        $file_name = 'wajah_izin_' . $user_id . '_' . time() . '.jpg';
        $file_path = $upload_dir . $file_name;

        // Cek apakah direktori uploads ada, jika tidak buat direktori
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Simpan file
        if (file_put_contents($file_path, $foto_binary)) {
            $foto_wajah = $file_name;
        } else {
            setMessage('danger', 'Gagal menyimpan foto wajah!');
            redirect('karyawan/izin_dinas.php');
        }
    } else {
        setMessage('danger', 'Foto wajah wajib diambil!');
        redirect('karyawan/izin_dinas.php');
    }

    // Insert data izin dinas dengan foto
    $query = "INSERT INTO izin_dinas (user_id, tanggal_mulai, tanggal_selesai, tujuan, keterangan, foto_surat_tugas, foto_wajah)
              VALUES ('$user_id', '$tanggal_mulai', '$tanggal_selesai', '$tujuan', '$keterangan', '$foto_surat_tugas', '$foto_wajah')";

    if (mysqli_query($conn, $query)) {
        // Simpan pesan sukses dalam session untuk ditampilkan sebagai SweetAlert
        $_SESSION['swal_type'] = 'success';
        $_SESSION['swal_title'] = 'Berhasil';
        $_SESSION['swal_text'] = 'Izin perjalanan dinas berhasil diajukan!';
    } else {
        // Simpan pesan error dalam session untuk ditampilkan sebagai SweetAlert
        $_SESSION['swal_type'] = 'error';
        $_SESSION['swal_title'] = 'Gagal';
        $_SESSION['swal_text'] = 'Gagal mengajukan izin perjalanan dinas: ' . mysqli_error($conn);
    }

    redirect('karyawan/izin_dinas.php');
}

// Ambil data izin dinas karyawan
$query = "SELECT id.*, a.nama as approved_by_name
          FROM izin_dinas id
          LEFT JOIN users a ON id.approved_by = a.id
          WHERE id.user_id = '$user_id'
          ORDER BY id.created_at DESC";
$result = mysqli_query($conn, $query);

$izin_dinas = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $izin_dinas[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="mobile-izin-container">
    <!-- Header Section -->
    <div class="izin-header">
        <div class="user-info">
            <div class="user-avatar">
                                <?php if (!empty($karyawan['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $karyawan['foto_profil']; ?>" alt="Foto Profil" style="width: 100px; height: 100px; ">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                <div class="user-position"><?php echo $karyawan['bidang'] ?? 'Karyawan'; ?></div>
            </div>
        </div>
        <div class="date-info">
            <i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?>
        </div>
    </div>

    <!-- Container -->
    <div class="container px-0">
        <!-- Izin Dinas List -->
        <h6 class="mb-3">Riwayat Izin Perjalanan Dinas</h6>

        <?php if (empty($izin_dinas)): ?>
            <div class="izin-card">
                <div class="text-center py-3">Tidak ada data izin perjalanan dinas</div>
            </div>
        <?php else: ?>
            <?php foreach ($izin_dinas as $izin): ?>
                <div class="izin-card">
                    <div class="izin-header">
                        <div class="izin-title"><?php echo $izin['tujuan']; ?></div>
                        <?php if ($izin['status'] == 'Pending'): ?>
                            <span class="status-badge warning">Pending</span>
                        <?php elseif ($izin['status'] == 'Approved'): ?>
                            <span class="status-badge success">Disetujui</span>
                        <?php else: ?>
                            <span class="status-badge danger">Ditolak</span>
                        <?php endif; ?>
                    </div>
                    <div class="izin-date">
                        <div><i class="fas fa-calendar-alt me-1"></i> <?php echo date('d M Y', strtotime($izin['tanggal_mulai'])); ?></div>
                        <div><i class="fas fa-arrow-right mx-2"></i></div>
                        <div><i class="fas fa-calendar-alt me-1"></i> <?php echo date('d M Y', strtotime($izin['tanggal_selesai'])); ?></div>
                    </div>

                    <?php if (!empty($izin['keterangan'])): ?>
                        <div class="izin-details">
                            <div class="izin-detail-item">
                                <div class="izin-detail-label">Keterangan</div>
                                <div class="izin-detail-value"><?php echo $izin['keterangan']; ?></div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Foto Surat Tugas dan Foto Wajah -->
                    <?php if (!empty($izin['foto_surat_tugas']) || !empty($izin['foto_wajah'])): ?>
                        <div class="izin-details">
                            <div class="izin-detail-label mb-2">Dokumen & Foto</div>
                            <div class="row">
                                <?php if (!empty($izin['foto_surat_tugas'])): ?>
                                    <div class="col-6">
                                        <div class="izin-detail-item">
                                            <div class="izin-detail-label">Surat Tugas</div>
                                            <div class="izin-detail-value">
                                                <a href="#" class="view-image" data-bs-toggle="modal" data-bs-target="#imageModal" data-src="<?php echo BASE_URL . 'uploads/' . $izin['foto_surat_tugas']; ?>" data-title="Surat Tugas">
                                                    <img src="<?php echo BASE_URL . 'uploads/' . $izin['foto_surat_tugas']; ?>" class="img-thumbnail" style="max-height: 100px;">
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($izin['foto_wajah'])): ?>
                                    <div class="col-6">
                                        <div class="izin-detail-item">
                                            <div class="izin-detail-label">Foto Wajah</div>
                                            <div class="izin-detail-value">
                                                <a href="#" class="view-image" data-bs-toggle="modal" data-bs-target="#imageModal" data-src="<?php echo BASE_URL . 'uploads/' . $izin['foto_wajah']; ?>" data-title="Foto Wajah">
                                                    <img src="<?php echo BASE_URL . 'uploads/' . $izin['foto_wajah']; ?>" class="img-thumbnail" style="max-height: 100px;">
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($izin['status'] == 'Pending'): ?>
                        <div class="izin-actions mt-3">
                            <form method="post" action="" id="form-batalkan-<?php echo $izin['id']; ?>">
                                <input type="hidden" name="batalkan" value="1">
                                <input type="hidden" name="izin_id" value="<?php echo $izin['id']; ?>">
                                <button type="button" class="btn-cancel" onclick="konfirmasiBatalkan(<?php echo $izin['id']; ?>)">
                                    <i class="fas fa-times"></i> Batalkan Pengajuan
                                </button>
                            </form>
                        </div>
                    <?php else: ?>
                        <div class="izin-details">
                            <div class="izin-detail-item">
                                <div class="izin-detail-label">Disetujui Oleh</div>
                                <div class="izin-detail-value"><?php echo $izin['approved_by_name'] ?? '-'; ?></div>
                            </div>
                            <?php if ($izin['approved_at']): ?>
                                <div class="izin-detail-item">
                                    <div class="izin-detail-label">Tanggal Disetujui</div>
                                    <div class="izin-detail-value"><?php echo date('d M Y H:i', strtotime($izin['approved_at'])); ?></div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- Tombol Ajukan Perjalanan Dinas Hari Ini -->
        <?php
        // Cek apakah hari ini sudah ada presensi masuk
        $today = date('Y-m-d');
        $query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
        $result = mysqli_query($conn, $query);
        $presensi_hari_ini = mysqli_fetch_assoc($result);

        // Cek apakah sudah ada izin dinas untuk hari ini
        $query = "SELECT * FROM izin_dinas
                  WHERE user_id = '$user_id'
                  AND tanggal_mulai <= '$today'
                  AND tanggal_selesai >= '$today'
                  AND status != 'Rejected'";
        $result = mysqli_query($conn, $query);
        $izin_dinas_hari_ini = mysqli_fetch_assoc($result);

        // Tampilkan tombol hanya jika sudah absen masuk, belum absen pulang, dan belum ada izin dinas hari ini
        if ($presensi_hari_ini && !empty($presensi_hari_ini['jam_masuk']) && empty($presensi_hari_ini['jam_pulang']) && !$izin_dinas_hari_ini):
        ?>
        <div class="info-section">
            <div class="info-title"><i class="fas fa-plane-departure"></i> Perjalanan Dinas Hari Ini</div>
            <div class="info-content">
                <div class="alert alert-info">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-info-circle me-3 fa-2x"></i>
                        <div>
                            <h6 class="mb-1">Sudah Absen Masuk Hari Ini?</h6>
                            <p class="mb-0">Jika Anda harus pergi untuk perjalanan dinas dan tidak bisa melakukan absen pulang, gunakan fitur ini.</p>
                        </div>
                    </div>
                    <div class="d-grid gap-2 mt-3">
                        <a href="perjalanan_dinas_hari_ini.php" class="btn btn-primary">
                            <i class="fas fa-plane-departure me-2"></i> Ajukan Perjalanan Dinas Hari Ini
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Info Section -->
        <div class="info-section">
            <div class="info-title">Ketentuan Izin Perjalanan Dinas</div>
            <div class="info-content">
                <ul>
                    <li>Izin perjalanan dinas harus diajukan sebelum tanggal keberangkatan.</li>
                    <li>Izin perjalanan dinas harus disetujui oleh admin.</li>
                    <li>Jika izin disetujui, sistem akan otomatis mengisi presensi Anda selama periode perjalanan dinas.</li>
                    <li>Presensi otomatis hanya dibuat untuk hari kerja sesuai dengan pengaturan hari kerja bidang Anda.</li>
                    <li>Jam masuk dan jam pulang akan diisi sesuai dengan jam kerja yang berlaku pada hari tersebut.</li>
                    <li>Anda juga dapat mengajukan perjalanan dinas pada hari yang sama setelah melakukan absen masuk.</li>
                </ul>
                <p class="mb-0">Pastikan untuk mengisi detail izin perjalanan dinas dengan benar.</p>
            </div>
        </div>

        <!-- Additional Quick Actions -->
       <br>
       <br>
       <br>


        <!-- Floating Action Button - hanya tampil jika belum absen masuk -->
        <?php if (!$presensi_hari_ini || empty($presensi_hari_ini['jam_masuk'])): ?>
        <a href="#" class="action-fab" data-bs-toggle="modal" data-bs-target="#ajukanIzinModal">
            <i class="fas fa-plus"></i>
        </a>
        <?php endif; ?>
    </div>
</div>

<!-- Modal Ajukan Izin Dinas -->
<div class="modal fade" id="ajukanIzinModal" tabindex="-1" aria-labelledby="ajukanIzinModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ajukanIzinModalLabel">Ajukan Izin Perjalanan Dinas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="" enctype="multipart/form-data" id="formIzinDinas">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tanggal_mulai" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="tanggal_mulai" name="tanggal_mulai" required min="<?php echo date('Y-m-d'); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="tanggal_selesai" class="form-label">Tanggal Selesai</label>
                                <input type="date" class="form-control" id="tanggal_selesai" name="tanggal_selesai" required min="<?php echo date('Y-m-d'); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="tujuan" class="form-label">Tujuan</label>
                                <input type="text" class="form-control" id="tujuan" name="tujuan" required>
                            </div>

                            <div class="mb-3">
                                <label for="keterangan" class="form-label">Keterangan</label>
                                <textarea class="form-control" id="keterangan" name="keterangan" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Foto Surat Tugas</label>
                                <div class="camera-preview" id="camera-preview-surat">
                                    <video id="video-surat" autoplay playsinline style="display: none; width: 100%; height: 100%; object-fit: cover;"></video>
                                    <canvas id="canvas-surat" style="display: none; width: 100%; height: 100%;"></canvas>
                                    <i class="fas fa-camera"></i>
                                    <div class="camera-overlay"></div>
                                    <div class="camera-message">Mengaktifkan kamera...</div>
                                </div>
                                <input type="hidden" name="foto_surat_tugas" id="foto_surat_tugas">
                                <div class="d-grid gap-2">
                                    <button type="button" id="btn-ambil-foto-surat" class="btn btn-primary">
                                        <i class="fas fa-camera"></i> Ambil Foto Surat
                                    </button>
                                    <button type="button" id="btn-ulangi-foto-surat" class="btn btn-secondary" style="display: none;">
                                        <i class="fas fa-redo"></i> Ulangi Foto
                                    </button>
                                </div>
                                <small class="text-muted">Ambil foto surat tugas Anda dengan kamera</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Foto Wajah</label>
                                <div class="camera-preview" id="camera-preview-izin">
                                    <video id="video-izin" autoplay playsinline style="display: none; width: 100%; height: 100%; object-fit: cover;"></video>
                                    <canvas id="canvas-izin" style="display: none; width: 100%; height: 100%;"></canvas>
                                    <i class="fas fa-camera"></i>
                                    <div class="camera-overlay"></div>
                                    <div class="camera-message">Mengaktifkan kamera...</div>
                                </div>
                                <input type="hidden" name="foto_wajah" id="foto_wajah">
                                <div class="d-grid gap-2">
                                    <button type="button" id="btn-ambil-foto" class="btn btn-primary">
                                        <i class="fas fa-camera"></i> Ambil Foto
                                    </button>
                                    <button type="button" id="btn-ulangi-foto" class="btn btn-secondary" style="display: none;">
                                        <i class="fas fa-redo"></i> Ulangi Foto
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="ajukan" class="btn btn-primary" id="btn-ajukan">Ajukan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.camera-preview {
    width: 100%;
    height: 300px;
    background-color: #343a40;
    border-radius: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.camera-preview i {
    font-size: 48px;
    opacity: 0.5;
    position: absolute;
    z-index: 5;
}

.camera-preview .camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px dashed rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    margin: 10px;
    pointer-events: none;
}

.camera-preview .camera-message {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 14px;
    opacity: 0.8;
    z-index: 10;
}

.camera-preview video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
    transform: scaleX(-1); /* Membalik video secara horizontal agar seperti cermin */
}

.camera-preview canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 15px;
    transform: scaleX(-1); /* Membalik canvas secara horizontal agar seperti cermin */
}

/* Untuk kamera belakang, jangan flip */
#video-surat {
    transform: scaleX(1);
}

#canvas-surat {
    transform: scaleX(1);
}

/* Perbaikan untuk Android WebView */
video {
    background-color: #000; /* Latar belakang hitam untuk video */
    object-fit: cover !important; /* Pastikan video menutupi area */
    -webkit-transform-style: preserve-3d; /* Perbaikan untuk beberapa perangkat */
    transform-style: preserve-3d;
    z-index: 1; /* Pastikan video di atas elemen lain */
}

/* Pastikan canvas juga memiliki z-index yang tepat */
canvas {
    z-index: 2; /* Canvas harus di atas video saat ditampilkan */
}

/* Perbaikan untuk beberapa perangkat Android */
.camera-preview {
    -webkit-transform: translateZ(0); /* Hardware acceleration */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Tombol Batalkan Pengajuan */
.btn-cancel {
    background-color: #e74a3b;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background-color: #c23321;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-cancel i {
    margin-right: 8px;
}

/* Foto Profil */
.izin-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    overflow: hidden;
    position: relative;
}

.izin-header .user-avatar .user-photo {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;
    max-height: 100%;
}
</style>

<script>
    // Validasi tanggal
    document.getElementById('tanggal_mulai').addEventListener('change', function() {
        document.getElementById('tanggal_selesai').min = this.value;
    });

    // Variabel untuk kamera
    // Kamera wajah
    let videoIzin = document.getElementById('video-izin');
    let canvasIzin = document.getElementById('canvas-izin');
    let cameraPreviewIzin = document.getElementById('camera-preview-izin');
    let btnAmbilFoto = document.getElementById('btn-ambil-foto');
    let btnUlangiFoto = document.getElementById('btn-ulangi-foto');
    let fotoWajahInput = document.getElementById('foto_wajah');

    // Kamera surat tugas
    let videoSurat = document.getElementById('video-surat');
    let canvasSurat = document.getElementById('canvas-surat');
    let cameraPreviewSurat = document.getElementById('camera-preview-surat');
    let btnAmbilFotoSurat = document.getElementById('btn-ambil-foto-surat');
    let btnUlangiFotoSurat = document.getElementById('btn-ulangi-foto-surat');
    let fotoSuratTugasInput = document.getElementById('foto_surat_tugas');

    // Variabel umum
    let btnAjukan = document.getElementById('btn-ajukan');
    let formIzinDinas = document.getElementById('formIzinDinas');
    let streamWajah = null;
    let streamSurat = null;
    let fotoWajahTerambil = false;
    let fotoSuratTerambil = false;

    // Fungsi untuk mengaktifkan kamera wajah
    function startCameraWajah() {
        console.log('Memulai kamera wajah');

        // Perbarui pesan
        if (cameraPreviewIzin) {
            cameraPreviewIzin.querySelector('.camera-message').textContent = 'Meminta izin kamera...';
        }

        // Matikan kamera jika sudah aktif
        if (streamWajah) {
            streamWajah.getTracks().forEach(track => track.stop());
            streamWajah = null;
        }

        // Aktifkan kamera dengan pendekatan yang lebih kompatibel dengan Android 13+
        navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'user',
                width: { ideal: 640 }, // Resolusi lebih rendah untuk kompatibilitas lebih baik
                height: { ideal: 480 },
                frameRate: { ideal: 15, max: 30 } // Batasi frame rate
            },
            audio: false
        }).then(function(stream) {
            console.log('Kamera wajah berhasil diaktifkan');
            streamWajah = stream;

            // Tampilkan video
            videoIzin.srcObject = stream;
            videoIzin.style.display = 'block';
            canvasIzin.style.display = 'none';

            if (cameraPreviewIzin.querySelector('i')) {
                cameraPreviewIzin.querySelector('i').style.display = 'none';
            }

            if (cameraPreviewIzin.querySelector('.camera-message')) {
                cameraPreviewIzin.querySelector('.camera-message').textContent = 'Kamera aktif.';
            }

            // Reset status foto
            fotoWajahTerambil = false;
            btnAmbilFoto.style.display = 'block';
            btnUlangiFoto.style.display = 'none';
            fotoWajahInput.value = '';

            // Mulai video
            videoIzin.play().then(function() {
                console.log('Video wajah berhasil diputar');
            }).catch(function(error) {
                console.error('Error playing video:', error);
                videoIzin.muted = true;
                videoIzin.play();
            });

        }).catch(function(error) {
            console.error('Error accessing camera:', error);

            // Coba fallback dengan constraint minimal
            console.log('Mencoba fallback dengan constraint minimal...');
            return navigator.mediaDevices.getUserMedia({
                video: true, // Gunakan constraint minimal
                audio: false
            }).then(function(stream) {
                console.log('Kamera wajah berhasil diaktifkan dengan fallback');
                streamWajah = stream;

                // Tampilkan video
                videoIzin.srcObject = stream;
                videoIzin.style.display = 'block';
                canvasIzin.style.display = 'none';

                if (cameraPreviewIzin.querySelector('i')) {
                    cameraPreviewIzin.querySelector('i').style.display = 'none';
                }

                if (cameraPreviewIzin.querySelector('.camera-message')) {
                    cameraPreviewIzin.querySelector('.camera-message').textContent = 'Kamera aktif dengan mode kompatibilitas.';
                }

                // Reset status foto
                fotoWajahTerambil = false;
                btnAmbilFoto.style.display = 'block';
                btnUlangiFoto.style.display = 'none';
                fotoWajahInput.value = '';

                // Mulai video
                return videoIzin.play().then(function() {
                    console.log('Video wajah berhasil diputar dengan fallback');
                }).catch(function(playError) {
                    console.error('Error playing video with fallback:', playError);
                    videoIzin.muted = true;
                    return videoIzin.play();
                });
            }).catch(function(fallbackError) {
                console.error('Fallback juga gagal:', fallbackError);

                if (cameraPreviewIzin && cameraPreviewIzin.querySelector('.camera-message')) {
                    cameraPreviewIzin.querySelector('.camera-message').textContent = 'Gagal mengakses kamera. Pastikan kamera aktif dan izin diberikan.';
                }

                // Tampilkan error yang lebih detail
                if (error.name === 'NotAllowedError' || fallbackError.name === 'NotAllowedError') {
                    alert('Akses kamera ditolak. Silakan berikan izin kamera di pengaturan browser Anda.');
                } else if (error.name === 'NotFoundError' || fallbackError.name === 'NotFoundError') {
                    alert('Kamera tidak ditemukan. Pastikan perangkat Anda memiliki kamera yang berfungsi.');
                } else if (error.name === 'NotReadableError' || fallbackError.name === 'NotReadableError' ||
                          error.message.includes('Could not start video source') ||
                          fallbackError.message.includes('Could not start video source')) {
                    alert('Tidak dapat mengakses kamera. Ini mungkin karena kamera sedang digunakan oleh aplikasi lain atau ada masalah dengan perangkat Anda. Coba tutup aplikasi lain yang mungkin menggunakan kamera atau restart perangkat Anda.');
                } else {
                    alert('Gagal mengakses kamera: ' + fallbackError.message);
                }
            });
        });
    }

    // Fungsi untuk mengaktifkan kamera surat tugas
    function startCameraSurat() {
        console.log('Memulai kamera surat tugas');

        // Perbarui pesan
        if (cameraPreviewSurat) {
            cameraPreviewSurat.querySelector('.camera-message').textContent = 'Meminta izin kamera...';
        }

        // Matikan kamera jika sudah aktif
        if (streamSurat) {
            streamSurat.getTracks().forEach(track => track.stop());
            streamSurat = null;
        }

        // Aktifkan kamera dengan pendekatan yang lebih kompatibel dengan Android 13+
        // Coba beberapa metode untuk memastikan kamera belakang digunakan

        // Cek apakah ada beberapa kamera
        navigator.mediaDevices.enumerateDevices()
        .then(function(devices) {
            // Filter untuk mendapatkan kamera video
            const videoDevices = devices.filter(device => device.kind === 'videoinput');
            console.log('Kamera yang tersedia:', videoDevices.length);

            // Jika ada lebih dari satu kamera, gunakan kamera belakang (biasanya indeks terakhir atau kedua)
            if (videoDevices.length > 1) {
                // Kamera belakang biasanya di indeks terakhir atau kedua
                const backCameraId = videoDevices[videoDevices.length - 1].deviceId;
                console.log('Mencoba menggunakan kamera belakang dengan deviceId:', backCameraId);

                return navigator.mediaDevices.getUserMedia({
                    video: {
                        deviceId: { exact: backCameraId },
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        frameRate: { ideal: 15, max: 30 }
                    },
                    audio: false
                });
            } else {
                // Jika hanya ada satu kamera, coba gunakan facingMode: 'environment'
                console.log('Hanya satu kamera terdeteksi, mencoba dengan facingMode: environment');
                return navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: { exact: 'environment' },
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        frameRate: { ideal: 15, max: 30 }
                    },
                    audio: false
                });
            }
        })
        .catch(function(err) {
            // Fallback jika enumerateDevices gagal
            console.log('Gagal mendeteksi perangkat kamera, mencoba dengan facingMode:', err);
            return navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment', // Coba tanpa exact
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                },
                audio: false
            });
        }).then(function(stream) {
            console.log('Kamera surat berhasil diaktifkan');
            streamSurat = stream;

            // Dapatkan informasi tentang track video yang digunakan
            const videoTrack = stream.getVideoTracks()[0];
            console.log('Menggunakan kamera:', videoTrack.label);

            // Tampilkan informasi kamera di konsol
            const settings = videoTrack.getSettings();
            console.log('Pengaturan kamera:', settings);

            // Cek apakah ini kamera belakang berdasarkan label
            const isBackCamera = videoTrack.label.toLowerCase().includes('back') ||
                               videoTrack.label.toLowerCase().includes('belakang') ||
                               settings.facingMode === 'environment';

            console.log('Apakah ini kamera belakang?', isBackCamera ? 'Ya' : 'Mungkin tidak');

            // Tampilkan video
            videoSurat.srcObject = stream;
            videoSurat.style.display = 'block';
            canvasSurat.style.display = 'none';

            if (cameraPreviewSurat.querySelector('i')) {
                cameraPreviewSurat.querySelector('i').style.display = 'none';
            }

            if (cameraPreviewSurat.querySelector('.camera-message')) {
                const cameraInfo = isBackCamera ? 'Kamera belakang aktif.' : 'Kamera aktif.';
                cameraPreviewSurat.querySelector('.camera-message').textContent = cameraInfo + ' Posisikan surat tugas agar terlihat jelas.';
            }

            // Reset status foto
            fotoSuratTerambil = false;
            btnAmbilFotoSurat.style.display = 'block';
            btnUlangiFotoSurat.style.display = 'none';
            fotoSuratTugasInput.value = '';

            // Mulai video
            videoSurat.play().then(function() {
                console.log('Video surat berhasil diputar');
            }).catch(function(error) {
                console.error('Error playing video:', error);
                videoSurat.muted = true;
                videoSurat.play();
            });

        }).catch(function(error) {
            console.error('Error accessing camera:', error);

            // Coba fallback dengan constraint minimal tapi tetap prioritaskan kamera belakang
            console.log('Mencoba fallback untuk kamera surat...');

            // Coba dengan pendekatan lain untuk mendapatkan kamera belakang
            return navigator.mediaDevices.enumerateDevices()
            .then(function(devices) {
                // Filter untuk mendapatkan kamera video
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                console.log('Fallback: Kamera yang tersedia:', videoDevices.length);

                if (videoDevices.length > 1) {
                    // Coba gunakan kamera terakhir (biasanya kamera belakang)
                    const backCameraId = videoDevices[videoDevices.length - 1].deviceId;
                    console.log('Fallback: Mencoba kamera dengan ID:', backCameraId);

                    return navigator.mediaDevices.getUserMedia({
                        video: { deviceId: backCameraId },
                        audio: false
                    });
                } else {
                    // Jika hanya ada satu kamera, gunakan constraint minimal
                    console.log('Fallback: Hanya satu kamera, menggunakan constraint minimal');
                    return navigator.mediaDevices.getUserMedia({
                        video: true,
                        audio: false
                    });
                }
            })
            .catch(function() {
                // Jika enumerateDevices gagal, gunakan constraint minimal
                console.log('Fallback: Gagal mendeteksi perangkat, menggunakan constraint minimal');
                return navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: false
                });
            }).then(function(stream) {
                console.log('Kamera surat berhasil diaktifkan dengan fallback');
                streamSurat = stream;

                // Dapatkan informasi tentang track video yang digunakan
                const videoTrack = stream.getVideoTracks()[0];
                console.log('Fallback: Menggunakan kamera:', videoTrack.label);

                // Tampilkan informasi kamera di konsol
                const settings = videoTrack.getSettings();
                console.log('Fallback: Pengaturan kamera:', settings);

                // Cek apakah ini kamera belakang berdasarkan label
                const isBackCamera = videoTrack.label.toLowerCase().includes('back') ||
                                   videoTrack.label.toLowerCase().includes('belakang') ||
                                   settings.facingMode === 'environment';

                console.log('Fallback: Apakah ini kamera belakang?', isBackCamera ? 'Ya' : 'Mungkin tidak');

                // Tampilkan video
                videoSurat.srcObject = stream;
                videoSurat.style.display = 'block';
                canvasSurat.style.display = 'none';

                if (cameraPreviewSurat.querySelector('i')) {
                    cameraPreviewSurat.querySelector('i').style.display = 'none';
                }

                if (cameraPreviewSurat.querySelector('.camera-message')) {
                    const cameraInfo = isBackCamera ? 'Kamera belakang aktif' : 'Kamera aktif';
                    cameraPreviewSurat.querySelector('.camera-message').textContent = cameraInfo + ' dengan mode kompatibilitas. Posisikan surat tugas agar terlihat jelas.';
                }

                // Reset status foto
                fotoSuratTerambil = false;
                btnAmbilFotoSurat.style.display = 'block';
                btnUlangiFotoSurat.style.display = 'none';
                fotoSuratTugasInput.value = '';

                // Mulai video
                return videoSurat.play().then(function() {
                    console.log('Video surat berhasil diputar dengan fallback');
                }).catch(function(playError) {
                    console.error('Error playing video with fallback:', playError);
                    videoSurat.muted = true;
                    return videoSurat.play();
                });
            }).catch(function(fallbackError) {
                console.error('Fallback juga gagal untuk kamera surat:', fallbackError);

                if (cameraPreviewSurat && cameraPreviewSurat.querySelector('.camera-message')) {
                    cameraPreviewSurat.querySelector('.camera-message').textContent = 'Gagal mengakses kamera. Pastikan kamera aktif dan izin diberikan.';
                }

                // Tampilkan error yang lebih detail
                if (error.name === 'NotAllowedError' || fallbackError.name === 'NotAllowedError') {
                    alert('Akses kamera ditolak. Silakan berikan izin kamera di pengaturan browser Anda.');
                } else if (error.name === 'NotFoundError' || fallbackError.name === 'NotFoundError') {
                    alert('Kamera tidak ditemukan. Pastikan perangkat Anda memiliki kamera yang berfungsi.');
                } else if (error.name === 'NotReadableError' || fallbackError.name === 'NotReadableError' ||
                          error.message.includes('Could not start video source') ||
                          fallbackError.message.includes('Could not start video source')) {
                    alert('Tidak dapat mengakses kamera. Ini mungkin karena kamera sedang digunakan oleh aplikasi lain atau ada masalah dengan perangkat Anda. Coba tutup aplikasi lain yang mungkin menggunakan kamera atau restart perangkat Anda.');
                } else {
                    alert('Gagal mengakses kamera: ' + fallbackError.message);
                }
            });
        });
    }

    // Fungsi untuk mengambil foto wajah
    function ambilFotoWajah() {
        try {
            console.log('Mengambil foto wajah');

            // Cek apakah elemen canvas dan video ada
            if (!canvasIzin || !videoIzin) {
                console.error('Elemen canvas atau video tidak ditemukan');
                alert('Terjadi kesalahan saat mengambil foto. Silakan coba lagi.');
                return;
            }

            // Cek apakah video sudah siap
            if (!videoIzin.videoWidth || !videoIzin.videoHeight) {
                console.error('Video belum siap, width/height: ', videoIzin.videoWidth, videoIzin.videoHeight);
                alert('Kamera belum siap. Silakan tunggu beberapa saat dan coba lagi.');
                return;
            }

            // Ambil konteks canvas
            const context = canvasIzin.getContext('2d');

            // Set ukuran canvas sesuai video
            canvasIzin.width = videoIzin.videoWidth;
            canvasIzin.height = videoIzin.videoHeight;

            // Gambar frame video ke canvas
            // Flip gambar secara horizontal untuk mengembalikan efek cermin
            context.translate(canvasIzin.width, 0);
            context.scale(-1, 1);
            context.drawImage(videoIzin, 0, 0, canvasIzin.width, canvasIzin.height);

            // Kembalikan transformasi ke normal
            context.setTransform(1, 0, 0, 1, 0, 0);

            // Tampilkan canvas dan sembunyikan video
            videoIzin.style.display = 'none';
            canvasIzin.style.display = 'block';

            // Konversi canvas ke base64
            const dataURL = canvasIzin.toDataURL('image/jpeg', 0.8);
            fotoWajahInput.value = dataURL;

            console.log('Foto wajah berhasil diambil');

            // Update status foto
            fotoWajahTerambil = true;
            btnAmbilFoto.style.display = 'none';
            btnUlangiFoto.style.display = 'block';

            // Matikan kamera
            if (streamWajah) {
                streamWajah.getTracks().forEach(track => track.stop());
                streamWajah = null;
            }
        } catch (error) {
            console.error('Error saat mengambil foto wajah:', error);
            alert('Terjadi kesalahan saat mengambil foto: ' + error.message);
        }
    }

    // Fungsi untuk mengambil foto surat tugas
    function ambilFotoSurat() {
        try {
            console.log('Mengambil foto surat');

            // Cek apakah elemen canvas dan video ada
            if (!canvasSurat || !videoSurat) {
                console.error('Elemen canvas atau video tidak ditemukan');
                alert('Terjadi kesalahan saat mengambil foto. Silakan coba lagi.');
                return;
            }

            // Cek apakah video sudah siap
            if (!videoSurat.videoWidth || !videoSurat.videoHeight) {
                console.error('Video belum siap, width/height: ', videoSurat.videoWidth, videoSurat.videoHeight);
                alert('Kamera belum siap. Silakan tunggu beberapa saat dan coba lagi.');
                return;
            }

            // Ambil konteks canvas
            const context = canvasSurat.getContext('2d');

            // Set ukuran canvas sesuai video
            canvasSurat.width = videoSurat.videoWidth;
            canvasSurat.height = videoSurat.videoHeight;

            // Gambar frame video ke canvas (tidak perlu flip untuk kamera belakang)
            context.drawImage(videoSurat, 0, 0, canvasSurat.width, canvasSurat.height);

            // Tampilkan canvas dan sembunyikan video
            videoSurat.style.display = 'none';
            canvasSurat.style.display = 'block';

            // Konversi canvas ke base64
            const dataURL = canvasSurat.toDataURL('image/jpeg', 0.8);
            fotoSuratTugasInput.value = dataURL;

            console.log('Foto surat berhasil diambil');

            // Update status foto
            fotoSuratTerambil = true;
            btnAmbilFotoSurat.style.display = 'none';
            btnUlangiFotoSurat.style.display = 'block';

            // Matikan kamera
            if (streamSurat) {
                streamSurat.getTracks().forEach(track => track.stop());
                streamSurat = null;
            }
        } catch (error) {
            console.error('Error saat mengambil foto surat:', error);
            alert('Terjadi kesalahan saat mengambil foto: ' + error.message);
        }
    }

    // Event listener untuk tombol ambil foto wajah
    btnAmbilFoto.addEventListener('click', ambilFotoWajah);

    // Event listener untuk tombol ulangi foto wajah
    btnUlangiFoto.addEventListener('click', startCameraWajah);

    // Event listener untuk tombol ambil foto surat
    btnAmbilFotoSurat.addEventListener('click', ambilFotoSurat);

    // Event listener untuk tombol ulangi foto surat
    btnUlangiFotoSurat.addEventListener('click', startCameraSurat);

    // Fungsi untuk mendeteksi Android WebView
    function isAndroidWebView() {
        const userAgent = navigator.userAgent.toLowerCase();
        return /android/.test(userAgent) && /wv/.test(userAgent);
    }

    // Fungsi untuk mendeteksi versi Android
    function getAndroidVersion() {
        const match = navigator.userAgent.toLowerCase().match(/android\s([0-9.]*)/);
        return match ? parseFloat(match[1]) : 0;
    }

    // Event listener untuk modal
    document.getElementById('ajukanIzinModal').addEventListener('shown.bs.modal', function() {
        console.log('Modal izin dinas dibuka');
        console.log('User Agent:', navigator.userAgent);

        const isAndroid = isAndroidWebView();
        const androidVersion = getAndroidVersion();

        console.log('Deteksi Android WebView:', isAndroid);
        console.log('Versi Android:', androidVersion);

        // Tambahkan informasi ke halaman untuk debugging
        if (isAndroid) {
            const infoElement = document.createElement('div');
            infoElement.style.display = 'none'; // Sembunyikan dari pengguna
            infoElement.id = 'android-info';
            infoElement.textContent = 'Android ' + androidVersion + ' WebView terdeteksi';
            document.body.appendChild(infoElement);
        }

        // Aktifkan kamera dengan delay
        setTimeout(function() {
            // Coba aktifkan kamera satu per satu untuk menghindari konflik
            startCameraWajah();

            // Tunggu sebentar sebelum mengaktifkan kamera kedua
            setTimeout(function() {
                startCameraSurat();
            }, 2000);
        }, 1000);

        // Tambahkan event listener untuk menangani error video
        const videoElements = [videoIzin, videoSurat];
        videoElements.forEach(function(video) {
            if (video) {
                video.addEventListener('error', function(e) {
                    console.error('Video error:', e);
                    // Tampilkan pesan error yang lebih informatif
                    const cameraPreview = video.closest('.camera-preview');
                    if (cameraPreview && cameraPreview.querySelector('.camera-message')) {
                        cameraPreview.querySelector('.camera-message').textContent = 'Error video: ' + (e.message || 'Tidak dapat memulai kamera');
                    }
                });
            }
        });
    });

    document.getElementById('ajukanIzinModal').addEventListener('hidden.bs.modal', function() {
        console.log('Modal izin dinas ditutup');

        // Matikan kedua kamera saat modal ditutup
        if (streamWajah) {
            streamWajah.getTracks().forEach(track => track.stop());
            streamWajah = null;
        }
        if (streamSurat) {
            streamSurat.getTracks().forEach(track => track.stop());
            streamSurat = null;
        }
    });



    // Fungsi untuk mengaktifkan kamera wajah hari ini - versi sederhana
    function startCameraWajahToday() {
        console.log('Memulai kamera wajah hari ini (versi sederhana)');

        // Perbarui pesan
        if (cameraPreviewWajahToday) {
            cameraPreviewWajahToday.querySelector('.camera-message').textContent = 'Meminta izin kamera...';
        }

        // Matikan kamera jika sudah aktif
        if (streamWajahToday) {
            streamWajahToday.getTracks().forEach(track => track.stop());
            streamWajahToday = null;
        }

        // Aktifkan kamera dengan pendekatan yang lebih sederhana
        navigator.mediaDevices.getUserMedia({
            video: true,
            audio: false
        }).then(function(stream) {
            console.log('Kamera wajah hari ini berhasil diaktifkan');
            streamWajahToday = stream;

            // Tampilkan video
            videoWajahToday.srcObject = stream;
            videoWajahToday.style.display = 'block';
            canvasWajahToday.style.display = 'none';

            if (cameraPreviewWajahToday.querySelector('i')) {
                cameraPreviewWajahToday.querySelector('i').style.display = 'none';
            }

            if (cameraPreviewWajahToday.querySelector('.camera-message')) {
                cameraPreviewWajahToday.querySelector('.camera-message').textContent = 'Kamera aktif. Klik tombol "Ambil Foto Wajah" untuk mengambil foto.';
            }

            // Reset status foto
            fotoWajahTodayTerambil = false;
            btnAmbilFotoWajahToday.style.display = 'block';
            btnUlangiFotoWajahToday.style.display = 'none';
            fotoWajahTodayInput.value = '';

            // Mulai video
            videoWajahToday.play().then(function() {
                console.log('Video wajah hari ini berhasil diputar');
            }).catch(function(error) {
                console.error('Error playing video:', error);
                videoWajahToday.muted = true;
                videoWajahToday.play();
            });

        }).catch(function(error) {
            console.error('Error accessing camera:', error);

            if (cameraPreviewWajahToday && cameraPreviewWajahToday.querySelector('.camera-message')) {
                cameraPreviewWajahToday.querySelector('.camera-message').textContent = 'Gagal mengakses kamera. Pastikan kamera aktif dan izin diberikan.';
            }

            // Tampilkan error yang lebih detail
            if (error.name === 'NotAllowedError') {
                alert('Akses kamera ditolak. Silakan berikan izin kamera di pengaturan browser Anda.');
            } else if (error.name === 'NotFoundError') {
                alert('Kamera tidak ditemukan. Pastikan perangkat Anda memiliki kamera yang berfungsi.');
            } else {
                alert('Gagal mengakses kamera: ' + error.message);
            }
        });
    }

    // Fungsi untuk mengaktifkan kamera surat tugas hari ini - versi sederhana
    function startCameraSuratToday() {
        console.log('Memulai kamera surat hari ini (versi sederhana)');

        // Perbarui pesan
        if (cameraPreviewSuratToday) {
            cameraPreviewSuratToday.querySelector('.camera-message').textContent = 'Meminta izin kamera...';
        }

        // Matikan kamera jika sudah aktif
        if (streamSuratToday) {
            streamSuratToday.getTracks().forEach(track => track.stop());
            streamSuratToday = null;
        }

        // Aktifkan kamera dengan pendekatan yang lebih sederhana
        navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment' // Gunakan kamera belakang untuk foto dokumen jika tersedia
            },
            audio: false
        }).then(function(stream) {
            console.log('Kamera surat hari ini berhasil diaktifkan');
            streamSuratToday = stream;

            // Tampilkan video
            videoSuratToday.srcObject = stream;
            videoSuratToday.style.display = 'block';
            canvasSuratToday.style.display = 'none';

            if (cameraPreviewSuratToday.querySelector('i')) {
                cameraPreviewSuratToday.querySelector('i').style.display = 'none';
            }

            if (cameraPreviewSuratToday.querySelector('.camera-message')) {
                cameraPreviewSuratToday.querySelector('.camera-message').textContent = 'Kamera aktif. Klik tombol "Ambil Foto Surat" untuk mengambil foto.';
            }

            // Reset status foto
            fotoSuratTodayTerambil = false;
            btnAmbilFotoSuratToday.style.display = 'block';
            btnUlangiFotoSuratToday.style.display = 'none';
            fotoSuratTugasTodayInput.value = '';

            // Mulai video
            videoSuratToday.play().then(function() {
                console.log('Video surat hari ini berhasil diputar');
            }).catch(function(error) {
                console.error('Error playing video:', error);
                videoSuratToday.muted = true;
                videoSuratToday.play();
            });

        }).catch(function(error) {
            console.error('Error accessing camera:', error);

            if (cameraPreviewSuratToday && cameraPreviewSuratToday.querySelector('.camera-message')) {
                cameraPreviewSuratToday.querySelector('.camera-message').textContent = 'Gagal mengakses kamera. Pastikan kamera aktif dan izin diberikan.';
            }

            // Tampilkan error yang lebih detail
            if (error.name === 'NotAllowedError') {
                alert('Akses kamera ditolak. Silakan berikan izin kamera di pengaturan browser Anda.');
            } else if (error.name === 'NotFoundError') {
                alert('Kamera tidak ditemukan. Pastikan perangkat Anda memiliki kamera yang berfungsi.');
            } else {
                alert('Gagal mengakses kamera: ' + error.message);
            }
        });
    }

    // Fungsi untuk mengambil foto wajah hari ini
    function ambilFotoWajahToday() {
        try {
            console.log('Mengambil foto wajah hari ini');

            // Cek apakah elemen canvas dan video ada
            if (!canvasWajahToday || !videoWajahToday) {
                console.error('Elemen canvas atau video tidak ditemukan');
                alert('Terjadi kesalahan saat mengambil foto. Silakan coba lagi.');
                return;
            }

            // Cek apakah video sudah siap
            if (!videoWajahToday.videoWidth || !videoWajahToday.videoHeight) {
                console.error('Video belum siap, width/height: ', videoWajahToday.videoWidth, videoWajahToday.videoHeight);
                alert('Kamera belum siap. Silakan tunggu beberapa saat dan coba lagi.');
                return;
            }

            // Ambil konteks canvas
            const context = canvasWajahToday.getContext('2d');

            // Set ukuran canvas sesuai video
            canvasWajahToday.width = videoWajahToday.videoWidth;
            canvasWajahToday.height = videoWajahToday.videoHeight;

            // Gambar frame video ke canvas
            context.drawImage(videoWajahToday, 0, 0, canvasWajahToday.width, canvasWajahToday.height);

            // Tampilkan canvas dan sembunyikan video
            videoWajahToday.style.display = 'none';
            canvasWajahToday.style.display = 'block';

            // Konversi canvas ke base64
            const dataURL = canvasWajahToday.toDataURL('image/jpeg');
            fotoWajahTodayInput.value = dataURL;

            console.log('Foto wajah hari ini berhasil diambil');

            // Update status foto
            fotoWajahTodayTerambil = true;
            btnAmbilFotoWajahToday.style.display = 'none';
            btnUlangiFotoWajahToday.style.display = 'block';

            // Matikan kamera
            if (streamWajahToday) {
                streamWajahToday.getTracks().forEach(track => track.stop());
                streamWajahToday = null;
            }
        } catch (error) {
            console.error('Error saat mengambil foto wajah:', error);
            alert('Terjadi kesalahan saat mengambil foto: ' + error.message);
        }
    }

    // Fungsi untuk mengambil foto surat tugas hari ini
    function ambilFotoSuratToday() {
        try {
            console.log('Mengambil foto surat hari ini');

            // Cek apakah elemen canvas dan video ada
            if (!canvasSuratToday || !videoSuratToday) {
                console.error('Elemen canvas atau video tidak ditemukan');
                alert('Terjadi kesalahan saat mengambil foto. Silakan coba lagi.');
                return;
            }

            // Cek apakah video sudah siap
            if (!videoSuratToday.videoWidth || !videoSuratToday.videoHeight) {
                console.error('Video belum siap, width/height: ', videoSuratToday.videoWidth, videoSuratToday.videoHeight);
                alert('Kamera belum siap. Silakan tunggu beberapa saat dan coba lagi.');
                return;
            }

            // Ambil konteks canvas
            const context = canvasSuratToday.getContext('2d');

            // Set ukuran canvas sesuai video
            canvasSuratToday.width = videoSuratToday.videoWidth;
            canvasSuratToday.height = videoSuratToday.videoHeight;

            // Gambar frame video ke canvas
            context.drawImage(videoSuratToday, 0, 0, canvasSuratToday.width, canvasSuratToday.height);

            // Tampilkan canvas dan sembunyikan video
            videoSuratToday.style.display = 'none';
            canvasSuratToday.style.display = 'block';

            // Konversi canvas ke base64
            const dataURL = canvasSuratToday.toDataURL('image/jpeg');
            fotoSuratTugasTodayInput.value = dataURL;

            console.log('Foto surat hari ini berhasil diambil');

            // Update status foto
            fotoSuratTodayTerambil = true;
            btnAmbilFotoSuratToday.style.display = 'none';
            btnUlangiFotoSuratToday.style.display = 'block';

            // Matikan kamera
            if (streamSuratToday) {
                streamSuratToday.getTracks().forEach(track => track.stop());
                streamSuratToday = null;
            }
        } catch (error) {
            console.error('Error saat mengambil foto surat:', error);
            alert('Terjadi kesalahan saat mengambil foto: ' + error.message);
        }
    }

    // Tidak perlu tombol aktivasi kamera lagi karena kamera akan diaktifkan otomatis saat modal dibuka

    // Event listener untuk tombol ambil foto wajah hari ini
    if (btnAmbilFotoWajahToday) {
        btnAmbilFotoWajahToday.addEventListener('click', ambilFotoWajahToday);
    }

    // Event listener untuk tombol ulangi foto wajah hari ini
    if (btnUlangiFotoWajahToday) {
        btnUlangiFotoWajahToday.addEventListener('click', startCameraWajahToday);
    }

    // Event listener untuk tombol ambil foto surat hari ini
    if (btnAmbilFotoSuratToday) {
        btnAmbilFotoSuratToday.addEventListener('click', ambilFotoSuratToday);
    }

    // Event listener untuk tombol ulangi foto surat hari ini
    if (btnUlangiFotoSuratToday) {
        btnUlangiFotoSuratToday.addEventListener('click', startCameraSuratToday);
    }

    // Event listener untuk modal perjalanan dinas hari ini
    const ajukanDinasHariIniModal = document.getElementById('ajukanDinasHariIniModal');
    if (ajukanDinasHariIniModal) {
        console.log('Modal perjalanan dinas hari ini ditemukan');

        // Event listener untuk tombol aktivasi kamera manual
        const btnAktifkanKameraManual = document.getElementById('btn-aktifkan-kamera-manual');
        if (btnAktifkanKameraManual) {
            btnAktifkanKameraManual.addEventListener('click', function() {
                alert('Mencoba mengaktifkan kamera secara manual...');

                // Cek apakah navigator.mediaDevices tersedia
                if (!navigator.mediaDevices) {
                    alert('navigator.mediaDevices tidak tersedia di browser ini');
                    return;
                }

                // Cek apakah getUserMedia tersedia
                if (!navigator.mediaDevices.getUserMedia) {
                    alert('navigator.mediaDevices.getUserMedia tidak tersedia di browser ini');
                    return;
                }

                // Aktifkan kedua kamera
                try {
                    console.log('Mencoba mengaktifkan kamera wajah...');
                    startCameraWajahToday();
                    console.log('Mencoba mengaktifkan kamera surat...');
                    startCameraSuratToday();

                    alert('Permintaan aktivasi kamera telah dikirim. Silakan berikan izin jika diminta oleh browser.');
                } catch (error) {
                    console.error('Error saat mengaktifkan kamera:', error);
                    alert('Error saat mengaktifkan kamera: ' + error.message);
                }
            });
        }

        ajukanDinasHariIniModal.addEventListener('shown.bs.modal', function() {
            console.log('Modal perjalanan dinas hari ini dibuka');

            // Reset status foto
            fotoWajahTodayTerambil = false;
            fotoSuratTodayTerambil = false;

            // Aktifkan kedua kamera saat modal dibuka
            setTimeout(function() {
                try {
                    console.log('Mencoba mengaktifkan kamera wajah...');
                    startCameraWajahToday();
                    console.log('Mencoba mengaktifkan kamera surat...');
                    startCameraSuratToday();
                } catch (error) {
                    console.error('Error saat mengaktifkan kamera:', error);
                    console.log('Silakan gunakan tombol "Aktifkan Kamera Secara Manual"');
                }
            }, 1000);
        });

        ajukanDinasHariIniModal.addEventListener('hidden.bs.modal', function() {
            console.log('Modal perjalanan dinas hari ini ditutup');

            // Matikan kedua kamera saat modal ditutup
            if (streamWajahToday) {
                streamWajahToday.getTracks().forEach(track => track.stop());
                streamWajahToday = null;
            }
            if (streamSuratToday) {
                streamSuratToday.getTracks().forEach(track => track.stop());
                streamSuratToday = null;
            }
        });
    } else {
        console.error('Modal perjalanan dinas hari ini tidak ditemukan');
    }

    // Validasi form perjalanan dinas hari ini sebelum submit
    const formDinasHariIni = document.getElementById('formDinasHariIni');
    if (formDinasHariIni) {
        formDinasHariIni.addEventListener('submit', function(e) {
            if (!fotoWajahTodayTerambil) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'Foto Wajah Diperlukan',
                    text: 'Anda harus mengambil foto wajah terlebih dahulu!',
                    confirmButtonColor: '#f6c23e',
                    confirmButtonText: 'Saya Mengerti'
                });
                return false;
            }

            if (!fotoSuratTodayTerambil) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'Foto Surat Tugas Diperlukan',
                    text: 'Anda harus mengambil foto surat tugas terlebih dahulu!',
                    confirmButtonColor: '#f6c23e',
                    confirmButtonText: 'Saya Mengerti'
                });
                return false;
            }
        });
    }

    // Validasi form sebelum submit
    formIzinDinas.addEventListener('submit', function(e) {
        if (!fotoWajahTerambil) {
            e.preventDefault();
            Swal.fire({
                icon: 'warning',
                title: 'Foto Wajah Diperlukan',
                text: 'Anda harus mengambil foto wajah terlebih dahulu!',
                confirmButtonColor: '#f6c23e',
                confirmButtonText: 'Saya Mengerti'
            });
            return false;
        }

        if (!fotoSuratTerambil) {
            e.preventDefault();
            Swal.fire({
                icon: 'warning',
                title: 'Foto Surat Tugas Diperlukan',
                text: 'Anda harus mengambil foto surat tugas terlebih dahulu!',
                confirmButtonColor: '#f6c23e',
                confirmButtonText: 'Saya Mengerti'
            });
            return false;
        }
    });

    // Event listener untuk melihat gambar
    document.querySelectorAll('.view-image').forEach(function(element) {
        element.addEventListener('click', function() {
            const src = this.getAttribute('data-src');
            const title = this.getAttribute('data-title');
            document.getElementById('imageModalLabel').textContent = title;
            document.getElementById('modalImage').src = src;
        });
    });

    // Fungsi untuk konfirmasi pembatalan izin dinas
    function konfirmasiBatalkan(izinId) {
        Swal.fire({
            title: 'Konfirmasi Pembatalan',
            text: 'Apakah Anda yakin ingin membatalkan pengajuan izin dinas ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#e74a3b',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Batalkan!',
            cancelButtonText: 'Tidak, Kembali'
        }).then((result) => {
            if (result.isConfirmed) {
                document.getElementById('form-batalkan-' + izinId).submit();
            }
        });
    }
</script>

<script src="js/izin_dinas.js"></script>

<!-- Modal untuk menampilkan gambar -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Lihat Gambar</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Gambar" class="img-fluid">
            </div>
        </div>
    </div>
</div>




<?php
// Include footer
include_once '../includes/footer.php';
?>
