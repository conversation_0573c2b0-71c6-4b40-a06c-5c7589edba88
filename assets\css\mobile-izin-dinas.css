/* Mobile Izin Dinas Style */

/* Base Styles */
.mobile-izin-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

@media (min-width: 768px) {
    .mobile-izin-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-izin-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-izin-container {
        max-width: 840px;
    }
}

/* Header Styles */
.izin-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.izin-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.izin-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
}

.izin-header .user-details {
    flex: 1;
}

.izin-header .user-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.izin-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.izin-header .date-info {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 15px;
    font-size: 14px;
}

.izin-header .date-info i {
    margin-right: 5px;
}

/* Action Button */
.action-fab {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 100;
    font-size: 24px;
    transition: all 0.3s ease;
}

.action-fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

/* Izin Card */
.izin-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 15px;
}

.izin-card .izin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    background: none;
    box-shadow: none;
    padding: 0;
    margin: 0 0 10px 0;
}

.izin-card .izin-title {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
}

.izin-card .izin-status {
    font-size: 12px;
}

.izin-card .izin-date {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
    color: #6c757d;
}

.izin-card .izin-details {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
}

.izin-card .izin-detail-item {
    margin-bottom: 8px;
}

.izin-card .izin-detail-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 2px;
}

.izin-card .izin-detail-value {
    font-size: 14px;
    color: #343a40;
}

/* Info Section */
.info-section {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 15px;
}

.info-section .info-title {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
    margin-bottom: 10px;
}

.info-section .info-content {
    font-size: 14px;
    color: #6c757d;
}

.info-section .info-content ul {
    padding-left: 20px;
    margin-bottom: 10px;
}

.info-section .info-content li {
    margin-bottom: 5px;
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
}

.modal-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

.modal-footer {
    border-top: none;
}

/* Badge Styles */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.success {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .izin-header {
        padding: 15px;
    }

    .action-fab {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

@media (min-width: 768px) {
    .action-fab {
        right: calc(50% - 300px + 20px);
    }
}

@media (min-width: 992px) {
    .action-fab {
        right: calc(50% - 360px + 20px);
    }
}

@media (min-width: 1200px) {
    .action-fab {
        right: calc(50% - 420px + 20px);
    }
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.action-button {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 12px 8px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-button .action-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    color: white;
    font-size: 18px;
}

.action-button .action-icon.primary {
    background-color: #4e73df;
}

.action-button .action-icon.success {
    background-color: #1cc88a;
}

.action-button .action-icon.warning {
    background-color: #f6c23e;
}

.action-button .action-icon.info {
    background-color: #36b9cc;
}

.action-button .action-icon.danger {
    background-color: #e74a3b;
}

.action-button .action-text {
    font-size: 14px;
    color: #343a40;
    font-weight: 500;
}

@media (min-width: 768px) {
    .quick-actions {
        grid-template-columns: repeat(4, 1fr);
    }
}
