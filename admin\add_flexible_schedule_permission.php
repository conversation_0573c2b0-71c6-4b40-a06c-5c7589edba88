<?php
// Include file konfigurasi
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek apakah kolom allow_flexible_schedule sudah ada
$query = "SHOW COLUMNS FROM users LIKE 'allow_flexible_schedule'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) > 0) {
    setMessage('info', 'Kolom allow_flexible_schedule sudah ada di tabel users!');
    redirect('admin/karyawan.php');
}

// Tambahkan kolom allow_flexible_schedule
$query = "ALTER TABLE users ADD COLUMN allow_flexible_schedule TINYINT(1) DEFAULT 0 AFTER allow_face";

if (mysqli_query($conn, $query)) {
    setMessage('success', 'Kolom allow_flexible_schedule berhasil ditambahkan ke tabel users!');
} else {
    setMessage('danger', 'Gagal menambahkan kolom allow_flexible_schedule: ' . mysqli_error($conn));
}

// Buat tabel user_schedule_choices jika belum ada
$query = "CREATE TABLE IF NOT EXISTS user_schedule_choices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    tanggal DATE NOT NULL,
    jam_kerja_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (jam_kerja_id) REFERENCES jam_kerja(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date (user_id, tanggal)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if (mysqli_query($conn, $query)) {
    setMessage('success', 'Tabel user_schedule_choices berhasil dibuat!');
} else {
    setMessage('danger', 'Gagal membuat tabel user_schedule_choices: ' . mysqli_error($conn));
}

redirect('admin/karyawan.php');
?>
