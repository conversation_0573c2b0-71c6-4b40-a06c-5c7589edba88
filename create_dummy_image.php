<?php
// Buat gambar dummy untuk absensi manual
$width = 300;
$height = 300;
$image = imagecreatetruecolor($width, $height);

// <PERSON>na latar belakang (biru muda)
$bg_color = imagecolorallocate($image, 100, 150, 255);
imagefill($image, 0, 0, $bg_color);

// Warna teks (putih)
$text_color = imagecolorallocate($image, 255, 255, 255);

// Tambahkan teks
$text = "Absensi Manual";
$font_size = 5;
$text_width = imagefontwidth($font_size) * strlen($text);
$text_height = imagefontheight($font_size);
$x = ($width - $text_width) / 2;
$y = ($height - $text_height) / 2;
imagestring($image, $font_size, $x, $y, $text, $text_color);

// Tambahkan teks tambahan
$text2 = "Dibuat oleh Admin";
$x2 = ($width - imagefontwidth($font_size) * strlen($text2)) / 2;
$y2 = $y + $text_height + 10;
imagestring($image, $font_size, $x2, $y2, $text2, $text_color);

// Pastikan direktori uploads ada
if (!file_exists('uploads')) {
    mkdir('uploads', 0777, true);
}

// Simpan gambar
imagejpeg($image, 'uploads/manual_absensi.jpg');
imagedestroy($image);

echo "Gambar dummy berhasil dibuat di uploads/manual_absensi.jpg";
?>
