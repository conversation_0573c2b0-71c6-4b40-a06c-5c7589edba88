/**
 * @fileoverview
 * HTML5 QR code scanning library.
 * - Decode QR Code using web cam or smartphone camera
 * 
 * <AUTHOR> <<EMAIL>>
 * 
 * The word "QR Code" is registered trademark of DENSO WAVE INCORPORATED
 * http://www.denso-wave.com/qrcode/faqpatent-e.html
 * 
 * Note: ECMA Script is not supported by all browsers. Use minified/html5-qrcode.min.js for better
 * browser support. Alternatively the transpiled code lives in transpiled/html5-qrcode.js
 */
class Html5Qrcode{constructor(e,t){if(this._elementId=e,this._lastScanImageFile=null,this._shouldScan=!0,this._isScanning=!1,!document.getElementById(e))throw"HTML Element with id="+e+" not found";var r;this._qrCodeSuccessCallback=null,this._qrCodeErrorCallback=null,this._videoElement=null,this._localMediaStream=null,this._canvasElement=null,this._context=null,this._videoConstraints=null,this._isScanning=!1,this._possibleFormats=null,this._selectFormats=null,this._selectedCameraId=null,t&&(r=t.formatsToSupport)&&(this._possibleFormats=r),this._verbose=!0===t?.verbose:!1}start(e,t,r,n){if(!e)throw"cameraIdOrConfig is required";if(!r||"function"!=typeof r)throw"qrCodeSuccessCallback is required and should be a function.";n||(n=console.log),this._qrCodeSuccessCallback=r,this._qrCodeErrorCallback=n;var o=this,s=function(t){var r=o._decodeFromCamera(t,e);o._isScanning=!0,r.then((function(){o._isScanning=!1})).catch((function(e){o._isScanning=!1}))};return navigator.mediaDevices&&navigator.mediaDevices.getUserMedia?this._setupUserMedia(e,s):Promise.reject("Camera streaming not supported by the browser.")}stop(){this._shouldScan=!1,this._localMediaStream&&(this._localMediaStream.getTracks().forEach((function(e){e.stop()})),this._localMediaStream=null),this._videoElement&&(this._videoElement.pause(),this._videoElement.srcObject=null,this._videoElement.parentElement&&this._videoElement.parentElement.removeChild(this._videoElement),this._videoElement=null),this._canvasElement&&(this._canvasElement.parentElement&&this._canvasElement.parentElement.removeChild(this._canvasElement),this._canvasElement=null),this._context=null,this._isScanning=!1,this._qrCodeSuccessCallback=null,this._qrCodeErrorCallback=null}scanFile(e,t){if(!e||!(e instanceof File))throw"imageFile argument is mandatory and should be instance of File. Use 'event.target.files[0]'.";if(t||(t=!0),!this._isScanning){this._isScanning=!0;var r=this,n=URL.createObjectURL(e),o=new Image;o.onload=function(){var s=o.height,a=o.width,i=document.createElement("canvas");i.width=a,i.height=s;var l=i.getContext("2d");l.drawImage(o,0,0,i.width,i.height),r._lastScanImageFile=e;var c=l.getImageData(0,0,a,s);r._possibleFormats&&(r._selectFormats=r._possibleFormats);var h=jsQR(c.data,c.width,c.height,{inversionAttempts:"dontInvert"});if(h){var u=h.data;r._isScanning=!1,r._qrCodeSuccessCallback(u,{format:r._selectFormats&&1===r._selectFormats.length?r._selectFormats[0]:"code_128",rawBytes:h.binaryData})}else{if(!t){r._isScanning=!1;var d="No QR code found.";return r._qrCodeErrorCallback(d,{format:r._selectFormats&&1===r._selectFormats.length?r._selectFormats[0]:"code_128"}),}setTimeout((function(){r._isScanning=!1,r.scanFile(e,t)}),Constants.SCAN_INTERVAL_DEFAULT)}},o.onerror=function(){r._isScanning=!1;var t="Unable to load the image";r._qrCodeErrorCallback(t,{format:r._selectFormats&&1===r._selectFormats.length?r._selectFormats[0]:"code_128"})},o.src=n}}clear(){this._context&&this._context.clearRect(0,0,this._canvasElement.width,this._canvasElement.height)}static getCameras(){return new Promise((function(e,t){if(navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices)navigator.mediaDevices.enumerateDevices().then((function(r){for(var n=[],o=0;o<r.length;o++){var s=r[o];s.kind&&"videoinput"==s.kind&&n.push({id:s.deviceId,label:s.label})}e(n)})).catch((function(e){t("Error in enumerating devices: "+e)}));else{if(!MediaStreamTrack||!MediaStreamTrack.getSources)return t("browser doesn't have MediaStreamTrack.getSources");MediaStreamTrack.getSources((function(t){for(var r=[],n=0;n<t.length;n++){var o=t[n];"video"==o.kind&&r.push({id:o.id,label:o.label})}e(r)}))}}))}getRunningTrackCapabilities(){if(this._localMediaStream)if(0!=this._localMediaStream.getVideoTracks().length){var e=this._localMediaStream.getVideoTracks()[0];return e.getCapabilities?e.getCapabilities():this._logError("videoTrack.getCapabilities() is not defined")}else this._logError("No video tracks found");else this._logError("Scanning is not in running state, unable to get capabilities")}getRunningTrackSettings(){if(this._localMediaStream)if(0!=this._localMediaStream.getVideoTracks().length){var e=this._localMediaStream.getVideoTracks()[0];return e.getSettings?e.getSettings():this._logError("videoTrack.getSettings() is not defined")}else this._logError("No video tracks found");else this._logError("Scanning is not in running state, unable to get settings")}applyVideoConstraints(e){if(!e)throw"videoConstaints is required argument.";if(!this._isScanning)throw"Scanning is not in running state, unable to apply video constraints";if(0===this._localMediaStream.getVideoTracks().length)throw"No video tracks found";return new Promise((function(t,r){try{this._localMediaStream.getVideoTracks()[0].applyConstraints(e).then((function(){t()})).catch((function(e){r(e)}))}catch(e){r("Error in applyConstraints: "+e)}}))}_setupUserMedia(e,t){return this._isScanning=!0,this._selectedCameraId=e,new Promise((function(r,n){var o=this,s=function(e){o._isScanning=!1,n(e)},a=document.getElementById(o._elementId);a.style.position="relative",o._shouldScan=!0,o._videoElement=document.createElement("video"),o._videoElement.style.width="100%",o._videoElement.style.height="100%",o._videoElement.setAttribute("autoplay",""),o._videoElement.setAttribute("playsinline",""),o._canvasElement=document.createElement("canvas"),o._canvasElement.style.width="100%",o._canvasElement.style.height="100%",o._canvasElement.style.display="none",o._canvasElement.style.position="absolute",a.appendChild(o._canvasElement),a.appendChild(o._videoElement),o._context=o._canvasElement.getContext("2d");var i=function(e){e&&e.video&&(e.video.facingMode="environment"),e||(e={});var t=o._videoConstraintsFromCameraIdOrConfig(e),r={audio:!1,video:t};o._verbose&&console.log("Requesting getUserMedia with config",r),navigator.mediaDevices.getUserMedia(r).then((function(e){o._localMediaStream=e,o._videoElement.srcObject=e,o._videoElement.onloadedmetadata=function(e){var t=o._videoElement.clientWidth,n=o._videoElement.clientHeight;o._videoElement.play(),o._canvasElement.width=t,o._canvasElement.height=n,r()},"function"==typeof t&&t(e)})).catch((function(e){s("Error getting userMedia, error = "+e)}))};if("string"==typeof e)i({video:{deviceId:{exact:e}}});else{if(!e||"object"!=typeof e)throw"Invalid camera id or config passed to start";i(e)}}))}._videoConstraintsFromCameraIdOrConfig(e){if("string"==typeof e)return{deviceId:{exact:e}};if("object"==typeof e){var t={};return Object.keys(e).forEach((function(r){t[r]=e[r]})),t}}_decodeFromCamera(e,t){var r=this;return new Promise((function(n,o){r._possibleFormats&&(r._selectFormats=r._possibleFormats);var s=function(){if(r._shouldScan){requestAnimationFrame(s);try{var t=r._canvasElement.width,a=r._canvasElement.height;r._context.drawImage(r._videoElement,0,0,t,a);var i=r._context.getImageData(0,0,t,a).data;r._selectFormats&&1===r._selectFormats.length&&"code_39"===r._selectFormats[0]?r._decodeCode39(i,t,a):r._decodeWithLibraryAndSelectFormats(i,t,a,r._selectFormats)}catch(e){r._verbose&&console.log("Error when scanning",e),o(e)}}else n()};e&&s()}))}._isSelectFormatsValid(e){if(!e||0===e.length)return!1;var t=[];return e.forEach((function(e){t.push(e.toLowerCase())})),!(t.length>1&&t.includes("code_39"))}_decodeWithLibraryAndSelectFormats(e,t,r,n){var o=this,s=jsQR(e,t,r,{inversionAttempts:"dontInvert"});if(s&&s.data){var a=s.data;o._qrCodeSuccessCallback(a,{format:n&&1===n.length?n[0]:"code_128",rawBytes:s.binaryData})}}_decodeCode39(e,t,r){try{var n=new ZXing.HTMLCanvasElementLuminanceSource(this._canvasElement),o=new ZXing.BinaryBitmap(new ZXing.HybridBinarizer(n)),s=new ZXing.MultiFormatReader;s.setHints(new Map([[ZXing.DecodeHintType.POSSIBLE_FORMATS,[ZXing.BarcodeFormat.CODE_39]]]))}}catch(e){this._verbose&&console.log("Failed to decode barcode, error = "+e),this._qrCodeErrorCallback("Failed to decode barcode, error = "+e,{format:"code_39"})}}_logError(e){this._verbose&&console.error(e)}}class Html5QrcodeScanner{constructor(e,t,r){this.elementId=e,this.config=t,this.verbose=!0===r,this.currentScanType=Html5QrcodeScanner.SCAN_TYPE_CAMERA,this.sectionSwapAllowed=!0,this.section=void 0,this.html5Qrcode=void 0,this.qrCodeSuccessCallback=void 0,this.qrCodeErrorCallback=void 0}render(e,t){var r=this;this.lastMatchFound=void 0,this.qrCodeSuccessCallback=function(t,n){if(e&&"function"==typeof e)r.lastMatchFound=t,e(t,n)},this.qrCodeErrorCallback=function(e,r){t&&"function"==typeof t&&t(e,r)};var n=document.getElementById(this.elementId);if(!n)throw"HTML Element with id="+this.elementId+" not found";n.innerHTML="",this.createBasicLayout(n),this.html5Qrcode=new Html5Qrcode(this.getScanRegionId(),{formatsToSupport:this.config?this.config.formatsToSupport:null,verbose:this.verbose})}clear(){var e=this,t=this.getHtml5QrcodeOrFail();return new Promise((function(r,n){t._isScanning&&t.stop().then((function(){e.html5Qrcode=void 0,r()})).catch((function(e){n("Unable to tear down, error ="+e)}))}))}getLastMatchFound(){return this.lastMatchFound}static get SCAN_TYPE_CAMERA(){return 1}static get SCAN_TYPE_FILE(){return 2}createBasicLayout(e){e.style.position="relative",e.style.padding="0px",e.style.border="1px solid silver",this.createHeader(e);var t=document.createElement("div"),r=this.getScanRegionId();t.id=r,t.style.width="100%",t.style.minHeight="100px",t.style.textAlign="center",e.appendChild(t),this.insertCameraScanImageToScanRegion();var n=document.createElement("div"),o=this.getDashboardId();n.id=o,n.style.width="100%",e.appendChild(n),this.setupInitialDashboard(n)}createHeader(e){var t=document.createElement("div");t.style.textAlign="left",t.style.margin="0px",e.appendChild(t);var r=document.createElement("div");r.id=this.getHeaderMessageContainerId(),r.style.display="none",r.style.textAlign="center",r.style.fontSize="14px",r.style.padding="2px 10px",r.style.margin="4px",r.style.borderTop="1px solid #f6f6f6",t.appendChild(r)}createSection(e){var t=document.createElement("div");t.id=this.getDashboardSectionId(),t.style.width="100%",t.style.padding="10px",t.style.textAlign="left",e.appendChild(t)}getHeaderMessageContainerId(){return this.elementId+"__header-message"}getDashboardId(){return this.elementId+"__dashboard"}getDashboardSectionId(){return this.elementId+"__dashboard-section"}getScanRegionId(){return this.elementId+"__scan-region"}getScanTypeRadioId(e){return this.elementId+"__scan-type-"+e}getCameraSelectionId(){return this.elementId+"__camera-selection"}getCameraPermissionButtonId(){return this.elementId+"__camera-permission-button"}getCameraScanRegion(){return document.getElementById(this.getScanRegionId())}getDashboardSectionCameraScanRegion(){return document.getElementById(this.getDashboardSectionId())}setupInitialDashboard(e){this.createSection(e);var t=document.createElement("div");t.id=this.getDashboardSectionCameraInitialisedId(),t.style.display="none",document.getElementById(this.getDashboardSectionId()).appendChild(t);var r=document.createElement("div");r.style.textAlign="center";var n=document.createElement("button");n.id=this.getCameraPermissionButtonId(),n.innerText="Request Camera Permissions",n.addEventListener("click",(function(){n.disabled=!0,n.innerText="Requesting camera permissions...";var e=this;Html5Qrcode.getCameras().then((function(t){e.showHideScanTypeSwapLink(!0),e.resetHeaderMessage(),e.setupCameraSelection(t)})).catch((function(t){n.innerText="No Cameras Found",e.setHeaderMessage("No cameras found",!0)}))})),r.appendChild(n),document.getElementById(this.getDashboardSectionId()).appendChild(r),this.insertCameraScanImageToScanRegion()}startCameraScanType(){var e=this,t=this.getHtml5QrcodeOrFail();t._isScanning||this.html5Qrcode.start(this.config.cameraIdOrConfig,{fps:this.config.fps,qrbox:this.config.qrbox,aspectRatio:this.config.aspectRatio,disableFlip:this.config.disableFlip},(function(t,r){e.qrCodeSuccessCallback(t,r)}),this.qrCodeErrorCallback).catch((function(t){e.setHeaderMessage(t,!0)}))}resetHeaderMessage(){document.getElementById(this.getHeaderMessageContainerId()).style.display="none"}setHeaderMessage(e,t){t||(t=!1);var r=document.getElementById(this.getHeaderMessageContainerId());r.innerText=e,r.style.color=t?"#F44336":"rgb(17, 17, 17)",r.style.display="block"}setupCameraSelection(e){var t=this;if(t.showHideScanTypeSwapLink(!0),t.resetHeaderMessage(),0==e.length)throw"No cameras found";var r=document.getElementById(t.getDashboardSectionId());r.style.textAlign="center";var n=document.createElement("span");n.style.marginRight="10px";var o=document.createElement("select");o.id=t.getCameraSelectionId();for(var s=1,a=null,i=0,l=e.length;i<l;i++){var c=e[i],h=c.id,u=null==c.label?h:c.label;(null!=a&&h!=a||(a=h,!0))&&(1===s&&(s=i),o.options[i]=new Option(u,h))}o.style.marginRight="10px",o.selectedIndex=s,n.appendChild(o),r.appendChild(n);var d=document.createElement("span"),f=document.createElement("button");f.innerText="Start Scanning",d.appendChild(f);var m=document.getElementById(t.getCameraPermissionButtonId());m.disabled=!0,m.hidden=!0,r.appendChild(d),f.addEventListener("click",(function(){o.disabled=!0,f.disabled=!0,t.currentScanType=Html5QrcodeScanner.SCAN_TYPE_CAMERA;var e=o.value;t.config.cameraIdOrConfig=e,t.setupInitialDashboard(document.getElementById(t.getDashboardId())),t.showHideScanTypeSwapLink(!1),t.insertCameraScanImageToScanRegion(),t.startCameraScanType(),t.getHtml5QrcodeOrFail()}))}insertCameraScanImageToScanRegion(){var e=document.getElementById(this.getScanRegionId());e.innerHTML="";var t=document.createElement("img");t.id=this.getCameraScanImageId(),t.alt="Scan your QR Code",t.src="data:image/svg+xml;base64,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",t.style.opacity="0.8",t.style.width="64px",t.style.display="block",t.style.margin="auto",e.appendChild(t)}getDashboardSectionCameraInitialisedId(){return this.elementId+"__dashboard-section-swaplink"}getHtml5QrcodeOrFail(){if(!this.html5Qrcode)throw"Code scanner not initialized.";return this.html5Qrcode}showHideScanTypeSwapLink(e){e||(e=!1);var t=this.getDashboardSectionCameraInitialisedId();document.getElementById(t).style.display=e?"block":"none"}getCameraScanImageId(){return this.elementId+"__camera-scan-image"}}
