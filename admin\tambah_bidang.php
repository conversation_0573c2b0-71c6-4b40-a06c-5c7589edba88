<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Proses tambah bidang
if (isset($_POST['tambah'])) {
    $nama_bidang = clean($_POST['nama_bidang']);
    $keterangan = clean($_POST['keterangan']);
    
    // Validasi input
    if (empty($nama_bidang)) {
        setMessage('danger', 'Nama bidang tidak boleh kosong');
        redirect('tambah_bidang.php');
    }
    
    // Cek apakah nama bidang sudah ada
    $query = "SELECT * FROM bidang WHERE nama_bidang = '$nama_bidang'";
    $result = mysqli_query($conn, $query);
    
    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Nama bidang sudah ada');
        redirect('tambah_bidang.php');
    }
    
    // Tambah bidang
    $query = "INSERT INTO bidang (nama_bidang, keterangan) VALUES ('$nama_bidang', '$keterangan')";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Bidang berhasil ditambahkan');
        redirect('bidang.php');
    } else {
        setMessage('danger', 'Gagal menambahkan bidang: ' . mysqli_error($conn));
        redirect('tambah_bidang.php');
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Tambah Bidang</h1>
        <a href="bidang.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Form Tambah Bidang</h6>
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="mb-3">
                    <label for="nama_bidang" class="form-label">Nama Bidang</label>
                    <input type="text" class="form-control" id="nama_bidang" name="nama_bidang" required>
                </div>

                <div class="mb-3">
                    <label for="keterangan" class="form-label">Keterangan</label>
                    <textarea class="form-control" id="keterangan" name="keterangan" rows="3"></textarea>
                </div>

                <div class="mt-3">
                    <button type="submit" name="tambah" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                    <a href="bidang.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
