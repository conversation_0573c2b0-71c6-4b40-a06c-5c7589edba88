<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil ID presensi
$id = isset($_GET['id']) ? clean($_GET['id']) : 0;

// Ambil data presensi untuk mendapatkan tanggal
$query = "SELECT tanggal FROM presensi WHERE id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Data presensi tidak ditemukan!');
    redirect('admin/monitoring.php');
}

$presensi = mysqli_fetch_assoc($result);
$tanggal = $presensi['tanggal'];

// Hapus data presensi
$query = "DELETE FROM presensi WHERE id = '$id'";

if (mysqli_query($conn, $query)) {
    setMessage('success', 'Data presensi berhasil dihapus!');
} else {
    setMessage('danger', 'Gagal menghapus data presensi: ' . mysqli_error($conn));
}

// Redirect ke halaman monitoring dengan tanggal yang sama
redirect('admin/monitoring.php?tanggal=' . $tanggal);
?>
