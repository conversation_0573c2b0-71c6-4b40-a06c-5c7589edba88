<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil data karyawan untuk filter
$karyawan = getAllKaryawan();

// Filter data
$filter_user_id = isset($_GET['user_id']) ? clean($_GET['user_id']) : '';
$filter_periode = isset($_GET['periode']) ? clean($_GET['periode']) : 'hari';
$filter_tanggal = isset($_GET['tanggal']) ? clean($_GET['tanggal']) : date('Y-m-d');

// Tentukan rentang tanggal berdasarkan periode
$tanggal_awal = $filter_tanggal;
$tanggal_akhir = $filter_tanggal;

if ($filter_periode == 'minggu') {
    // Ambil tanggal awal minggu (<PERSON><PERSON>) dan akhir minggu (Minggu)
    $timestamp = strtotime($filter_tanggal);
    $day_of_week = date('N', $timestamp);
    $tanggal_awal = date('Y-m-d', strtotime("-" . ($day_of_week - 1) . " days", $timestamp));
    $tanggal_akhir = date('Y-m-d', strtotime("+" . (7 - $day_of_week) . " days", $timestamp));
} elseif ($filter_periode == 'bulan') {
    // Ambil tanggal awal bulan dan akhir bulan
    $tanggal_awal = date('Y-m-01', strtotime($filter_tanggal));
    $tanggal_akhir = date('Y-m-t', strtotime($filter_tanggal));
}

// Query untuk statistik aktivitas
$query_stats = "SELECT 
                    COUNT(*) as total_aktivitas,
                    SUM(CASE WHEN status = 'di dalam radius' THEN 1 ELSE 0 END) as dalam_radius,
                    SUM(CASE WHEN status = 'di luar radius' THEN 1 ELSE 0 END) as luar_radius,
                    AVG(jarak_dari_kantor) as rata_jarak
                FROM aktivitas_karyawan
                WHERE tanggal BETWEEN '$tanggal_awal' AND '$tanggal_akhir'";

if (!empty($filter_user_id)) {
    $query_stats .= " AND user_id = '$filter_user_id'";
}

$result_stats = mysqli_query($conn, $query_stats);
$stats = mysqli_fetch_assoc($result_stats);

// Query untuk data per jam (untuk diagram)
$query_per_jam = "SELECT 
                    HOUR(waktu) as jam,
                    COUNT(*) as jumlah,
                    SUM(CASE WHEN status = 'di dalam radius' THEN 1 ELSE 0 END) as dalam_radius,
                    SUM(CASE WHEN status = 'di luar radius' THEN 1 ELSE 0 END) as luar_radius
                FROM aktivitas_karyawan
                WHERE tanggal BETWEEN '$tanggal_awal' AND '$tanggal_akhir'";

if (!empty($filter_user_id)) {
    $query_per_jam .= " AND user_id = '$filter_user_id'";
}

$query_per_jam .= " GROUP BY HOUR(waktu) ORDER BY HOUR(waktu)";

$result_per_jam = mysqli_query($conn, $query_per_jam);
$data_per_jam = [];
while ($row = mysqli_fetch_assoc($result_per_jam)) {
    $data_per_jam[] = $row;
}

// Query untuk data per hari (untuk diagram mingguan dan bulanan)
if ($filter_periode != 'hari') {
    $query_per_hari = "SELECT 
                        tanggal,
                        COUNT(*) as jumlah,
                        SUM(CASE WHEN status = 'di dalam radius' THEN 1 ELSE 0 END) as dalam_radius,
                        SUM(CASE WHEN status = 'di luar radius' THEN 1 ELSE 0 END) as luar_radius
                    FROM aktivitas_karyawan
                    WHERE tanggal BETWEEN '$tanggal_awal' AND '$tanggal_akhir'";

    if (!empty($filter_user_id)) {
        $query_per_hari .= " AND user_id = '$filter_user_id'";
    }

    $query_per_hari .= " GROUP BY tanggal ORDER BY tanggal";

    $result_per_hari = mysqli_query($conn, $query_per_hari);
    $data_per_hari = [];
    while ($row = mysqli_fetch_assoc($result_per_hari)) {
        $data_per_hari[] = $row;
    }
}

// Query untuk data per lokasi
$query_per_lokasi = "SELECT 
                        l.nama_lokasi,
                        COUNT(*) as jumlah,
                        SUM(CASE WHEN ak.status = 'di dalam radius' THEN 1 ELSE 0 END) as dalam_radius,
                        SUM(CASE WHEN ak.status = 'di luar radius' THEN 1 ELSE 0 END) as luar_radius
                    FROM aktivitas_karyawan ak
                    LEFT JOIN lokasi l ON ak.lokasi_id = l.id
                    WHERE ak.tanggal BETWEEN '$tanggal_awal' AND '$tanggal_akhir'";

if (!empty($filter_user_id)) {
    $query_per_lokasi .= " AND ak.user_id = '$filter_user_id'";
}

$query_per_lokasi .= " GROUP BY l.id ORDER BY jumlah DESC";

$result_per_lokasi = mysqli_query($conn, $query_per_lokasi);
$data_per_lokasi = [];
while ($row = mysqli_fetch_assoc($result_per_lokasi)) {
    $data_per_lokasi[] = $row;
}

// Query untuk data aktivitas detail
$query_detail = "SELECT ak.*, u.nik, u.nama, l.nama_lokasi 
                FROM aktivitas_karyawan ak
                JOIN users u ON ak.user_id = u.id
                LEFT JOIN lokasi l ON ak.lokasi_id = l.id
                WHERE ak.tanggal BETWEEN '$tanggal_awal' AND '$tanggal_akhir'";

if (!empty($filter_user_id)) {
    $query_detail .= " AND ak.user_id = '$filter_user_id'";
}

$query_detail .= " ORDER BY ak.tanggal DESC, ak.waktu DESC";

$result_detail = mysqli_query($conn, $query_detail);
$data_detail = [];
while ($row = mysqli_fetch_assoc($result_detail)) {
    $data_detail[] = $row;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Laporan Aktivitas Karyawan</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="aktivitas_karyawan.php">Aktivitas Karyawan</a></li>
        <li class="breadcrumb-item active">Laporan Aktivitas</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Laporan
        </div>
        <div class="card-body">
            <form method="get" action="" class="row g-3">
                <div class="col-md-3">
                    <label for="user_id" class="form-label">Karyawan</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">Semua Karyawan</option>
                        <?php foreach ($karyawan as $k): ?>
                            <option value="<?php echo $k['id']; ?>" <?php echo $filter_user_id == $k['id'] ? 'selected' : ''; ?>>
                                <?php echo $k['nik'] . ' - ' . $k['nama']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="periode" class="form-label">Periode</label>
                    <select class="form-select" id="periode" name="periode">
                        <option value="hari" <?php echo $filter_periode == 'hari' ? 'selected' : ''; ?>>Harian</option>
                        <option value="minggu" <?php echo $filter_periode == 'minggu' ? 'selected' : ''; ?>>Mingguan</option>
                        <option value="bulan" <?php echo $filter_periode == 'bulan' ? 'selected' : ''; ?>>Bulanan</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="tanggal" class="form-label">Tanggal</label>
                    <input type="date" class="form-control" id="tanggal" name="tanggal" value="<?php echo $filter_tanggal; ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <a href="laporan_aktivitas.php" class="btn btn-secondary">
                        <i class="fas fa-sync-alt me-1"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small">Total Aktivitas</div>
                            <div class="fs-4"><?php echo number_format($stats['total_aktivitas']); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-map-marker-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <span>Periode: <?php echo date('d/m/Y', strtotime($tanggal_awal)) . ($tanggal_awal != $tanggal_akhir ? ' - ' . date('d/m/Y', strtotime($tanggal_akhir)) : ''); ?></span>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small">Di Dalam Radius</div>
                            <div class="fs-4"><?php echo number_format($stats['dalam_radius']); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <span><?php echo $stats['total_aktivitas'] > 0 ? round(($stats['dalam_radius'] / $stats['total_aktivitas']) * 100, 2) : 0; ?>% dari total aktivitas</span>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small">Di Luar Radius</div>
                            <div class="fs-4"><?php echo number_format($stats['luar_radius']); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <span><?php echo $stats['total_aktivitas'] > 0 ? round(($stats['luar_radius'] / $stats['total_aktivitas']) * 100, 2) : 0; ?>% dari total aktivitas</span>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small">Rata-rata Jarak</div>
                            <div class="fs-4"><?php echo number_format($stats['rata_jarak'], 2); ?> m</div>
                        </div>
                        <div>
                            <i class="fas fa-route fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <span>Jarak rata-rata dari lokasi kantor</span>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Diagram Aktivitas Per Jam -->
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-1"></i>
                    Aktivitas Per Jam
                </div>
                <div class="card-body">
                    <canvas id="chartPerJam" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>

        <!-- Diagram Status Aktivitas -->
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Status Aktivitas
                </div>
                <div class="card-body">
                    <canvas id="chartStatus" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>
    </div>

    <?php if ($filter_periode != 'hari'): ?>
    <div class="row">
        <!-- Diagram Aktivitas Per Hari -->
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-line me-1"></i>
                    Aktivitas Per Hari
                </div>
                <div class="card-body">
                    <canvas id="chartPerHari" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- Diagram Aktivitas Per Lokasi -->
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-1"></i>
                    Aktivitas Per Lokasi
                </div>
                <div class="card-body">
                    <canvas id="chartPerLokasi" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Detail Aktivitas
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-datatable" id="detailTable" data-order='[[0, "desc"], [1, "desc"]]'>
                    <thead>
                        <tr>
                            <th>Tanggal</th>
                            <th>Waktu</th>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Lokasi Kantor</th>
                            <th>Status</th>
                            <th>Jarak (m)</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($data_detail as $d): ?>
                            <tr>
                                <td><?php echo date('d/m/Y', strtotime($d['tanggal'])); ?></td>
                                <td><?php echo date('H:i:s', strtotime($d['waktu'])); ?></td>
                                <td><?php echo $d['nik']; ?></td>
                                <td><?php echo $d['nama']; ?></td>
                                <td><?php echo $d['nama_lokasi'] ?? 'Tidak ada'; ?></td>
                                <td>
                                    <?php if ($d['status'] == 'di dalam radius'): ?>
                                        <span class="badge bg-success">Di Dalam Radius</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Di Luar Radius</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo number_format($d['jarak_dari_kantor'], 2); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info view-map" 
                                            data-lat="<?php echo $d['latitude']; ?>" 
                                            data-lng="<?php echo $d['longitude']; ?>"
                                            data-nama="<?php echo $d['nama']; ?>"
                                            data-waktu="<?php echo date('d/m/Y H:i:s', strtotime($d['tanggal'] . ' ' . $d['waktu'])); ?>">
                                        <i class="fas fa-map-marked-alt"></i> Lihat Peta
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk menampilkan peta -->
<div class="modal fade" id="mapModal" tabindex="-1" aria-labelledby="mapModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mapModalLabel">Lokasi Karyawan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="map" style="height: 400px;"></div>
                <div class="mt-3">
                    <p><strong>Karyawan:</strong> <span id="mapNama"></span></p>
                    <p><strong>Waktu:</strong> <span id="mapWaktu"></span></p>
                    <p><strong>Koordinat:</strong> <span id="mapCoords"></span></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    $(document).ready(function() {
        // Inisialisasi peta saat modal dibuka
        $('.view-map').on('click', function() {
            const lat = $(this).data('lat');
            const lng = $(this).data('lng');
            const nama = $(this).data('nama');
            const waktu = $(this).data('waktu');

            // Set informasi di modal
            $('#mapNama').text(nama);
            $('#mapWaktu').text(waktu);
            $('#mapCoords').text(`${lat}, ${lng}`);

            // Tampilkan modal
            $('#mapModal').modal('show');

            // Inisialisasi peta setelah modal ditampilkan
            $('#mapModal').on('shown.bs.modal', function() {
                // Buat peta
                const map = L.map('map').setView([lat, lng], 15);

                // Tambahkan layer peta
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(map);

                // Tambahkan marker
                L.marker([lat, lng]).addTo(map)
                    .bindPopup(`<b>${nama}</b><br>Waktu: ${waktu}`).openPopup();

                // Perbaiki ukuran peta setelah modal ditampilkan
                map.invalidateSize();
            });
        });

        // Reset peta saat modal ditutup
        $('#mapModal').on('hidden.bs.modal', function() {
            $('#map').html('');
        });

        // Chart Aktivitas Per Jam
        const ctxPerJam = document.getElementById('chartPerJam');
        new Chart(ctxPerJam, {
            type: 'bar',
            data: {
                labels: [
                    <?php 
                    for ($i = 0; $i < 24; $i++) {
                        echo "'" . sprintf("%02d:00", $i) . "',";
                    }
                    ?>
                ],
                datasets: [
                    {
                        label: 'Di Dalam Radius',
                        backgroundColor: 'rgba(40, 167, 69, 0.7)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1,
                        data: [
                            <?php 
                            for ($i = 0; $i < 24; $i++) {
                                $found = false;
                                foreach ($data_per_jam as $d) {
                                    if ((int)$d['jam'] === $i) {
                                        echo $d['dalam_radius'] . ',';
                                        $found = true;
                                        break;
                                    }
                                }
                                if (!$found) {
                                    echo '0,';
                                }
                            }
                            ?>
                        ]
                    },
                    {
                        label: 'Di Luar Radius',
                        backgroundColor: 'rgba(220, 53, 69, 0.7)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 1,
                        data: [
                            <?php 
                            for ($i = 0; $i < 24; $i++) {
                                $found = false;
                                foreach ($data_per_jam as $d) {
                                    if ((int)$d['jam'] === $i) {
                                        echo $d['luar_radius'] . ',';
                                        $found = true;
                                        break;
                                    }
                                }
                                if (!$found) {
                                    echo '0,';
                                }
                            }
                            ?>
                        ]
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    x: {
                        stacked: true,
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true
                    }
                }
            }
        });

        // Chart Status Aktivitas
        const ctxStatus = document.getElementById('chartStatus');
        new Chart(ctxStatus, {
            type: 'pie',
            data: {
                labels: ['Di Dalam Radius', 'Di Luar Radius'],
                datasets: [{
                    data: [<?php echo $stats['dalam_radius']; ?>, <?php echo $stats['luar_radius']; ?>],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(220, 53, 69, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });

        <?php if ($filter_periode != 'hari'): ?>
        // Chart Aktivitas Per Hari
        const ctxPerHari = document.getElementById('chartPerHari');
        new Chart(ctxPerHari, {
            type: 'line',
            data: {
                labels: [
                    <?php 
                    foreach ($data_per_hari as $d) {
                        echo "'" . date('d/m/Y', strtotime($d['tanggal'])) . "',";
                    }
                    ?>
                ],
                datasets: [
                    {
                        label: 'Total Aktivitas',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderColor: 'rgba(0, 123, 255, 1)',
                        borderWidth: 2,
                        fill: true,
                        data: [
                            <?php 
                            foreach ($data_per_hari as $d) {
                                echo $d['jumlah'] . ',';
                            }
                            ?>
                        ]
                    },
                    {
                        label: 'Di Dalam Radius',
                        backgroundColor: 'transparent',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(40, 167, 69, 1)',
                        data: [
                            <?php 
                            foreach ($data_per_hari as $d) {
                                echo $d['dalam_radius'] . ',';
                            }
                            ?>
                        ]
                    },
                    {
                        label: 'Di Luar Radius',
                        backgroundColor: 'transparent',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(220, 53, 69, 1)',
                        data: [
                            <?php 
                            foreach ($data_per_hari as $d) {
                                echo $d['luar_radius'] . ',';
                            }
                            ?>
                        ]
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        <?php endif; ?>

        // Chart Aktivitas Per Lokasi
        const ctxPerLokasi = document.getElementById('chartPerLokasi');
        new Chart(ctxPerLokasi, {
            type: 'bar',
            data: {
                labels: [
                    <?php 
                    foreach ($data_per_lokasi as $d) {
                        echo "'" . ($d['nama_lokasi'] ?? 'Tidak ada') . "',";
                    }
                    ?>
                ],
                datasets: [
                    {
                        label: 'Di Dalam Radius',
                        backgroundColor: 'rgba(40, 167, 69, 0.7)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1,
                        data: [
                            <?php 
                            foreach ($data_per_lokasi as $d) {
                                echo $d['dalam_radius'] . ',';
                            }
                            ?>
                        ]
                    },
                    {
                        label: 'Di Luar Radius',
                        backgroundColor: 'rgba(220, 53, 69, 0.7)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 1,
                        data: [
                            <?php 
                            foreach ($data_per_lokasi as $d) {
                                echo $d['luar_radius'] . ',';
                            }
                            ?>
                        ]
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    x: {
                        stacked: true,
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true
                    }
                }
            }
        });
    });
</script>
