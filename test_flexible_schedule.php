<?php
// Test script untuk mengecek fitur flexible schedule
require_once 'config/database.php';

// Test 1: <PERSON>k apakah kolom allow_flexible_schedule ada
echo "<h2>Test 1: Cek kolom allow_flexible_schedule</h2>";
$query = "SHOW COLUMNS FROM users LIKE 'allow_flexible_schedule'";
$result = mysqli_query($conn, $query);
if (mysqli_num_rows($result) > 0) {
    echo "✅ Kolom allow_flexible_schedule sudah ada<br>";
} else {
    echo "❌ Kolom allow_flexible_schedule belum ada<br>";
}

// Test 2: Cek apakah tabel user_schedule_choices ada
echo "<h2>Test 2: Cek tabel user_schedule_choices</h2>";
$query = "SHOW TABLES LIKE 'user_schedule_choices'";
$result = mysqli_query($conn, $query);
if (mysqli_num_rows($result) > 0) {
    echo "✅ Tabel user_schedule_choices sudah ada<br>";
} else {
    echo "❌ Tabel user_schedule_choices belum ada<br>";
}

// Test 3: Cek data karyawan dengan izin flexible schedule
echo "<h2>Test 3: <PERSON><PERSON><PERSON> dengan izin flexible schedule</h2>";
$query = "SELECT id, nama, nik, allow_flexible_schedule FROM users WHERE role = 'karyawan'";
$result = mysqli_query($conn, $query);
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Nama</th><th>NIK</th><th>Flexible Schedule</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        $status = $row['allow_flexible_schedule'] == 1 ? '✅ Ya' : '❌ Tidak';
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['nama']}</td>";
        echo "<td>{$row['nik']}</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error: " . mysqli_error($conn);
}

// Test 4: Cek jam kerja yang tersedia
echo "<h2>Test 4: Jam kerja yang tersedia</h2>";
$query = "SELECT jk.*, jkb.bidang_id, b.nama_bidang 
          FROM jam_kerja jk 
          JOIN jam_kerja_bidang jkb ON jk.id = jkb.jam_kerja_id 
          JOIN bidang b ON jkb.bidang_id = b.id";
$result = mysqli_query($conn, $query);
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Nama Jam Kerja</th><th>Jam Masuk</th><th>Jam Pulang</th><th>Bidang</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['nama_jam_kerja']}</td>";
        echo "<td>{$row['jam_masuk']}</td>";
        echo "<td>{$row['jam_pulang']}</td>";
        echo "<td>{$row['nama_bidang']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error: " . mysqli_error($conn);
}

// Test 5: Simulasi kondisi untuk pop-up
echo "<h2>Test 5: Simulasi kondisi pop-up</h2>";
$user_id = 1; // Ganti dengan ID user yang ingin ditest
$today = date('Y-m-d');

// Ambil data user
$query = "SELECT * FROM users WHERE id = '$user_id'";
$result = mysqli_query($conn, $query);
$user = mysqli_fetch_assoc($result);

if ($user) {
    echo "User: {$user['nama']} (ID: {$user['id']})<br>";
    echo "Allow flexible schedule: " . ($user['allow_flexible_schedule'] == 1 ? 'Ya' : 'Tidak') . "<br>";
    
    // Cek apakah sudah memilih jam kerja hari ini
    $query = "SELECT * FROM user_schedule_choices WHERE user_id = '$user_id' AND tanggal = '$today'";
    $result = mysqli_query($conn, $query);
    $selected_schedule = mysqli_fetch_assoc($result);
    echo "Sudah pilih jam kerja hari ini: " . ($selected_schedule ? 'Ya' : 'Tidak') . "<br>";
    
    // Cek jam kerja options
    $query = "SELECT DISTINCT jk.* 
              FROM jam_kerja jk 
              JOIN jam_kerja_bidang jkb ON jk.id = jkb.jam_kerja_id 
              WHERE jkb.bidang_id = '{$user['bidang_id']}'";
    $result = mysqli_query($conn, $query);
    $jam_kerja_options = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $jam_kerja_options[] = $row;
    }
    echo "Jam kerja options tersedia: " . count($jam_kerja_options) . "<br>";
    
    // Cek presensi hari ini
    $query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
    $result = mysqli_query($conn, $query);
    $presensi_hari_ini = mysqli_fetch_assoc($result);
    echo "Sudah absen hari ini: " . ($presensi_hari_ini ? 'Ya' : 'Tidak') . "<br>";
    
    // Evaluasi kondisi pop-up
    $allow_flexible_schedule = $user['allow_flexible_schedule'] == 1;
    $should_show_popup = $allow_flexible_schedule && !$selected_schedule && !empty($jam_kerja_options) && empty($presensi_hari_ini);
    
    echo "<br><strong>Kondisi Pop-up:</strong><br>";
    echo "- Allow flexible schedule: " . ($allow_flexible_schedule ? '✅' : '❌') . "<br>";
    echo "- Belum pilih jam kerja: " . (!$selected_schedule ? '✅' : '❌') . "<br>";
    echo "- Ada jam kerja options: " . (!empty($jam_kerja_options) ? '✅' : '❌') . "<br>";
    echo "- Belum absen hari ini: " . (empty($presensi_hari_ini) ? '✅' : '❌') . "<br>";
    echo "<br><strong>Hasil: Pop-up " . ($should_show_popup ? '✅ AKAN MUNCUL' : '❌ TIDAK MUNCUL') . "</strong><br>";
    
} else {
    echo "❌ User dengan ID $user_id tidak ditemukan";
}
?>
