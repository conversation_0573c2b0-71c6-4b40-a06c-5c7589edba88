<?php
// Tentukan halaman aktif
$current_page = basename($_SERVER['PHP_SELF']);

// Cek apakah karyawan sedang dalam perjalanan dinas
$izin_dinas_aktif = false;
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    $today = date('Y-m-d');

    // Koneksi ke database sudah dilakukan di file yang meng-include bottom-nav.php
    // Jadi tidak perlu melakukan koneksi lagi di sini

    // Query untuk memeriksa izin dinas aktif
    $query = "SELECT * FROM izin_dinas
              WHERE user_id = '$user_id'
              AND status = 'Approved'
              AND tanggal_mulai <= '$today'
              AND tanggal_selesai >= '$today'";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $izin_dinas_aktif = mysqli_fetch_assoc($result);
    }
}
?>

<!-- Bottom Navigation Bar -->
<div class="bottom-nav">
    <a href="<?php echo BASE_URL; ?>karyawan/index.php" class="bottom-nav-item <?php echo ($current_page == 'index.php') ? 'active' : ''; ?>">
        <div class="bottom-nav-icon">
            <i class="fas fa-home"></i>
        </div>
        <div class="bottom-nav-text">Beranda</div>
    </a>
    <a href="<?php echo BASE_URL; ?>karyawan/riwayat.php" class="bottom-nav-item <?php echo ($current_page == 'riwayat.php') ? 'active' : ''; ?>">
        <div class="bottom-nav-icon">
            <i class="fas fa-history"></i>
        </div>
        <div class="bottom-nav-text">Riwayat</div>
    </a>
        <?php if ($izin_dinas_aktif): ?>
    <a href="javascript:void(0)" class="bottom-nav-item disabled <?php echo ($current_page == 'presensi.php') ? 'active' : ''; ?>" onclick="showDinasAlert()">
        <div class="bottom-nav-icon">
            <i class="fas fa-camera" style="color: #adb5bd;"></i>
        </div>
        <div class="bottom-nav-text" style="color: #adb5bd;">Absensi</div>
    </a>
    <?php else: ?>
    <a href="<?php echo BASE_URL; ?>karyawan/presensi.php" class="bottom-nav-item <?php echo ($current_page == 'presensi.php') ? 'active' : ''; ?>">
        <div class="bottom-nav-icon">
            <i class="fas fa-camera"></i>
        </div>
        <div class="bottom-nav-text">Absensi</div>
    </a>
    <?php endif; ?>
    <a href="<?php echo BASE_URL; ?>karyawan/izin_dinas.php" class="bottom-nav-item <?php echo ($current_page == 'izin_dinas.php') ? 'active' : ''; ?>">
        <div class="bottom-nav-icon">
            <i class="fas fa-plane"></i>
        </div>
        <div class="bottom-nav-text">Izin Dinas</div>
    </a>
    <a href="<?php echo BASE_URL; ?>profile.php" class="bottom-nav-item <?php echo ($current_page == 'profile.php' || $current_page == 'change_password.php') ? 'active' : ''; ?>">
        <div class="bottom-nav-icon">
            <i class="fas fa-user"></i>
        </div>
        <div class="bottom-nav-text">Profil</div>
    </a>
</div>

<?php if ($izin_dinas_aktif): ?>
<script>
    function showDinasAlert() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'info',
                title: 'Perjalanan Dinas Aktif',
                text: 'Anda sedang dalam perjalanan dinas ke <?php echo $izin_dinas_aktif['tujuan']; ?>. Presensi Anda akan diisi otomatis selama periode perjalanan dinas.',
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'Saya Mengerti'
            });
        } else {
            alert('Anda sedang dalam perjalanan dinas. Presensi diisi otomatis.');
        }
    }
</script>
<?php endif; ?>
