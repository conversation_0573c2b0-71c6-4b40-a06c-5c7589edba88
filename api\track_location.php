<?php
/**
 * API untuk menerima data lokasi karyawan
 * Endpoint ini menerima data lokasi dari aplikasi Android dan menyimpannya ke database
 *
 * Mendukung dua format data:
 *
 * 1. Format data tunggal:
 * {
 *     "api_key": "absensiku_api_key_2023",
 *     "nik": "123456789",
 *     "longitude": "106.8456",
 *     "latitude": "-6.2088"
 * }
 *
 * 2. Format data batch (multiple):
 * {
 *     "api_key": "absensiku_api_key_2023",
 *     "locations": [
 *         {
 *             "nik": "123456789",
 *             "longitude": "106.8456",
 *             "latitude": "-6.2088"
 *         },
 *         {
 *             "nik": "987654321",
 *             "longitude": "106.8256",
 *             "latitude": "-6.1988"
 *         }
 *     ]
 * }
 */

// Header untuk API
header('Content-Type: application/json');

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Fungsi untuk validasi API key
function validateApiKey($api_key) {
    // Kunci API yang valid (sebaiknya disimpan di file konfigurasi terpisah)
    $valid_api_key = 'absensiku_api_key_2023';

    return $api_key === $valid_api_key;
}

// Fungsi untuk mendapatkan user_id berdasarkan NIK
function getUserIdByNik($nik) {
    global $conn;

    // Bersihkan input
    $nik = clean($nik);

    // Cek apakah NIK valid
    $query = "SELECT id FROM users WHERE nik = '$nik' AND role = 'karyawan'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);
        return $user['id'];
    }

    return false;
}

// Fungsi untuk menyimpan data lokasi
function saveLocation($user_id, $longitude, $latitude) {
    global $conn;

    // Bersihkan input
    $user_id = clean($user_id);
    $longitude = clean($longitude);
    $latitude = clean($latitude);

    // Tanggal dan waktu saat ini
    $tanggal = date('Y-m-d');
    $waktu = date('H:i:s');

    // Cek lokasi kantor terdekat dan hitung jarak
    $query = "SELECT id, nama_lokasi, longitude, latitude, radius FROM lokasi";
    $result = mysqli_query($conn, $query);

    $lokasi_id = null;
    $status = 'di luar radius';
    $jarak_terdekat = PHP_FLOAT_MAX;

    while ($lokasi = mysqli_fetch_assoc($result)) {
        $jarak = hitungJarak($latitude, $longitude, $lokasi['latitude'], $lokasi['longitude']);

        if ($jarak < $jarak_terdekat) {
            $jarak_terdekat = $jarak;
            $lokasi_id = $lokasi['id'];

            // Cek apakah di dalam radius
            if ($jarak <= $lokasi['radius']) {
                $status = 'di dalam radius';
            }
        }
    }

    // Simpan data lokasi ke database
    $query = "INSERT INTO aktivitas_karyawan (user_id, tanggal, waktu, longitude, latitude, status, jarak_dari_kantor, lokasi_id)
              VALUES ('$user_id', '$tanggal', '$waktu', '$longitude', '$latitude', '$status', '$jarak_terdekat', " . ($lokasi_id ? "'$lokasi_id'" : "NULL") . ")";

    if (mysqli_query($conn, $query)) {
        return true;
    } else {
        // Log error untuk debugging
        error_log("Error saveLocation: " . mysqli_error($conn) . " Query: " . $query);
        return false;
    }
}

// Cek metode request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => 'Metode tidak diizinkan'
    ]);
    exit;
}

// Ambil data dari request
$data = json_decode(file_get_contents('php://input'), true);

// Jika data tidak valid, coba ambil dari $_POST
if (!$data) {
    $data = $_POST;
}

// Inisialisasi array untuk menyimpan hasil
$results = [];
$success_count = 0;
$error_count = 0;

// Cek apakah data adalah array dari beberapa lokasi atau satu lokasi
if (isset($data['locations']) && is_array($data['locations'])) {
    // Format data: { "api_key": "xxx", "locations": [ { "nik": "123", "longitude": "106.8", "latitude": "-6.2" }, ... ] }

    // Validasi API key
    if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
        echo json_encode([
            'status' => 'error',
            'message' => 'API key tidak valid'
        ]);
        exit;
    }

    // Proses setiap data lokasi
    foreach ($data['locations'] as $index => $location) {
        $result = [
            'index' => $index,
            'status' => 'error',
            'message' => ''
        ];

        // Validasi data lokasi
        if (!isset($location['nik']) || !isset($location['longitude']) || !isset($location['latitude'])) {
            $result['message'] = 'Data tidak lengkap';
            $results[] = $result;
            $error_count++;
            continue;
        }

        // Dapatkan user_id berdasarkan NIK
        $user_id = getUserIdByNik($location['nik']);
        if (!$user_id) {
            $result['message'] = 'NIK tidak valid';
            $results[] = $result;
            $error_count++;
            continue;
        }

        // Simpan data lokasi
        if (saveLocation($user_id, $location['longitude'], $location['latitude'])) {
            $result['status'] = 'success';
            $result['message'] = 'Data lokasi berhasil disimpan';
            $success_count++;
        } else {
            $result['message'] = 'Gagal menyimpan data lokasi';
            $error_count++;
        }

        $results[] = $result;
    }

    // Kirim respons
    echo json_encode([
        'status' => $error_count === 0 ? 'success' : ($success_count === 0 ? 'error' : 'partial'),
        'message' => "Berhasil menyimpan $success_count data, gagal menyimpan $error_count data",
        'results' => $results
    ]);

} else {
    // Format data lama: { "api_key": "xxx", "nik": "123", "longitude": "106.8", "latitude": "-6.2" }

    // Validasi data yang diterima
    if (!isset($data['api_key']) || !isset($data['nik']) ||
        !isset($data['longitude']) || !isset($data['latitude'])) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Data tidak lengkap'
        ]);
        exit;
    }

    // Validasi API key
    if (!validateApiKey($data['api_key'])) {
        echo json_encode([
            'status' => 'error',
            'message' => 'API key tidak valid'
        ]);
        exit;
    }

    // Dapatkan user_id berdasarkan NIK
    $user_id = getUserIdByNik($data['nik']);
    if (!$user_id) {
        echo json_encode([
            'status' => 'error',
            'message' => 'NIK tidak valid'
        ]);
        exit;
    }

    // Simpan data lokasi
    if (saveLocation($user_id, $data['longitude'], $data['latitude'])) {
        echo json_encode([
            'status' => 'success',
            'message' => 'Data lokasi berhasil disimpan'
        ]);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'Gagal menyimpan data lokasi'
        ]);
    }
}
