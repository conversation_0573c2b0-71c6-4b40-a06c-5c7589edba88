<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek parameter id
if (!isset($_GET['id'])) {
    setMessage('danger', 'ID rapat tidak valid!');
    redirect('admin/rapat.php');
}

$id = clean($_GET['id']);

// Ambil data rapat
$query = "SELECT r.*,
          u1.nama as created_by_name,
          u2.nama as penanggung_jawab_name,
          u2.nik as penanggung_jawab_nik
          FROM rapat r
          JOIN users u1 ON r.created_by = u1.id
          LEFT JOIN users u2 ON r.penanggung_jawab_id = u2.id
          WHERE r.id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Data rapat tidak ditemukan!');
    redirect('admin/rapat.php');
}

$rapat = mysqli_fetch_assoc($result);

// Ambil data peserta rapat
$query = "SELECT rp.*, u.nik, u.nama, u.bidang, u.foto_profil
          FROM rapat_peserta rp
          JOIN users u ON rp.user_id = u.id
          WHERE rp.rapat_id = '$id'
          ORDER BY u.nama ASC";
$result = mysqli_query($conn, $query);

$peserta_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $peserta_list[] = $row;
    }
}

// Hitung statistik kehadiran
$total_peserta = count($peserta_list);
$total_hadir = 0;
$total_tidak_hadir = 0;

foreach ($peserta_list as $peserta) {
    if ($peserta['status'] == 'hadir') {
        $total_hadir++;
    } else if ($peserta['status'] == 'tidak hadir') {
        $total_tidak_hadir++;
    }
}

$persentase_kehadiran = $total_peserta > 0 ? round(($total_hadir / $total_peserta) * 100) : 0;

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Detail Rapat</h1>
        <div>
            <a href="cetak_kehadiran_rapat.php?id=<?php echo $rapat['id']; ?>" class="btn btn-primary me-2" target="_blank">
                <i class="fas fa-print"></i> Cetak Kehadiran
            </a>
            <a href="print_barcode_rapat.php?id=<?php echo $rapat['id']; ?>" class="btn btn-success me-2" target="_blank">
                <i class="fas fa-qrcode"></i> Cetak Barcode
            </a>
            <a href="rapat.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['message'])): ?>
    <div class="alert alert-<?php echo $_SESSION['message']['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['message']['text']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php unset($_SESSION['message']); endif; ?>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informasi Rapat</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="30%">Judul</th>
                            <td><?php echo $rapat['judul']; ?></td>
                        </tr>
                        <tr>
                            <th>Tanggal</th>
                            <td><?php echo date('d/m/Y', strtotime($rapat['tanggal'])); ?></td>
                        </tr>
                        <tr>
                            <th>Waktu</th>
                            <td><?php echo date('H:i', strtotime($rapat['waktu_mulai'])) . ' - ' . date('H:i', strtotime($rapat['waktu_selesai'])); ?></td>
                        </tr>
                        <tr>
                            <th>Lokasi</th>
                            <td><?php echo $rapat['lokasi']; ?></td>
                        </tr>
                        <tr>
                            <th>Deskripsi</th>
                            <td><?php echo nl2br($rapat['deskripsi'] ?? '-'); ?></td>
                        </tr>
                        <tr>
                            <th>Penanggung Jawab</th>
                            <td>
                                <?php if (!empty($rapat['penanggung_jawab_name'])): ?>
                                    <?php echo $rapat['penanggung_jawab_name']; ?> (<?php echo $rapat['penanggung_jawab_nik']; ?>)
                                <?php else: ?>
                                    <span class="text-muted">Belum ditentukan</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Dibuat Oleh</th>
                            <td><?php echo $rapat['created_by_name']; ?></td>
                        </tr>
                        <tr>
                            <th>Tanggal Dibuat</th>
                            <td><?php echo date('d/m/Y H:i', strtotime($rapat['created_at'])); ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Barcode Rapat</h6>
                </div>
                <div class="card-body text-center">
                    <div class="barcode-container mb-3">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=<?php echo urlencode($rapat['barcode_value']); ?>" alt="Barcode" class="img-fluid">
                    </div>
                    <p class="text-muted">Scan barcode ini untuk melakukan absensi rapat</p>
                    <div class="d-flex justify-content-center gap-2">
                        <a href="print_barcode_rapat.php?id=<?php echo $rapat['id']; ?>" class="btn btn-success" target="_blank">
                            <i class="fas fa-qrcode"></i> Cetak Barcode
                        </a>
                        <a href="cetak_kehadiran_rapat.php?id=<?php echo $rapat['id']; ?>" class="btn btn-primary" target="_blank">
                            <i class="fas fa-print"></i> Cetak Kehadiran
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Statistik Kehadiran</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center mb-3">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white mb-3">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo $total_peserta; ?></h5>
                                    <p class="card-text">Total Peserta</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white mb-3">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo $total_hadir; ?></h5>
                                    <p class="card-text">Hadir</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-danger text-white mb-3">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo $total_tidak_hadir; ?></h5>
                                    <p class="card-text">Tidak Hadir</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="progress mb-3" style="height: 25px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $persentase_kehadiran; ?>%;" aria-valuenow="<?php echo $persentase_kehadiran; ?>" aria-valuemin="0" aria-valuemax="100">
                            <?php echo $persentase_kehadiran; ?>% Hadir
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">Daftar Peserta</h6>
                    <div class="input-group" style="width: 250px;">
                        <input type="text" class="form-control" id="searchPeserta" placeholder="Cari peserta...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Nama</th>
                                    <th>NIK</th>
                                    <th>Bidang</th>
                                    <th>Status</th>
                                    <th>Waktu Hadir</th>
                                </tr>
                            </thead>
                            <tbody id="pesertaTableBody">
                                <?php if (empty($peserta_list)): ?>
                                    <tr>
                                        <td colspan="5" class="text-center">Tidak ada data peserta</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($peserta_list as $peserta): ?>
                                        <tr class="peserta-row">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if (!empty($peserta['foto_profil'])): ?>
                                                        <img src="<?php echo BASE_URL . 'uploads/' . $peserta['foto_profil']; ?>" alt="Foto Profil" class="rounded-circle me-2" style="width: 30px; height: 30px;">
                                                    <?php else: ?>
                                                        <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php echo $peserta['nama']; ?>
                                                </div>
                                            </td>
                                            <td><?php echo $peserta['nik']; ?></td>
                                            <td><?php echo $peserta['bidang'] ?? '-'; ?></td>
                                            <td>
                                                <?php if ($peserta['status'] == 'hadir'): ?>
                                                    <span class="badge bg-success">Hadir</span>
                                                <?php elseif ($peserta['status'] == 'tidak hadir'): ?>
                                                    <span class="badge bg-danger">Tidak Hadir</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Belum Absen</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $peserta['waktu_hadir'] ? date('d/m/Y H:i', strtotime($peserta['waktu_hadir'])) : '-'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pencarian peserta
    const searchInput = document.getElementById('searchPeserta');
    const pesertaRows = document.querySelectorAll('.peserta-row');

    searchInput.addEventListener('keyup', function() {
        const searchValue = this.value.toLowerCase();

        pesertaRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchValue)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
