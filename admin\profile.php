<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil data user
$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = '$user_id'";
$result = mysqli_query($conn, $query);
$user = mysqli_fetch_assoc($result);

// Proses update profil
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nama = clean($_POST['nama']);
    $email = clean($_POST['email']);
    
    // Upload foto profil baru jika ada
    $foto_profil = null;
    if (isset($_FILES['foto_profil']) && $_FILES['foto_profil']['error'] == 0) {
        $file_tmp = $_FILES['foto_profil']['tmp_name'];
        $file_name = $_FILES['foto_profil']['name'];
        $file_size = $_FILES['foto_profil']['size'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Cek ekstensi file
        if (in_array($file_ext, ALLOWED_EXTENSIONS)) {
            // Cek ukuran file
            if ($file_size <= MAX_UPLOAD_SIZE) {
                $foto_profil = 'admin_profile_' . time() . '.' . $file_ext;
                $upload_path = UPLOAD_PATH . $foto_profil;

                if (move_uploaded_file($file_tmp, $upload_path)) {
                    // Berhasil upload

                    // Hapus foto lama jika ada
                    if (!empty($user['foto_profil'])) {
                        $old_file = UPLOAD_PATH . $user['foto_profil'];
                        if (file_exists($old_file)) {
                            unlink($old_file);
                        }
                    }

                    // Update foto profil
                    $query = "UPDATE users SET foto_profil = '$foto_profil' WHERE id = '$user_id'";
                    mysqli_query($conn, $query);
                } else {
                    setMessage('danger', 'Gagal mengupload foto profil!');
                    redirect('admin/profile.php');
                }
            } else {
                setMessage('danger', 'Ukuran file terlalu besar! Maksimal ' . (MAX_UPLOAD_SIZE / 1024 / 1024) . 'MB');
                redirect('admin/profile.php');
            }
        } else {
            setMessage('danger', 'Ekstensi file tidak diizinkan! Hanya ' . implode(', ', ALLOWED_EXTENSIONS) . ' yang diizinkan');
            redirect('admin/profile.php');
        }
    }

    // Update data profil
    $query = "UPDATE users SET nama = '$nama'";
    
    // Update email jika ada
    if (!empty($email)) {
        $query .= ", email = '$email'";
    }
    
    $query .= " WHERE id = '$user_id'";

    if (mysqli_query($conn, $query)) {
        // Update session
        $_SESSION['nama'] = $nama;

        setMessage('success', 'Profil berhasil diperbarui!');
        // Tambahkan parameter untuk menampilkan SweetAlert
        $_SESSION['show_sweet_alert'] = true;
    } else {
        setMessage('danger', 'Gagal memperbarui profil!');
    }

    redirect('admin/profile.php');
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Profil Admin</h1>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
        </a>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Foto Profil</h6>
                </div>
                <div class="card-body text-center">
                    <?php if (!empty($user['foto_profil'])): ?>
                        <img src="<?php echo BASE_URL . 'uploads/' . $user['foto_profil']; ?>" class="img-thumbnail rounded-circle mb-3" style="width: 200px; height: 200px; object-fit: cover;">
                    <?php else: ?>
                        <div class="mb-3" style="width: 200px; height: 200px; margin: 0 auto; background-color: #f8f9fa; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user-circle fa-5x text-secondary"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mt-3">
                        <h5><?php echo $user['nama']; ?></h5>
                        <p class="text-muted"><?php echo $user['nik']; ?></p>
                        <p><span class="badge bg-primary">Administrator</span></p>
                    </div>
                </div>
            </div>
            
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Akses Cepat</h6>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="change_password.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-key me-2"></i> Ubah Password
                        </a>
                        <a href="karyawan.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-users me-2"></i> Manajemen Karyawan
                        </a>
                        <a href="monitoring.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-desktop me-2"></i> Monitoring Presensi
                        </a>
                        <a href="laporan.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-alt me-2"></i> Laporan
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Edit Profil</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="nik" class="form-label">NIK</label>
                            <input type="text" class="form-control" id="nik" value="<?php echo $user['nik']; ?>" readonly>
                            <small class="text-muted">NIK tidak dapat diubah</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="nama" class="form-label">Nama</label>
                            <input type="text" class="form-control" id="nama" name="nama" value="<?php echo $user['nama']; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo $user['email'] ?? ''; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="foto_profil" class="form-label">Foto Profil</label>
                            <input type="file" class="form-control" id="foto_profil" name="foto_profil" accept="image/*">
                            <small class="text-muted">Biarkan kosong jika tidak ingin mengubah foto profil</small>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Simpan Perubahan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informasi Sistem</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Nama Aplikasi:</strong> <?php echo APP_NAME; ?></p>
                            <p><strong>Versi:</strong> 1.0</p>
                            <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE']; ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
                            <p><strong>Database:</strong> MySQL</p>
                            <p><strong>Waktu Server:</strong> <?php echo date('d-m-Y H:i:s'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Tampilkan SweetAlert jika ada pesan sukses
document.addEventListener('DOMContentLoaded', function() {
    <?php if (isset($_SESSION['message']) && isset($_SESSION['show_sweet_alert']) && $_SESSION['show_sweet_alert']): ?>
        Swal.fire({
            icon: '<?php echo ($_SESSION['message']['type'] == 'success') ? 'success' : 'error'; ?>',
            title: '<?php echo ($_SESSION['message']['type'] == 'success') ? 'Berhasil!' : 'Gagal!'; ?>',
            text: '<?php echo $_SESSION['message']['text']; ?>',
            timer: 2000,
            showConfirmButton: false
        });
        <?php unset($_SESSION['show_sweet_alert']); ?>
    <?php endif; ?>
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
