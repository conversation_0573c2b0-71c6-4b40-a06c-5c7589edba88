<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Cek apakah ada parameter id
if (!isset($_GET['id'])) {
    setMessage('danger', 'ID karyawan tidak valid');
    redirect('karyawan.php');
}

$id = $_GET['id'];

// Ambil data karyawan berdasarkan id
$query = "SELECT * FROM users WHERE id = '$id' AND role = 'karyawan'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Karyawan tidak ditemukan');
    redirect('karyawan.php');
}

$karyawan = mysqli_fetch_assoc($result);

// Proses reset password
if (isset($_POST['reset_password'])) {
    $password = $_POST['password'];

    // Validasi input
    if (empty($password)) {
        setMessage('danger', 'Password tidak boleh kosong');
        redirect('reset_password.php?id=' . $id);
    }

    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Update password
    $query = "UPDATE users SET password = '$hashed_password' WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Password berhasil direset');
        redirect('karyawan.php');
    } else {
        setMessage('danger', 'Gagal mereset password: ' . mysqli_error($conn));
        redirect('reset_password.php?id=' . $id);
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Reset Password Karyawan</h1>
        <a href="karyawan.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Form Reset Password</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Anda akan mereset password untuk karyawan: <strong><?php echo $karyawan['nama']; ?> (NIK: <?php echo $karyawan['nik']; ?>)</strong>
            </div>

            <form method="post" action="">
                <div class="mb-3">
                    <label for="password" class="form-label">Password Baru</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>

                <div class="mt-3">
                    <button type="submit" name="reset_password" class="btn btn-warning">
                        <i class="fas fa-key"></i> Reset Password
                    </button>
                    <a href="karyawan.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
