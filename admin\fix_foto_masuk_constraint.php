<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses admin
checkAccess('admin');

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        Perbaiki Constraint Kolom foto_masuk
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Script ini akan mengubah kolom foto_masuk dari NOT NULL menjadi DEFAULT NULL untuk mendukung absensi barcode.
                    </div>

                    <?php
                    // Cek struktur kolom foto_masuk saat ini
                    $query = "SHOW COLUMNS FROM presensi LIKE 'foto_masuk'";
                    $result = mysqli_query($conn, $query);
                    
                    if ($result && mysqli_num_rows($result) > 0) {
                        $column_info = mysqli_fetch_assoc($result);
                        echo "<div class='alert alert-secondary'>";
                        echo "<h6>Struktur Kolom foto_masuk Saat Ini:</h6>";
                        echo "<pre>" . print_r($column_info, true) . "</pre>";
                        echo "</div>";
                        
                        // Cek apakah kolom masih NOT NULL
                        if ($column_info['Null'] == 'NO') {
                            echo "<div class='alert alert-warning'>";
                            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                            echo "Kolom foto_masuk masih memiliki constraint NOT NULL. Perlu diperbaiki.";
                            echo "</div>";
                            
                            // Ubah constraint kolom
                            $alter_query = "ALTER TABLE presensi MODIFY COLUMN foto_masuk VARCHAR(255) DEFAULT NULL";
                            
                            if (mysqli_query($conn, $alter_query)) {
                                echo "<div class='alert alert-success'>";
                                echo "<i class='fas fa-check-circle me-2'></i>";
                                echo "Berhasil mengubah kolom foto_masuk menjadi DEFAULT NULL.";
                                echo "</div>";
                                
                                // Verifikasi perubahan
                                $verify_query = "SHOW COLUMNS FROM presensi LIKE 'foto_masuk'";
                                $verify_result = mysqli_query($conn, $verify_query);
                                if ($verify_result && mysqli_num_rows($verify_result) > 0) {
                                    $new_column_info = mysqli_fetch_assoc($verify_result);
                                    echo "<div class='alert alert-info'>";
                                    echo "<h6>Struktur Kolom foto_masuk Setelah Perubahan:</h6>";
                                    echo "<pre>" . print_r($new_column_info, true) . "</pre>";
                                    echo "</div>";
                                }
                            } else {
                                echo "<div class='alert alert-danger'>";
                                echo "<i class='fas fa-times-circle me-2'></i>";
                                echo "Gagal mengubah kolom foto_masuk: " . mysqli_error($conn);
                                echo "</div>";
                            }
                        } else {
                            echo "<div class='alert alert-success'>";
                            echo "<i class='fas fa-check-circle me-2'></i>";
                            echo "Kolom foto_masuk sudah memiliki constraint yang benar (DEFAULT NULL).";
                            echo "</div>";
                        }
                    } else {
                        echo "<div class='alert alert-danger'>";
                        echo "<i class='fas fa-times-circle me-2'></i>";
                        echo "Kolom foto_masuk tidak ditemukan di tabel presensi.";
                        echo "</div>";
                    }
                    
                    // Juga cek dan perbaiki kolom foto_pulang jika perlu
                    $query_pulang = "SHOW COLUMNS FROM presensi LIKE 'foto_pulang'";
                    $result_pulang = mysqli_query($conn, $query_pulang);
                    
                    if ($result_pulang && mysqli_num_rows($result_pulang) > 0) {
                        $column_info_pulang = mysqli_fetch_assoc($result_pulang);
                        echo "<div class='mt-4'>";
                        echo "<h6>Struktur Kolom foto_pulang:</h6>";
                        echo "<pre>" . print_r($column_info_pulang, true) . "</pre>";
                        
                        if ($column_info_pulang['Null'] == 'NO') {
                            echo "<div class='alert alert-warning'>";
                            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                            echo "Kolom foto_pulang juga memiliki constraint NOT NULL. Memperbaiki...";
                            echo "</div>";
                            
                            $alter_query_pulang = "ALTER TABLE presensi MODIFY COLUMN foto_pulang VARCHAR(255) DEFAULT NULL";
                            
                            if (mysqli_query($conn, $alter_query_pulang)) {
                                echo "<div class='alert alert-success'>";
                                echo "<i class='fas fa-check-circle me-2'></i>";
                                echo "Berhasil mengubah kolom foto_pulang menjadi DEFAULT NULL.";
                                echo "</div>";
                            } else {
                                echo "<div class='alert alert-danger'>";
                                echo "<i class='fas fa-times-circle me-2'></i>";
                                echo "Gagal mengubah kolom foto_pulang: " . mysqli_error($conn);
                                echo "</div>";
                            }
                        } else {
                            echo "<div class='alert alert-success'>";
                            echo "<i class='fas fa-check-circle me-2'></i>";
                            echo "Kolom foto_pulang sudah memiliki constraint yang benar (DEFAULT NULL).";
                            echo "</div>";
                        }
                        echo "</div>";
                    }
                    ?>
                    
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Kembali ke Dashboard
                        </a>
                        <a href="../karyawan/scan_barcode.php" class="btn btn-success">
                            <i class="fas fa-qrcode me-2"></i>
                            Test Scan Barcode
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
