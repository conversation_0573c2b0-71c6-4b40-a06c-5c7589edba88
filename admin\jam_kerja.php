<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses tambah jam kerja
if (isset($_POST['tambah'])) {
    $bidang_id = clean($_POST['bidang_id']);
    $nama_jam_kerja = clean($_POST['nama_jam_kerja']);
    $awal_jam_masuk = clean($_POST['awal_jam_masuk']);
    $jam_masuk = clean($_POST['jam_masuk']);
    $akhir_jam_masuk = clean($_POST['akhir_jam_masuk']);
    $jam_pulang = clean($_POST['jam_pulang']);
    $akhir_jam_pulang = clean($_POST['akhir_jam_pulang']);

    // Cek apakah nama jam kerja sudah terdaftar untuk bidang yang sama
    $query = "SELECT * FROM jam_kerja WHERE bidang_id = '$bidang_id' AND nama_jam_kerja = '$nama_jam_kerja'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Nama jam kerja sudah terdaftar untuk bidang tersebut!');
        redirect('admin/jam_kerja.php');
    }

    // Insert data jam kerja baru
    $query = "INSERT INTO jam_kerja (bidang_id, nama_jam_kerja, awal_jam_masuk, jam_masuk, akhir_jam_masuk, jam_pulang, akhir_jam_pulang)
              VALUES ('$bidang_id', '$nama_jam_kerja', '$awal_jam_masuk', '$jam_masuk', '$akhir_jam_masuk', '$jam_pulang', '$akhir_jam_pulang')";

    if (mysqli_query($conn, $query)) {
        // Dapatkan ID jam kerja yang baru saja dibuat
        $jam_kerja_id = mysqli_insert_id($conn);

        // Otomatis tambahkan ke jam_kerja_bidang untuk semua hari (default tidak aktif)
        $hari_list = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
        $success_count = 0;

        foreach ($hari_list as $hari) {
            // Cek apakah sudah ada entri untuk bidang dan hari ini
            $check_query = "SELECT id FROM jam_kerja_bidang WHERE bidang_id = '$bidang_id' AND hari = '$hari'";
            $check_result = mysqli_query($conn, $check_query);

            if (mysqli_num_rows($check_result) == 0) {
                // Jika belum ada, buat entri baru dengan jam_kerja_id = NULL (tidak aktif)
                $insert_bidang_query = "INSERT INTO jam_kerja_bidang (bidang_id, hari, jam_kerja_id)
                                       VALUES ('$bidang_id', '$hari', NULL)";
                if (mysqli_query($conn, $insert_bidang_query)) {
                    $success_count++;
                }
            } else {
                $success_count++; // Sudah ada, anggap berhasil
            }
        }

        if ($success_count == 7) {
            setMessage('success', 'Jam kerja berhasil ditambahkan! Jam kerja ini sekarang tersedia di pilihan "Pilih Jam Kerja" untuk karyawan. Anda juga dapat mengatur jadwal khusus di menu "Jam Kerja Bidang".');
        } else {
            setMessage('warning', 'Jam kerja berhasil ditambahkan, tetapi ada masalah saat membuat jadwal bidang (' . $success_count . '/7 hari berhasil). Silakan periksa menu "Jam Kerja Bidang".');
        }
    } else {
        setMessage('danger', 'Gagal menambahkan jam kerja: ' . mysqli_error($conn));
    }

    redirect('admin/jam_kerja.php');
}

// Proses edit jam kerja
if (isset($_POST['edit'])) {
    $id = clean($_POST['id']);
    $bidang_id = clean($_POST['bidang_id']);
    $nama_jam_kerja = clean($_POST['nama_jam_kerja']);
    $awal_jam_masuk = clean($_POST['awal_jam_masuk']);
    $jam_masuk = clean($_POST['jam_masuk']);
    $akhir_jam_masuk = clean($_POST['akhir_jam_masuk']);
    $jam_pulang = clean($_POST['jam_pulang']);
    $akhir_jam_pulang = clean($_POST['akhir_jam_pulang']);

    // Cek apakah nama jam kerja sudah terdaftar untuk bidang yang sama (selain jam kerja ini)
    $query = "SELECT * FROM jam_kerja WHERE bidang_id = '$bidang_id' AND nama_jam_kerja = '$nama_jam_kerja' AND id != '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Nama jam kerja sudah terdaftar untuk bidang tersebut!');
        redirect('admin/jam_kerja.php');
    }

    // Update data jam kerja
    $query = "UPDATE jam_kerja SET bidang_id = '$bidang_id', nama_jam_kerja = '$nama_jam_kerja',
              awal_jam_masuk = '$awal_jam_masuk', jam_masuk = '$jam_masuk', akhir_jam_masuk = '$akhir_jam_masuk',
              jam_pulang = '$jam_pulang', akhir_jam_pulang = '$akhir_jam_pulang'
              WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Data jam kerja berhasil diperbarui!');
        // Tambahkan parameter untuk menampilkan SweetAlert
        $_SESSION['show_sweet_alert'] = true;
    } else {
        setMessage('danger', 'Gagal memperbarui data jam kerja!');
    }

    redirect('admin/jam_kerja.php');
}

// Proses hapus jam kerja
if (isset($_GET['hapus'])) {
    $id = clean($_GET['hapus']);

    // Hapus jam kerja
    $query = "DELETE FROM jam_kerja WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Jam kerja berhasil dihapus!');
    } else {
        setMessage('danger', 'Gagal menghapus jam kerja!');
    }

    redirect('admin/jam_kerja.php');
}

// Cek apakah tabel bidang sudah ada
$query = "SHOW TABLES LIKE 'bidang'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('warning', 'Database perlu diperbarui. Silakan klik tombol Update Database.');
    redirect('admin/update_database.php');
}

// Cek apakah kolom bidang_id sudah ada di tabel jam_kerja
$query = "SHOW COLUMNS FROM jam_kerja LIKE 'bidang_id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('warning', 'Database perlu diperbarui. Silakan klik tombol Update Database.');
    redirect('admin/update_database.php');
}

// Ambil data jam kerja
try {
    $query = "SELECT jk.*, b.nama_bidang
              FROM jam_kerja jk
              JOIN bidang b ON jk.bidang_id = b.id
              ORDER BY b.nama_bidang ASC, jk.nama_jam_kerja ASC";
    $result = mysqli_query($conn, $query);

    $jam_kerja = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $jam_kerja[] = $row;
        }
    }
} catch (Exception $e) {
    setMessage('warning', 'Database perlu diperbarui. Silakan klik tombol Update Database.');
    redirect('admin/update_database.php');
}

// Ambil data bidang untuk dropdown
$query = "SELECT * FROM bidang ORDER BY nama_bidang ASC";
$result = mysqli_query($conn, $query);

$bidang = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $bidang[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<style>
/* CSS untuk form edit inline */
.bg-light {
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Memperbaiki tampilan form pada layar kecil */
@media (max-width: 768px) {
    .row > div {
        margin-bottom: 15px;
    }
}

/* Memperbaiki tampilan tabel */
.table td {
    vertical-align: middle;
}

/* Animasi transisi untuk form edit */
tr {
    transition: all 0.3s ease;
}

/* Highlight untuk baris yang sedang diedit */
.editing-row {
    background-color: #e8f4ff !important;
}
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Konfigurasi Jam Kerja</h1>
        <div>
            <a href="update_database.php" class="btn btn-warning me-2">
                <i class="fas fa-database"></i> Update Database
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#tambahJamKerjaModal">
                <i class="fas fa-plus"></i> Tambah Jam Kerja
            </button>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">Data Jam Kerja</h6>
            <div>
                <a href="bidang.php" class="btn btn-sm btn-info me-2">
                    <i class="fas fa-building"></i> Kelola Bidang
                </a>
                <a href="jam_kerja_bidang.php" class="btn btn-sm btn-success">
                    <i class="fas fa-calendar-alt"></i> Jam Kerja Bidang
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable">
                    <thead>
                        <tr>
                            <th>Bidang</th>
                            <th>Nama Jam Kerja</th>
                            <th>Awal Jam Masuk</th>
                            <th>Jam Masuk</th>
                            <th>Akhir Jam Masuk</th>
                            <th>Jam Pulang</th>
                            <th>Akhir Jam Pulang</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($jam_kerja)): ?>
                            <tr>
                                <td colspan="8" class="text-center">Tidak ada data jam kerja</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($jam_kerja as $jk): ?>
                                <!-- Baris tampilan data -->
                                <tr id="view_<?php echo $jk['id']; ?>">
                                    <td><?php echo $jk['nama_bidang']; ?></td>
                                    <td><?php echo $jk['nama_jam_kerja']; ?></td>
                                    <td><?php echo $jk['awal_jam_masuk']; ?></td>
                                    <td><?php echo $jk['jam_masuk']; ?></td>
                                    <td><?php echo $jk['akhir_jam_masuk'] ?? '-'; ?></td>
                                    <td><?php echo $jk['jam_pulang']; ?></td>
                                    <td><?php echo $jk['akhir_jam_pulang'] ?? '-'; ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="toggleEdit(<?php echo $jk['id']; ?>)">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <a href="jam_kerja.php?hapus=<?php echo $jk['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus jam kerja ini?')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </a>
                                    </td>
                                </tr>

                                <!-- Baris form edit -->
                                <tr id="edit_<?php echo $jk['id']; ?>" style="display: none;">
                                    <td colspan="8">
                                        <form method="post" action="" class="p-3 bg-light rounded">
                                            <input type="hidden" name="id" value="<?php echo $jk['id']; ?>">

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="bidang_id_<?php echo $jk['id']; ?>" class="form-label">Bidang</label>
                                                    <select class="form-select" id="bidang_id_<?php echo $jk['id']; ?>" name="bidang_id" required>
                                                        <?php foreach ($bidang as $b): ?>
                                                            <option value="<?php echo $b['id']; ?>" <?php echo ($jk['bidang_id'] == $b['id']) ? 'selected' : ''; ?>>
                                                                <?php echo $b['nama_bidang']; ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="nama_jam_kerja_<?php echo $jk['id']; ?>" class="form-label">Nama Jam Kerja</label>
                                                    <input type="text" class="form-control" id="nama_jam_kerja_<?php echo $jk['id']; ?>" name="nama_jam_kerja" value="<?php echo $jk['nama_jam_kerja']; ?>" required>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-4">
                                                    <label for="awal_jam_masuk_<?php echo $jk['id']; ?>" class="form-label">Awal Jam Masuk</label>
                                                    <input type="time" class="form-control" id="awal_jam_masuk_<?php echo $jk['id']; ?>" name="awal_jam_masuk" value="<?php echo $jk['awal_jam_masuk']; ?>" required>
                                                    <small class="text-muted">Waktu mulai absensi masuk</small>
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="jam_masuk_<?php echo $jk['id']; ?>" class="form-label">Jam Masuk</label>
                                                    <input type="time" class="form-control" id="jam_masuk_<?php echo $jk['id']; ?>" name="jam_masuk" value="<?php echo $jk['jam_masuk']; ?>" required>
                                                    <small class="text-muted">Batas waktu masuk (lebih dari ini dianggap terlambat)</small>
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="akhir_jam_masuk_<?php echo $jk['id']; ?>" class="form-label">Akhir Jam Masuk</label>
                                                    <input type="time" class="form-control" id="akhir_jam_masuk_<?php echo $jk['id']; ?>" name="akhir_jam_masuk" value="<?php echo $jk['akhir_jam_masuk'] ?? ''; ?>" required>
                                                    <small class="text-muted">Batas akhir absen masuk</small>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="jam_pulang_<?php echo $jk['id']; ?>" class="form-label">Jam Pulang</label>
                                                    <input type="time" class="form-control" id="jam_pulang_<?php echo $jk['id']; ?>" name="jam_pulang" value="<?php echo $jk['jam_pulang']; ?>" required>
                                                    <small class="text-muted">Waktu pulang (kurang dari ini dianggap pulang awal)</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="akhir_jam_pulang_<?php echo $jk['id']; ?>" class="form-label">Akhir Jam Pulang</label>
                                                    <input type="time" class="form-control" id="akhir_jam_pulang_<?php echo $jk['id']; ?>" name="akhir_jam_pulang" value="<?php echo $jk['akhir_jam_pulang'] ?? ''; ?>" required>
                                                    <small class="text-muted">Batas akhir absen pulang</small>
                                                </div>
                                            </div>

                                            <div class="text-end">
                                                <button type="button" class="btn btn-secondary" onclick="toggleEdit(<?php echo $jk['id']; ?>)">
                                                    <i class="fas fa-times"></i> Batal
                                                </button>
                                                <button type="submit" name="edit" class="btn btn-primary">
                                                    <i class="fas fa-save"></i> Simpan
                                                </button>
                                            </div>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Jam Kerja -->
<div class="modal fade" id="tambahJamKerjaModal" tabindex="-1" aria-labelledby="tambahJamKerjaModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tambahJamKerjaModalLabel">Tambah Jam Kerja</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bidang_id" class="form-label">Bidang</label>
                        <select class="form-select" id="bidang_id" name="bidang_id" required>
                            <option value="">Pilih Bidang</option>
                            <?php foreach ($bidang as $b): ?>
                                <option value="<?php echo $b['id']; ?>"><?php echo $b['nama_bidang']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="nama_jam_kerja" class="form-label">Nama Jam Kerja</label>
                        <input type="text" class="form-control" id="nama_jam_kerja" name="nama_jam_kerja" required>
                    </div>

                    <div class="mb-3">
                        <label for="awal_jam_masuk" class="form-label">Awal Jam Masuk</label>
                        <input type="time" class="form-control" id="awal_jam_masuk" name="awal_jam_masuk" required>
                        <small class="text-muted">Waktu mulai absensi masuk</small>
                    </div>

                    <div class="mb-3">
                        <label for="jam_masuk" class="form-label">Jam Masuk</label>
                        <input type="time" class="form-control" id="jam_masuk" name="jam_masuk" required>
                        <small class="text-muted">Batas waktu masuk (lebih dari ini dianggap terlambat)</small>
                    </div>

                    <div class="mb-3">
                        <label for="akhir_jam_masuk" class="form-label">Akhir Jam Masuk</label>
                        <input type="time" class="form-control" id="akhir_jam_masuk" name="akhir_jam_masuk" required>
                        <small class="text-muted">Batas akhir absen masuk (setelah ini tidak bisa absen masuk)</small>
                    </div>

                    <div class="mb-3">
                        <label for="jam_pulang" class="form-label">Jam Pulang</label>
                        <input type="time" class="form-control" id="jam_pulang" name="jam_pulang" required>
                        <small class="text-muted">Waktu pulang (kurang dari ini dianggap pulang awal)</small>
                    </div>

                    <div class="mb-3">
                        <label for="akhir_jam_pulang" class="form-label">Akhir Jam Pulang</label>
                        <input type="time" class="form-control" id="akhir_jam_pulang" name="akhir_jam_pulang" required>
                        <small class="text-muted">Batas akhir absen pulang (setelah ini tidak bisa absen pulang)</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="tambah" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Fungsi untuk menampilkan/menyembunyikan form edit
function toggleEdit(id) {
    // Tampilkan/sembunyikan baris tampilan data
    const viewRow = document.getElementById('view_' + id);
    const editRow = document.getElementById('edit_' + id);

    // Sembunyikan semua form edit yang terbuka
    const allEditRows = document.querySelectorAll('[id^="edit_"]');
    const allViewRows = document.querySelectorAll('[id^="view_"]');

    allEditRows.forEach(row => {
        if (row.id !== 'edit_' + id) {
            row.style.display = 'none';
        }
    });

    allViewRows.forEach(row => {
        if (row.id !== 'view_' + id) {
            row.style.display = '';
            row.classList.remove('editing-row');
        }
    });

    // Toggle tampilan baris yang diklik
    if (editRow.style.display === 'none') {
        viewRow.classList.add('editing-row');
        editRow.style.display = '';
    } else {
        viewRow.classList.remove('editing-row');
        viewRow.style.display = '';
        editRow.style.display = 'none';
    }
}

// Tampilkan SweetAlert jika ada pesan sukses
document.addEventListener('DOMContentLoaded', function() {
    <?php if (isset($_SESSION['message']) && isset($_SESSION['show_sweet_alert']) && $_SESSION['show_sweet_alert']): ?>
        Swal.fire({
            icon: '<?php echo ($_SESSION['message']['type'] == 'success') ? 'success' : 'error'; ?>',
            title: '<?php echo ($_SESSION['message']['type'] == 'success') ? 'Berhasil!' : 'Gagal!'; ?>',
            text: '<?php echo $_SESSION['message']['text']; ?>',
            timer: 2000,
            showConfirmButton: false
        });
        <?php unset($_SESSION['show_sweet_alert']); ?>
    <?php endif; ?>
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
