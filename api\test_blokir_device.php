<?php
/**
 * File untuk menguji API blokir_device.php dan check_device.php
 * File ini berisi contoh penggunaan API blokir_device.php dan check_device.php
 */

// Fungsi untuk mengirim request ke API
function sendRequest($url, $data) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return [
        'code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

// URL API
$api_blokir_url = 'http://localhost/absensiku/api/blokir_device.php';
$api_check_url = 'http://localhost/absensiku/api/check_device.php';

// API key
$api_key = 'absensiku_api_key_2023';

// Contoh data NIK karyawan (ganti dengan NIK yang valid di database)
$nik = '123456789'; // Ganti dengan NIK yang valid

// Contoh device ID
$device_id = 'test_device_' . time();

// Contoh 1: Memblokir device
$blokir_data = [
    'api_key' => $api_key,
    'nik' => $nik,
    'device_id' => $device_id,
    'alasan' => 'Device untuk pengujian'
];

// Contoh 2: Memeriksa status device
$check_data = [
    'api_key' => $api_key,
    'device_id' => $device_id,
    'nik' => $nik
];

// Contoh 3: Memblokir beberapa device sekaligus
$batch_data = [
    'api_key' => $api_key,
    'data' => [
        [
            'nik' => $nik,
            'device_id' => 'test_device_batch_1_' . time(),
            'alasan' => 'Device batch 1 untuk pengujian'
        ],
        [
            'nik' => $nik,
            'device_id' => 'test_device_batch_2_' . time(),
            'alasan' => 'Device batch 2 untuk pengujian'
        ]
    ]
];

// Pilih contoh yang ingin dijalankan
$test_case = isset($_GET['test']) ? $_GET['test'] : 'blokir';

// Jalankan test case yang dipilih
if ($test_case === 'blokir') {
    $result = sendRequest($api_blokir_url, $blokir_data);
    $title = 'Test Case: Memblokir Device';
    $data_sent = $blokir_data;
} elseif ($test_case === 'check') {
    $result = sendRequest($api_check_url, $check_data);
    $title = 'Test Case: Memeriksa Status Device';
    $data_sent = $check_data;
} else {
    $result = sendRequest($api_blokir_url, $batch_data);
    $title = 'Test Case: Memblokir Beberapa Device Sekaligus';
    $data_sent = $batch_data;
}

// Tampilkan hasil
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Blokir Device</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .json-key {
            color: #d63384;
        }
        .json-string {
            color: #20c997;
        }
        .json-number {
            color: #0d6efd;
        }
        .json-boolean {
            color: #fd7e14;
        }
        .json-null {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Test API Blokir Device</h1>

        <div class="mb-3">
            <a href="?test=blokir" class="btn btn-primary me-2">Test Blokir Device</a>
            <a href="?test=check" class="btn btn-info me-2">Test Check Device</a>
            <a href="?test=batch" class="btn btn-success">Test Batch Blokir</a>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo $title; ?></h5>
            </div>
            <div class="card-body">
                <h6>Data yang Dikirim:</h6>
                <pre><?php echo json_encode($data_sent, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?></pre>

                <h6 class="mt-4">HTTP Status Code:</h6>
                <pre><?php echo $result['code']; ?></pre>

                <h6 class="mt-4">Response:</h6>
                <pre><?php echo json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?></pre>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Dokumentasi API</h5>
            </div>
            <div class="card-body">
                <h6>1. API Blokir Device:</h6>
                <pre>{
    "api_key": "absensiku_api_key_2023",
    "nik": "123456789",
    "device_id": "abc123xyz456",
    "alasan": "Device tidak dikenali"
}</pre>

                <h6 class="mt-4">2. API Check Device:</h6>
                <pre>{
    "api_key": "absensiku_api_key_2023",
    "device_id": "abc123xyz456",
    "nik": "123456789" // opsional
}</pre>

                <h6 class="mt-4">3. API Blokir Device (Batch):</h6>
                <pre>{
    "api_key": "absensiku_api_key_2023",
    "data": [
        {
            "nik": "123456789",
            "device_id": "abc123xyz456",
            "alasan": "Device tidak dikenali"
        },
        {
            "nik": "987654321",
            "device_id": "def456uvw789",
            "alasan": "Device mencurigakan"
        }
    ]
}</pre>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
