<?php
/**
 * Setup Script
 * File ini digunakan untuk mengatur database dan membuat admin default
 */

// Cek apakah file diakses langsung
if (basename($_SERVER['PHP_SELF']) == 'setup.php') {
    // Koneksi ke MySQL
    $host = 'localhost';
    $user = 'root';
    $pass = '';
    
    // Buat koneksi
    $conn = mysqli_connect($host, $user, $pass);
    
    // Cek koneksi
    if (!$conn) {
        die("Koneksi ke MySQL gagal: " . mysqli_connect_error());
    }
    
    // Baca file SQL
    $sql = file_get_contents('database.sql');
    
    // Eksekusi query
    if (mysqli_multi_query($conn, $sql)) {
        echo "<h2>Setup berhasil!</h2>";
        echo "<p>Database dan tabel berhasil dibuat.</p>";
        echo "<p>Admin default:</p>";
        echo "<ul>";
        echo "<li>NIK: admin</li>";
        echo "<li>Password: password</li>";
        echo "</ul>";
        echo "<p><a href='index.php'>Klik di sini untuk login</a></p>";
    } else {
        echo "<h2>Setup gagal!</h2>";
        echo "<p>Error: " . mysqli_error($conn) . "</p>";
    }
    
    // Tutup koneksi
    mysqli_close($conn);
}
?>
