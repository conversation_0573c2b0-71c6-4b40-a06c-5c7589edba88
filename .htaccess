# Disable directory browsing
Options -Indexes

# Protect the htaccess file
<Files .htaccess>
    Order Allow,<PERSON>y
    Den<PERSON> from all
</Files>

# Protect config files
<FilesMatch "^(database\.php|config\.php)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Disable server signature
ServerSignature Off

# Set default character set
AddDefaultCharset UTF-8

# Protect against XSS attacks
<IfModule mod_headers.c>
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
</IfModule>

# Enable rewriting
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Sesuaikan jika aplikasi berada di subfolder, misal: /absensiku/
    RewriteBase /

    # Redirect ke HTTPS (aktifkan jika pakai HTTPS)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Redirect ke index.php jika mengakses root
    RewriteCond %{REQUEST_URI} ^/?$
    RewriteRule ^$ index.php [L]

    # Cegah akses langsung ke folder sensitif
    RewriteRule ^includes/ - [F,L]
    RewriteRule ^config/ - [F,L]

    # Redirect /admin dan /admin/ ke admin/login.php
    RewriteRule ^admin$ admin/login.php [L]
    RewriteRule ^admin/$ admin/login.php [L]

    # Error Pages
    ErrorDocument 404 /404.php
    ErrorDocument 403 /403.php
    ErrorDocument 500 /500.php
</IfModule>

# PHP settings
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 128M
    php_value session.gc_maxlifetime 1440

    # Nonaktifkan error display di production
    # php_flag display_errors off
    # php_value error_reporting 0
</IfModule>
