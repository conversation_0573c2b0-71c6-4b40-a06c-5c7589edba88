/**
 * SweetAlert Helper Functions
 * File ini berisi fungsi-fungsi helper untuk SweetAlert2
 */

// Fungsi untuk menampilkan peringatan lokasi
function showLocationAlert(type) {
    var title = 'Lokasi Di Luar Radius';
    var text = 'Anda tidak dapat melakukan absensi ' + type + ' karena berada di luar radius lokasi yang ditentukan. Jarak Anda saat ini: ' + 
              Math.round(userLocation.distance) + ' meter, sedangkan radius yang diizinkan: ' + officeLocation.radius + ' meter.';
    
    // Periksa apakah SweetAlert2 tersedia
    if (typeof Swal !== 'undefined') {
        // Gunakan SweetAlert2
        Swal.fire({
            icon: 'error',
            title: title,
            text: text,
            footer: '<a href="#" onclick="showLeafletMap(); return false;">Lihat lokasi Anda di peta</a>',
            confirmButtonColor: '#d33',
            confirmButtonText: '<PERSON><PERSON>'
        });
    } else {
        // Fallback ke alert biasa jika SweetAlert2 tidak tersedia
        console.error('SweetAlert2 tidak tersedia. Menggunakan alert biasa.');
        alert(title + '\n\n' + text + '\n\nKlik OK untuk melihat lokasi Anda di peta.');
        showLeafletMap();
    }
}

// Fungsi untuk menampilkan konfirmasi absensi
function confirmAbsensi(type, formId) {
    if (typeof Swal !== 'undefined') {
        var title = type === 'masuk' ? 'Konfirmasi Absensi Masuk' : 'Konfirmasi Absensi Pulang';
        var text = type === 'masuk' ? 
            'Anda akan melakukan absensi masuk. Pastikan Anda berada di lokasi yang benar.' : 
            'Anda akan melakukan absensi pulang. Pastikan Anda berada di lokasi yang benar.';
        
        Swal.fire({
            icon: 'question',
            title: title,
            text: text,
            showCancelButton: true,
            confirmButtonColor: '#1cc88a',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, Lakukan Absensi',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                document.getElementById(formId).submit();
            }
        });
        
        return false;
    } else {
        return confirm(type === 'masuk' ? 
            'Anda akan melakukan absensi masuk. Lanjutkan?' : 
            'Anda akan melakukan absensi pulang. Lanjutkan?');
    }
}

// Fungsi untuk menampilkan pesan sukses
function showSuccessMessage(message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'success',
            title: 'Berhasil',
            text: message,
            confirmButtonColor: '#1cc88a'
        });
    } else {
        alert('Berhasil: ' + message);
    }
}

// Fungsi untuk menampilkan pesan error
function showErrorMessage(message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: message,
            confirmButtonColor: '#d33'
        });
    } else {
        alert('Error: ' + message);
    }
}

// Fungsi untuk menampilkan pesan warning
function showWarningMessage(message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'warning',
            title: 'Perhatian',
            text: message,
            confirmButtonColor: '#f6c23e'
        });
    } else {
        alert('Perhatian: ' + message);
    }
}

// Fungsi untuk menampilkan pesan info
function showInfoMessage(message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'info',
            title: 'Informasi',
            text: message,
            confirmButtonColor: '#36b9cc'
        });
    } else {
        alert('Informasi: ' + message);
    }
}

// Fungsi untuk menampilkan pesan dari PHP
function showPhpMessage(type, message) {
    switch(type) {
        case 'success':
            showSuccessMessage(message);
            break;
        case 'danger':
            showErrorMessage(message);
            break;
        case 'warning':
            showWarningMessage(message);
            break;
        default:
            showInfoMessage(message);
            break;
    }
}

// Fungsi untuk menguji SweetAlert2
function testSweetAlert() {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'SweetAlert2 Berfungsi!',
            text: 'Ini adalah pesan uji untuk memastikan SweetAlert2 berfungsi dengan baik.',
            icon: 'success',
            confirmButtonText: 'OK'
        });
        return true;
    } else {
        alert('SweetAlert2 tidak tersedia. Peringatan akan menggunakan alert biasa.');
        return false;
    }
}
