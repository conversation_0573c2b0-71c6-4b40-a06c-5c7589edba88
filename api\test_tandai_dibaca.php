<?php
/**
 * Test file untuk API tandai_pesan_dibaca.php
 */

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/pesan_functions.php';

// Start session jika belum dimulai
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set header untuk JSON response
header('Content-Type: application/json');

echo json_encode([
    'test' => 'API Test',
    'session_status' => session_status(),
    'session_data' => [
        'user_id' => $_SESSION['user_id'] ?? 'not set',
        'role' => $_SESSION['role'] ?? 'not set',
        'nama' => $_SESSION['nama'] ?? 'not set'
    ],
    'functions_available' => [
        'tandaiPesanDibaca' => function_exists('tandaiPesanDibaca'),
        'hitungPesanAktif' => function_exists('hitungPesanAktif'),
        'getPesanPeringatanAktif' => function_exists('getPesanPeringatanAktif')
    ],
    'database_connection' => $conn ? 'connected' : 'failed',
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'not set',
    'raw_input' => file_get_contents('php://input'),
    'post_data' => $_POST,
    'get_data' => $_GET
]);
?>
