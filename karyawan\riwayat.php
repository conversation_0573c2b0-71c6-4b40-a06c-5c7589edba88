<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('karyawan');

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Filter bulan dan tahun
$bulan = isset($_GET['bulan']) ? clean($_GET['bulan']) : date('m');
$tahun = isset($_GET['tahun']) ? clean($_GET['tahun']) : date('Y');

// Ambil data presensi
$query = "SELECT * FROM presensi
          WHERE user_id = '$user_id'
          AND MONTH(tanggal) = '$bulan'
          AND YEAR(tanggal) = '$tahun'
          ORDER BY tanggal DESC";
$result = mysqli_query($conn, $query);

$presensi = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $presensi[] = $row;
    }
}

// Ambil data denda
$denda = getDenda();

// Hitung jumlah hari dalam bulan
$jumlah_hari = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);

// Ambil data hari libur dalam bulan ini
$query = "SELECT tanggal FROM hari_libur WHERE MONTH(tanggal) = '$bulan' AND YEAR(tanggal) = '$tahun'";
$result = mysqli_query($conn, $query);

$hari_libur = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $hari_libur[] = $row['tanggal'];
    }
}

// Hitung jumlah hari kerja (tidak termasuk Sabtu, Minggu, dan hari libur)
$hari_kerja = 0;
for ($i = 1; $i <= $jumlah_hari; $i++) {
    $tanggal = sprintf('%04d-%02d-%02d', $tahun, $bulan, $i);
    $hari = date('N', strtotime($tanggal)); // 1 (Senin) sampai 7 (Minggu)

    // Cek apakah hari kerja (Senin-Jumat) dan bukan hari libur
    if ($hari >= 1 && $hari <= 5 && !in_array($tanggal, $hari_libur)) {
        $hari_kerja++;
    }
}

// Hitung statistik
$jumlah_kehadiran = count($presensi);
$jumlah_terlambat = 0;
$jumlah_pulang_awal = 0;
$jumlah_tidak_absen_pulang = 0;

foreach ($presensi as $p) {
    if ($p['status'] == 'Terlambat') {
        $jumlah_terlambat++;
    } elseif ($p['status'] == 'Pulang Awal') {
        $jumlah_pulang_awal++;
    }

    // Hitung jumlah tidak absen pulang (absen masuk ada tapi absen pulang tidak ada)
    if (!empty($p['jam_masuk']) && empty($p['jam_pulang'])) {
        $jumlah_tidak_absen_pulang++;
    }
}

// Hitung jumlah hari tidak hadir (hanya untuk hari yang sudah lewat)
$jumlah_tidak_hadir = 0;
$today = date('Y-m-d');
$current_time = date('H:i:s');

// Ambil jam pulang default
$jam_kerja_default = getJamKerjaByUserId($user_id);
$jam_pulang_default = $jam_kerja_default ? $jam_kerja_default['jam_pulang'] : '17:00:00';

// Cek apakah sudah lewat jam pulang hari ini
$lewat_jam_pulang = $current_time > $jam_pulang_default;

for ($i = 1; $i <= $jumlah_hari; $i++) {
    $tanggal = sprintf('%04d-%02d-%02d', $tahun, $bulan, $i);
    $hari = date('N', strtotime($tanggal)); // 1 (Senin) sampai 7 (Minggu)

    // Hanya hitung untuk hari kerja (Senin-Jumat) dan bukan hari libur
    if ($hari >= 1 && $hari <= 5 && !in_array($tanggal, $hari_libur)) {
        // Hanya hitung untuk tanggal yang sudah lewat
        // atau hari ini jika sudah lewat jam pulang
        if ($tanggal < $today || ($tanggal == $today && $lewat_jam_pulang)) {
            // Cek apakah ada presensi pada tanggal tersebut
            $ada_presensi = false;
            foreach ($presensi as $p) {
                if ($p['tanggal'] == $tanggal) {
                    $ada_presensi = true;
                    break;
                }
            }

            // Jika tidak ada presensi, tambahkan ke jumlah tidak hadir
            if (!$ada_presensi) {
                $jumlah_tidak_hadir++;
            }
        }
    }
}

// Hitung total denda
$total_denda_terlambat = 0;
$total_denda_pulang_awal = 0;
$total_denda_tidak_hadir = 0;
$total_denda_tidak_absen_pulang = 0;

// Hitung denda keterlambatan dan pulang awal hanya untuk data yang ada
foreach ($presensi as $p) {
    if ($p['status'] == 'Terlambat') {
        $total_denda_terlambat += $denda['denda_masuk'];
    } elseif ($p['status'] == 'Pulang Awal') {
        $total_denda_pulang_awal += $denda['denda_pulang'];
    }

    // Hitung denda tidak absen pulang
    if (!empty($p['jam_masuk']) && empty($p['jam_pulang'])) {
        $total_denda_tidak_absen_pulang += $denda['denda_tidak_absen_pulang'];
    }
}

// Hitung denda tidak hadir berdasarkan jumlah hari tidak hadir yang sudah dihitung
$total_denda_tidak_hadir = $jumlah_tidak_hadir * $denda['denda_tidak_absen'];

$total_denda = $total_denda_terlambat + $total_denda_pulang_awal + $total_denda_tidak_absen_pulang + $total_denda_tidak_hadir;

// Include header
include_once '../includes/header.php';
?>

<div class="mobile-riwayat-container">
    <!-- Header Section -->
    <div class="riwayat-header">
        <div class="user-info">
            <div class="user-avatar">
                                <?php if (!empty($karyawan['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $karyawan['foto_profil']; ?>" alt="Foto Profil" style="width: 100px; height: 100px; ">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                <div class="user-position"><?php echo $karyawan['bidang'] ?? 'Karyawan'; ?></div>
            </div>
        </div>
        <div class="date-info">
            <i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?>
        </div>
    </div>

    <!-- Container -->
    <div class="container px-0">
        <!-- Filter Section -->
        <div class="filter-section">
            <div class="filter-title">Filter Riwayat Presensi</div>
            <form method="get" class="filter-form">
                <select class="form-select" name="bulan">
                    <?php for ($i = 1; $i <= 12; $i++): ?>
                        <option value="<?php echo sprintf('%02d', $i); ?>" <?php echo ($bulan == sprintf('%02d', $i)) ? 'selected' : ''; ?>>
                            <?php echo date('F', strtotime('2023-' . sprintf('%02d', $i) . '-01')); ?>
                        </option>
                    <?php endfor; ?>
                </select>
                <select class="form-select" name="tahun">
                    <?php for ($i = date('Y') - 5; $i <= date('Y'); $i++): ?>
                        <option value="<?php echo $i; ?>" <?php echo ($tahun == $i) ? 'selected' : ''; ?>>
                            <?php echo $i; ?>
                        </option>
                    <?php endfor; ?>
                </select>
                <button type="submit" class="btn">Filter</button>
            </form>
        </div>

        <!-- Stats Cards -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-title">Hari Kerja</div>
                <div class="stat-content">
                    <div class="stat-value"><?php echo $hari_kerja; ?> hari</div>
                    <div class="stat-icon primary">
                        <i class="fas fa-calendar"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-title">Kehadiran</div>
                <div class="stat-content">
                    <div class="stat-value"><?php echo $jumlah_kehadiran; ?> hari</div>
                    <div class="stat-icon success">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-title">Keterlambatan</div>
                <div class="stat-content">
                    <div class="stat-value"><?php echo $jumlah_terlambat; ?> kali</div>
                    <div class="stat-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-title">Total Denda</div>
                <div class="stat-content">
                    <div class="stat-value">Rp <?php echo number_format($total_denda, 0, ',', '.'); ?></div>
                    <div class="stat-icon danger">
                        <i class="fas fa-money-bill-alt"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Presensi List -->
        <h6 class="mb-3">Riwayat Presensi - <?php echo date('F Y', strtotime($tahun . '-' . $bulan . '-01')); ?></h6>
        <div class="presensi-list">
            <?php if (empty($presensi)): ?>
                <div class="presensi-card">
                    <div class="text-center py-3">Tidak ada data presensi</div>
                </div>
            <?php else: ?>
                <?php foreach ($presensi as $p): ?>
                    <div class="presensi-card">
                        <div class="presensi-date">
                            <span><i class="fas fa-calendar-day"></i> <?php echo date('d F Y', strtotime($p['tanggal'])); ?></span>
                            <?php if ($p['status'] == 'Tepat Waktu'): ?>
                                <span class="status-badge success">Tepat Waktu</span>
                            <?php elseif ($p['status'] == 'Terlambat'): ?>
                                <span class="status-badge warning">Terlambat</span>
                            <?php elseif ($p['status'] == 'Pulang Awal'): ?>
                                <span class="status-badge warning">Pulang Awal</span>
                            <?php elseif ($p['status'] == 'Lembur'): ?>
                                <span class="status-badge info">Lembur</span>
                            <?php else: ?>
                                <span class="status-badge"><?php echo $p['status']; ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="presensi-info">
                            <div class="info-item">
                                <div class="info-label">Jam Masuk</div>
                                <div class="info-value"><?php echo $p['jam_masuk']; ?></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Jam Pulang</div>
                                <div class="info-value"><?php echo $p['jam_pulang'] ?? '-'; ?></div>
                            </div>
                        </div>
                        <?php if (!empty($p['keterangan'])): ?>
                            <div class="info-item mt-2">
                                <div class="info-label">Keterangan</div>
                                <div class="info-value"><?php echo $p['keterangan']; ?></div>
                            </div>
                        <?php endif; ?>
                        <div class="presensi-actions">
                            <a href="detail_presensi.php?id=<?php echo $p['id']; ?>" class="btn-detail">
                                <i class="fas fa-eye"></i> Lihat Detail
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Denda Section -->
        <div class="denda-section">
            <div class="denda-title">Rincian Denda</div>
            <div class="denda-info">
                <i class="fas fa-info-circle"></i> Denda dihitung berdasarkan:
                <ul>
                    <li>Keterlambatan: dihitung saat Anda terlambat masuk</li>
                    <li>Pulang Awal: dihitung saat Anda pulang sebelum waktunya</li>
                    <li>Tidak Absen Pulang: dihitung saat Anda melakukan absen masuk tetapi tidak melakukan absen pulang</li>
                    <li>Tidak Hadir: dihitung untuk hari kerja yang sudah lewat dan Anda tidak melakukan absensi</li>
                </ul>
            </div>
            <div class="denda-item">
                <div class="denda-label">Keterlambatan (<?php echo $jumlah_terlambat; ?> kali)</div>
                <div class="denda-value">Rp <?php echo number_format($total_denda_terlambat, 0, ',', '.'); ?></div>
            </div>
            <div class="denda-item">
                <div class="denda-label">Pulang Awal (<?php echo $jumlah_pulang_awal; ?> kali)</div>
                <div class="denda-value">Rp <?php echo number_format($total_denda_pulang_awal, 0, ',', '.'); ?></div>
            </div>
            <div class="denda-item">
                <div class="denda-label">Tidak Absen Pulang (<?php echo $jumlah_tidak_absen_pulang; ?> kali)</div>
                <div class="denda-value">Rp <?php echo number_format($total_denda_tidak_absen_pulang, 0, ',', '.'); ?></div>
            </div>
            <div class="denda-item">
                <div class="denda-label">Tidak Hadir (<?php echo $jumlah_tidak_hadir; ?> hari)</div>
                <div class="denda-value">Rp <?php echo number_format($total_denda_tidak_hadir, 0, ',', '.'); ?></div>
            </div>
            <div class="denda-total">
                <div class="denda-label">Total Denda</div>
                <div class="denda-value">Rp <?php echo number_format($total_denda, 0, ',', '.'); ?></div>
            </div>
        </div>

        <!-- Additional Quick Actions -->
        <h6 class="mb-3">Menu Tambahan</h6>
        <div class="quick-actions">
            <a href="<?php echo BASE_URL; ?>help.php" class="action-button">
                <div class="action-icon primary">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="action-text">Bantuan</div>
            </a>
            <a href="<?php echo BASE_URL; ?>change_password.php" class="action-button">
                <div class="action-icon success">
                    <i class="fas fa-key"></i>
                </div>
                <div class="action-text">Ubah Password</div>
            </a>
            <a href="#" class="action-button" onclick="window.location.reload()">
                <div class="action-icon warning">
                    <i class="fas fa-sync-alt"></i>
                </div>
                <div class="action-text">Refresh</div>
            </a>
            <a href="<?php echo BASE_URL; ?>logout.php" class="action-button">
                <div class="action-icon danger">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <div class="action-text">Logout</div>
            </a>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
