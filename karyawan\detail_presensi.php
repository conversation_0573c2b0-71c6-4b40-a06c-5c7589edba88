<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('karyawan');

// Ambil ID presensi
$id = isset($_GET['id']) ? clean($_GET['id']) : 0;

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Ambil data presensi
$query = "SELECT * FROM presensi WHERE id = '$id' AND user_id = '$user_id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Data presensi tidak ditemukan!');
    redirect('karyawan/riwayat.php');
}

$presensi = mysqli_fetch_assoc($result);

// Gunakan header tanpa sidebar
$no_sidebar = true;
include_once '../includes/header.php';
?>

<!-- Mobile CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/mobile-detail-presensi.css">

<div class="mobile-detail-container">
    <!-- Header Section -->
    <div class="detail-header">
        <div class="user-info">
            <div class="user-avatar">
                <?php if (!empty($karyawan['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $karyawan['foto_profil']; ?>" alt="Foto Profil" class="user-photo">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                <div class="user-position"><?php echo $karyawan['bidang'] ?? 'Karyawan'; ?></div>
            </div>
        </div>
        <div class="date-info">
            <i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?>
        </div>
    </div>

    <!-- Content -->
    <div class="detail-content">
        <div class="detail-title">
            <h6>Detail Presensi</h6>
            <a href="riwayat.php" class="back-button">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>

        <!-- Informasi Presensi -->
        <div class="detail-card">
            <div class="detail-card-header">
                Informasi Presensi
            </div>
            <div class="detail-card-body">
                <table class="detail-table">
                    <tr>
                        <th>Tanggal</th>
                        <td><?php echo date('d-m-Y', strtotime($presensi['tanggal'])); ?></td>
                    </tr>
                    <tr>
                        <th>Jam Masuk</th>
                        <td><?php echo $presensi['jam_masuk']; ?></td>
                    </tr>
                    <tr>
                        <th>Lokasi Masuk</th>
                        <td><?php echo $presensi['lokasi_masuk']; ?></td>
                    </tr>
                    <tr>
                        <th>Jam Pulang</th>
                        <td><?php echo $presensi['jam_pulang'] ?? '-'; ?></td>
                    </tr>
                    <tr>
                        <th>Lokasi Pulang</th>
                        <td><?php echo $presensi['lokasi_pulang'] ?? '-'; ?></td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            <?php if ($presensi['status'] == 'Tepat Waktu'): ?>
                                <span class="status-badge success">Tepat Waktu</span>
                            <?php elseif ($presensi['status'] == 'Terlambat'): ?>
                                <span class="status-badge warning">Terlambat</span>
                            <?php elseif ($presensi['status'] == 'Pulang Awal'): ?>
                                <span class="status-badge warning">Pulang Awal</span>
                            <?php elseif ($presensi['status'] == 'Lembur'): ?>
                                <span class="status-badge info">Lembur</span>
                            <?php else: ?>
                                <span class="status-badge"><?php echo $presensi['status']; ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Keterangan</th>
                        <td><?php echo $presensi['keterangan'] ?? '-'; ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Foto Masuk -->
        <div class="detail-card">
            <div class="detail-card-header">
                Foto Masuk
            </div>
            <div class="detail-card-body">
                <?php if (!empty($presensi['foto_masuk'])): ?>
                    <div class="detail-image">
                        <img src="<?php echo BASE_URL . 'uploads/' . $presensi['foto_masuk']; ?>" alt="Foto Masuk">
                    </div>
                <?php else: ?>
                    <div class="no-image-alert">
                        <i class="fas fa-image me-2"></i> Tidak ada foto masuk
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Foto Pulang -->
        <div class="detail-card">
            <div class="detail-card-header">
                Foto Pulang
            </div>
            <div class="detail-card-body">
                <?php if (!empty($presensi['foto_pulang'])): ?>
                    <div class="detail-image">
                        <img src="<?php echo BASE_URL . 'uploads/' . $presensi['foto_pulang']; ?>" alt="Foto Pulang">
                    </div>
                <?php else: ?>
                    <div class="no-image-alert">
                        <i class="fas fa-image me-2"></i> Tidak ada foto pulang
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Bottom Navigation -->
<?php include_once '../includes/bottom-nav.php'; ?>

<?php
// Include footer
include_once '../includes/footer.php';
?>
