-- <PERSON><PERSON> kolom untuk izin jam kerja fleksibel
ALTER TABLE users ADD COLUMN allow_flexible_schedule TINYINT(1) DEFAULT 0 AFTER allow_face;

-- Tabel untuk menyimpan pilihan jam kerja karyawan per hari
CREATE TABLE IF NOT EXISTS user_schedule_choices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    tanggal DATE NOT NULL,
    jam_kerja_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (jam_kerja_id) REFERENCES jam_kerja(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date (user_id, tanggal)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
