<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Filter tanggal
$tanggal = isset($_GET['tanggal']) ? $_GET['tanggal'] : date('Y-m-d');

// Query untuk data presensi
$query = "SELECT p.*, u.nik, u.nama, u.bidang, u.jabatan
          FROM presensi p
          JOIN users u ON p.user_id = u.id
          WHERE p.tanggal = '$tanggal'
          ORDER BY p.jam_masuk ASC";
$result = mysqli_query($conn, $query);

$presensi = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $presensi[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<style>
.photo-thumbnail {
    position: relative;
    display: inline-block;
}

.photo-preview {
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.photo-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.photo-thumbnail::after {
    content: '\f065'; /* Font Awesome expand icon */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.photo-thumbnail:hover::after {
    opacity: 1;
}

.table td {
    vertical-align: middle;
}
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Monitoring Presensi</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">Data Presensi</h6>
            <div class="d-flex">
                <form method="get" class="form-inline me-2">
                    <div class="input-group">
                        <input type="date" class="form-control" name="tanggal" value="<?php echo $tanggal; ?>">
                        <button type="submit" class="btn btn-primary">Filter</button>
                    </div>
                </form>
                <a href="absensi_manual.php?tanggal=<?php echo $tanggal; ?>" class="btn btn-success">
                    <i class="fas fa-user-clock"></i> Absensi Manual
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Bidang</th>
                            <th>Jam Masuk</th>
                            <th>Foto Masuk</th>
                            <th>Lokasi Masuk</th>
                            <th>Jam Pulang</th>
                            <th>Foto Pulang</th>
                            <th>Lokasi Pulang</th>
                            <th>Status</th>
                            <th>Keterangan</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($presensi)): ?>
                            <tr>
                                <td colspan="12" class="text-center">Tidak ada data presensi</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($presensi as $p): ?>
                                <tr>
                                    <td><?php echo $p['nik']; ?></td>
                                    <td><?php echo $p['nama']; ?></td>
                                    <td><?php echo $p['bidang']; ?></td>
                                    <td><?php echo $p['jam_masuk']; ?></td>
                                    <td>
                                        <?php if (!empty($p['foto_masuk'])): ?>
                                            <div class="photo-thumbnail">
                                                <img src="<?php echo BASE_URL . 'uploads/' . $p['foto_masuk']; ?>"
                                                     alt="Foto Masuk"
                                                     class="img-thumbnail photo-preview"
                                                     style="width: 60px; height: 60px; object-fit: cover; cursor: pointer;"
                                                     onclick="showPhotoModal('<?php echo BASE_URL . 'uploads/' . $p['foto_masuk']; ?>', 'Foto Masuk - <?php echo $p['nama']; ?>')">
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $p['lokasi_masuk'] ?? '-'; ?></td>
                                    <td><?php echo $p['jam_pulang'] ?? '-'; ?></td>
                                    <td>
                                        <?php if (!empty($p['foto_pulang'])): ?>
                                            <div class="photo-thumbnail">
                                                <img src="<?php echo BASE_URL . 'uploads/' . $p['foto_pulang']; ?>"
                                                     alt="Foto Pulang"
                                                     class="img-thumbnail photo-preview"
                                                     style="width: 60px; height: 60px; object-fit: cover; cursor: pointer;"
                                                     onclick="showPhotoModal('<?php echo BASE_URL . 'uploads/' . $p['foto_pulang']; ?>', 'Foto Pulang - <?php echo $p['nama']; ?>')">
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($p['lokasi_pulang'] == 'Perjalanan Dinas'): ?>
                                            <span class="badge bg-primary">Perjalanan Dinas</span>
                                        <?php else: ?>
                                            <?php echo $p['lokasi_pulang'] ?? '-'; ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($p['status'] == 'Tepat Waktu'): ?>
                                            <span class="badge bg-success">Tepat Waktu</span>
                                        <?php elseif ($p['status'] == 'Terlambat'): ?>
                                            <span class="badge bg-warning">Terlambat</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?php echo $p['status']; ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (strpos($p['keterangan'], 'Absensi manual') !== false): ?>
                                            <span class="badge bg-info">Manual</span>
                                        <?php endif; ?>
                                        <?php if (strpos($p['keterangan'], 'Perjalanan Dinas') !== false): ?>
                                            <span class="badge bg-primary">Perjalanan Dinas</span>
                                        <?php endif; ?>
                                        <?php echo $p['keterangan'] ?? '-'; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="detail_presensi.php?id=<?php echo $p['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> Detail
                                            </a>
                                            <a href="edit_presensi.php?id=<?php echo $p['id']; ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <?php if (empty($p['jam_pulang']) && strpos($p['keterangan'], 'Perjalanan Dinas') === false): ?>
                                            <a href="tandai_dinas.php?id=<?php echo $p['id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-plane"></i> Tandai Dinas
                                            </a>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-sm btn-danger btn-delete" data-id="<?php echo $p['id']; ?>">
                                                <i class="fas fa-trash"></i> Hapus
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk menampilkan foto -->
<div class="modal fade" id="photoModal" tabindex="-1" aria-labelledby="photoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoModalLabel">Foto Presensi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalPhoto" src="" alt="Foto Presensi" class="img-fluid" style="max-height: 70vh;">
            </div>
            <div class="modal-footer">
                <a id="downloadPhoto" href="" download class="btn btn-success">
                    <i class="fas fa-download"></i> Download
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<script>
// Fungsi untuk menampilkan foto dalam modal
function showPhotoModal(photoUrl, title) {
    const modal = new bootstrap.Modal(document.getElementById('photoModal'));
    const modalPhoto = document.getElementById('modalPhoto');
    const modalTitle = document.getElementById('photoModalLabel');
    const downloadLink = document.getElementById('downloadPhoto');

    // Set foto dan title
    modalPhoto.src = photoUrl;
    modalTitle.textContent = title;
    downloadLink.href = photoUrl;

    // Tampilkan modal
    modal.show();
}

document.addEventListener('DOMContentLoaded', function() {
    // Konfirmasi hapus presensi
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');

            Swal.fire({
                title: 'Konfirmasi Hapus',
                text: 'Apakah Anda yakin ingin menghapus data presensi ini?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = 'hapus_presensi.php?id=' + id;
                }
            });
        });
    });
});
</script>
