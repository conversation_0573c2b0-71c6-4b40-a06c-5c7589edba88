<?php
/**
 * API untuk menandai pesan peringatan sebagai sudah dibaca
 */

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/pesan_functions.php';

// Start session jika belum dimulai
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set header untuk JSON response
header('Content-Type: application/json');

// Cek apakah user sudah login
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized. Please login first.'
    ]);
    exit;
}

// Cek apakah user adalah karyawan
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'karyawan') {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'Access denied. Only employees can mark messages as read.'
    ]);
    exit;
}

// Cek method request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use POST method.'
    ]);
    exit;
}

// Ambil data dari request
$raw_input = file_get_contents('php://input');
$input = json_decode($raw_input, true);

// Debug information (hapus di production)
error_log("API tandai_pesan_dibaca.php - Raw input: " . $raw_input);
error_log("API tandai_pesan_dibaca.php - Decoded input: " . print_r($input, true));

// Validasi input
if (!$input || !isset($input['pesan_id']) || empty($input['pesan_id'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Message ID is required.',
        'debug' => [
            'raw_input' => $raw_input,
            'decoded_input' => $input
        ]
    ]);
    exit;
}

$pesan_id = (int)$input['pesan_id'];
$user_id = (int)$_SESSION['user_id'];

try {
    // Cek apakah pesan ada dan milik user yang sedang login
    $check_query = "SELECT id, judul, status FROM pesan_peringatan 
                    WHERE id = $pesan_id AND user_id = $user_id";
    $check_result = mysqli_query($conn, $check_query);
    
    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Message not found or access denied.'
        ]);
        exit;
    }
    
    $pesan_data = mysqli_fetch_assoc($check_result);
    
    // Cek apakah pesan masih aktif
    if ($pesan_data['status'] !== 'aktif') {
        echo json_encode([
            'success' => false,
            'message' => 'Message is already marked as read or completed.',
            'current_status' => $pesan_data['status']
        ]);
        exit;
    }
    
    // Tandai pesan sebagai dibaca
    $success = tandaiPesanDibaca($pesan_id, $user_id);
    
    if ($success) {
        // Log aktivitas (opsional)
        $log_message = "Karyawan membaca pesan peringatan: " . $pesan_data['judul'];
        if (function_exists('logActivity')) {
            logActivity($user_id, 'read_warning', $log_message);
        }
        
        // Hitung sisa pesan aktif
        $sisa_pesan = hitungPesanAktif($user_id);
        
        echo json_encode([
            'success' => true,
            'message' => 'Message marked as read successfully.',
            'data' => [
                'pesan_id' => $pesan_id,
                'judul' => $pesan_data['judul'],
                'sisa_pesan_aktif' => $sisa_pesan,
                'tanggal_dibaca' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to mark message as read. Database error.',
            'error' => mysqli_error($conn)
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error.',
        'error' => $e->getMessage()
    ]);
}

/**
 * Fungsi untuk log aktivitas (jika belum ada)
 */
if (!function_exists('logActivity')) {
    function logActivity($user_id, $action, $description) {
        global $conn;
        
        $user_id = (int)$user_id;
        $action = mysqli_real_escape_string($conn, $action);
        $description = mysqli_real_escape_string($conn, $description);
        $timestamp = date('Y-m-d H:i:s');
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        
        $query = "INSERT INTO activity_log (user_id, action, description, ip_address, created_at) 
                  VALUES ($user_id, '$action', '$description', '$ip_address', '$timestamp')";
        
        // Jalankan query tapi jangan error jika tabel tidak ada
        @mysqli_query($conn, $query);
    }
}
?>
