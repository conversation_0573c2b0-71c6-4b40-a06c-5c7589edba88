<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses admin
checkAccess('admin');

// Proses hapus pesan
if (isset($_GET['delete']) && isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    $delete_query = "DELETE FROM pesan_peringatan WHERE id = $id";
    if (mysqli_query($conn, $delete_query)) {
        $_SESSION['success_message'] = "Pesan peringatan berhasil dihapus.";
    } else {
        $_SESSION['error_message'] = "Gagal menghapus pesan peringatan.";
    }
    header('Location: pesan_peringatan.php');
    exit;
}

// Query untuk mengambil data pesan peringatan
$query = "SELECT pp.*, u.nama, u.nik, admin.nama as admin_nama
          FROM pesan_peringatan pp
          JOIN users u ON pp.user_id = u.id
          JOIN users admin ON pp.dibuat_oleh = admin.id
          ORDER BY pp.tanggal_dibuat DESC";
$result = mysqli_query($conn, $query);

$pesan_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $pesan_list[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<style>
.badge-tingkat-ringan { background-color: #ffc107; }
.badge-tingkat-sedang { background-color: #fd7e14; }
.badge-tingkat-berat { background-color: #dc3545; }
.badge-status-aktif { background-color: #dc3545; }
.badge-status-dibaca { background-color: #6c757d; }
.badge-status-selesai { background-color: #198754; }
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Pesan Peringatan Karyawan
        </h1>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">Daftar Pesan Peringatan</h6>
            <a href="tambah_pesan_peringatan.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Buat Pesan Peringatan
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Karyawan</th>
                            <th>NIK</th>
                            <th>Judul</th>
                            <th>Jenis Pelanggaran</th>
                            <th>Tingkat</th>
                            <th>Status</th>
                            <th>Tanggal Dibuat</th>
                            <th>Dibuat Oleh</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($pesan_list)): ?>
                            <tr>
                                <td colspan="10" class="text-center">
                                    <div class="py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">Belum ada pesan peringatan</h5>
                                        <p class="text-muted">Klik tombol "Buat Pesan Peringatan" untuk membuat pesan baru.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($pesan_list as $index => $pesan): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td><?php echo $pesan['nama']; ?></td>
                                    <td><?php echo $pesan['nik']; ?></td>
                                    <td><?php echo $pesan['judul']; ?></td>
                                    <td>
                                        <?php
                                        $jenis_labels = [
                                            'terlambat' => 'Terlambat',
                                            'tidak_absen' => 'Tidak Absen',
                                            'pulang_awal' => 'Pulang Awal',
                                            'lainnya' => 'Lainnya'
                                        ];
                                        echo $jenis_labels[$pesan['jenis_pelanggaran']] ?? $pesan['jenis_pelanggaran'];
                                        ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-tingkat-<?php echo $pesan['tingkat_peringatan']; ?>">
                                            <?php echo ucfirst($pesan['tingkat_peringatan']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-status-<?php echo $pesan['status']; ?>">
                                            <?php echo ucfirst($pesan['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($pesan['tanggal_dibuat'])); ?></td>
                                    <td><?php echo $pesan['admin_nama']; ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="detail_pesan_peringatan.php?id=<?php echo $pesan['id']; ?>" 
                                               class="btn btn-sm btn-info" title="Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit_pesan_peringatan.php?id=<?php echo $pesan['id']; ?>" 
                                               class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="?delete=1&id=<?php echo $pesan['id']; ?>" 
                                               class="btn btn-sm btn-danger btn-delete" title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Konfirmasi hapus
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            if (confirm('Apakah Anda yakin ingin menghapus pesan peringatan ini?')) {
                window.location.href = this.href;
            }
        });
    });

    // Initialize DataTable jika ada data
    <?php if (!empty($pesan_list)): ?>
    $('#dataTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        },
        "order": [[ 7, "desc" ]], // Sort by tanggal dibuat
        "pageLength": 25
    });
    <?php endif; ?>
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
