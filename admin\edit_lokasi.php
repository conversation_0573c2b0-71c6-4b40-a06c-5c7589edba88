<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Cek apakah ada parameter id
if (!isset($_GET['id'])) {
    setMessage('danger', 'ID lokasi tidak valid');
    redirect('admin/lokasi.php');
}

$id = clean($_GET['id']);

// Ambil data lokasi berdasarkan id
$query = "SELECT * FROM lokasi WHERE id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Lokasi tidak ditemukan');
    redirect('admin/lokasi.php');
}

$lokasi = mysqli_fetch_assoc($result);

// Proses edit lokasi
if (isset($_POST['edit'])) {
    $nama_lokasi = clean($_POST['nama_lokasi']);
    $latitude = clean($_POST['latitude']);
    $longitude = clean($_POST['longitude']);
    $radius = clean($_POST['radius']);

    // Validasi input
    if (empty($nama_lokasi) || empty($latitude) || empty($longitude) || empty($radius)) {
        setMessage('danger', 'Semua field harus diisi');
        redirect("edit_lokasi.php?id=$id");
    }

    // Validasi koordinat
    if (!is_numeric($latitude) || !is_numeric($longitude) || !is_numeric($radius)) {
        setMessage('danger', 'Koordinat dan radius harus berupa angka');
        redirect("edit_lokasi.php?id=$id");
    }



    // Update lokasi
    $query = "UPDATE lokasi SET
              nama_lokasi = '$nama_lokasi',
              latitude = '$latitude',
              longitude = '$longitude',
              radius = '$radius'
              WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Lokasi berhasil diupdate');
        redirect('admin/lokasi.php');
    } else {
        setMessage('danger', 'Gagal mengupdate lokasi: ' . mysqli_error($conn));
        redirect("edit_lokasi.php?id=$id");
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Edit Lokasi</h1>
        <a href="lokasi.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Form Edit Lokasi</h6>
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nama_lokasi" class="form-label">Nama Lokasi</label>
                            <input type="text" class="form-control" id="nama_lokasi" name="nama_lokasi" value="<?php echo $lokasi['nama_lokasi']; ?>" required>
                        </div>




                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="latitude" class="form-label">Latitude</label>
                            <input type="text" class="form-control" id="latitude" name="latitude" value="<?php echo $lokasi['latitude']; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="longitude" class="form-label">Longitude</label>
                            <input type="text" class="form-control" id="longitude" name="longitude" value="<?php echo $lokasi['longitude']; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="radius" class="form-label">Radius (meter)</label>
                            <input type="number" class="form-control" id="radius" name="radius" value="<?php echo $lokasi['radius']; ?>" required>
                            <small class="text-muted">Radius dalam meter untuk menentukan batas area absensi</small>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Pilih Lokasi di Peta</label>
                    <div id="map" style="height: 400px; border-radius: 10px;"></div>
                    <small class="text-muted">Klik pada peta untuk menentukan lokasi</small>
                </div>

                <div class="mt-3">
                    <button type="submit" name="edit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                    <a href="lokasi.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inisialisasi peta
        var map = L.map('map').setView([<?php echo $lokasi['latitude']; ?>, <?php echo $lokasi['longitude']; ?>], 15);

        // Tambahkan tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Tambahkan marker
        var marker = L.marker([<?php echo $lokasi['latitude']; ?>, <?php echo $lokasi['longitude']; ?>], {
            draggable: true
        }).addTo(map);

        // Tambahkan circle untuk radius
        var circle = L.circle([<?php echo $lokasi['latitude']; ?>, <?php echo $lokasi['longitude']; ?>], {
            radius: <?php echo $lokasi['radius']; ?>,
            color: '#4e73df',
            fillColor: '#4e73df',
            fillOpacity: 0.2
        }).addTo(map);

        // Update koordinat saat marker di-drag
        marker.on('dragend', function(e) {
            var position = marker.getLatLng();
            document.getElementById('latitude').value = position.lat.toFixed(6);
            document.getElementById('longitude').value = position.lng.toFixed(6);
            circle.setLatLng(position);
        });

        // Update koordinat saat klik pada peta
        map.on('click', function(e) {
            marker.setLatLng(e.latlng);
            document.getElementById('latitude').value = e.latlng.lat.toFixed(6);
            document.getElementById('longitude').value = e.latlng.lng.toFixed(6);
            circle.setLatLng(e.latlng);
        });

        // Update radius saat nilai input berubah
        document.getElementById('radius').addEventListener('input', function() {
            var radius = parseInt(this.value);
            circle.setRadius(radius);
        });
    });
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
