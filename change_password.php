<?php
// Include file konfigurasi
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Cek login
if (!isset($_SESSION['user_id'])) {
    redirect('index.php');
}

// Cek role
$is_admin = isset($_SESSION['role']) && $_SESSION['role'] == 'admin';
$is_mobile = true;

// Proses ubah password
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $old_password = $_POST['old_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    if (empty($old_password) || empty($new_password) || empty($confirm_password)) {
        setMessage('danger', 'Semua field harus diisi!');
    } elseif ($new_password != $confirm_password) {
        setMessage('danger', 'Password baru dan konfirmasi password tidak sama!');
    } elseif (strlen($new_password) < 6) {
        setMessage('danger', 'Password baru minimal 6 karakter!');
    } else {
        // Verifikasi password lama
        $user_id = $_SESSION['user_id'];
        $query = "SELECT * FROM users WHERE id = '$user_id'";
        $result = mysqli_query($conn, $query);

        if (mysqli_num_rows($result) > 0) {
            $user = mysqli_fetch_assoc($result);

            // Verifikasi password lama
            if (password_verify($old_password, $user['password'])) {
                // Update password baru
                $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                $query = "UPDATE users SET password = '$new_password_hash' WHERE id = '$user_id'";

                if (mysqli_query($conn, $query)) {
                    setMessage('success', 'Password berhasil diubah!');

                    // Redirect berdasarkan role
                    if ($is_admin) {
                        redirect('admin/index.php');
                    } else {
                        redirect('karyawan/index.php');
                    }
                } else {
                    setMessage('danger', 'Gagal mengubah password: ' . mysqli_error($conn));
                }
            } else {
                setMessage('danger', 'Password lama salah!');
            }
        } else {
            setMessage('danger', 'User tidak ditemukan!');
        }
    }
}

// Gunakan header tanpa sidebar
$no_sidebar = true;
include_once 'includes/header.php';
?>

<!-- Mobile CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/mobile-help.css">

<div class="mobile-password-container">
    <!-- Header -->
    <div class="mobile-password-header">
        <a href="<?php echo $is_admin ? 'admin/index.php' : 'karyawan/index.php'; ?>" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h4>Ubah Password</h4>
    </div>

    <!-- Content -->
    <div class="mobile-password-content">
        <div class="password-card">
            <div class="password-card-body">
                <?php
                // Tampilkan pesan jika ada
                $message = getMessage();
                if ($message): ?>
                    <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                        <?php echo $message['text']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form method="post" action="">
                    <div class="form-group">
                        <label for="old_password" class="form-label">Password Lama</label>
                        <input type="password" class="form-control" id="old_password" name="old_password" required>
                    </div>
                    <div class="form-group">
                        <label for="new_password" class="form-label">Password Baru</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                        <div class="form-text">Minimal 6 karakter</div>
                    </div>
                    <div class="form-group">
                        <label for="confirm_password" class="form-label">Konfirmasi Password Baru</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn-primary">Ubah Password</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>



<?php
// Include footer
include_once 'includes/footer.php';
?>
