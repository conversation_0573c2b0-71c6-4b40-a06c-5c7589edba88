/* Mobile Presensi Style */

/* Base Styles */
.mobile-presensi-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

@media (min-width: 768px) {
    .mobile-presensi-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-presensi-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-presensi-container {
        max-width: 840px;
    }
}

/* Header Styles */
.presensi-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.presensi-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.presensi-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    overflow: hidden;
    position: relative;
    padding: 2px;
}

.presensi-header .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
    object-position: center;
    background-color: rgba(255, 255, 255, 0.2);
}

.presensi-header .user-avatar .user-photo {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;
    max-height: 100%;
}

.presensi-header .user-details {
    flex: 1;
}

.presensi-header .user-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.presensi-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.presensi-header .date-info {
    display: flex;
    justify-content: space-between;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 15px;
    font-size: 14px;
}

.presensi-header .date-info i {
    margin-right: 5px;
}

/* Clock Styles */
.clock-container {
    text-align: center;
    margin: 20px 0;
}

.clock {
    font-size: 48px;
    font-weight: bold;
    color: #343a40;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 5px;
}

.date {
    font-size: 16px;
    color: #6c757d;
}

/* Status Card */
.status-card {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 15px;
}

.status-card .status-title {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.status-card .status-title i {
    margin-right: 10px;
    color: #4e73df;
}

.status-card .status-content {
    padding: 10px;
    border-radius: 10px;
    background-color: #f8f9fa;
}



/* Camera Preview */
.camera-preview {
    width: 100%;
    height: 450px; /* Ukuran tinggi ditambah dari 300px menjadi 450px */
    background-color: #343a40;
    border-radius: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.camera-preview i {
    font-size: 48px;
    opacity: 0.5;
    position: absolute;
    z-index: 5;
}

.camera-preview .camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px dashed rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    margin: 10px;
    pointer-events: none;
}

.camera-preview .camera-message {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 14px;
    opacity: 0.8;
    z-index: 10;
}

.camera-preview video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
    transform: scaleX(-1); /* Membalik video secara horizontal agar seperti cermin */
}

.camera-preview canvas {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 15px;
    transform: scaleX(-1); /* Membalik canvas secara horizontal agar seperti cermin */
}

/* Camera Preview untuk Desktop */
.camera-preview-desktop {
    width: 100%;
    height: 350px;
    background-color: #343a40;
    border-radius: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.camera-preview-desktop i {
    font-size: 48px;
    opacity: 0.5;
    position: absolute;
    z-index: 5;
}

.camera-preview-desktop .camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px dashed rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    margin: 10px;
    pointer-events: none;
}

.camera-preview-desktop .camera-message {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 14px;
    opacity: 0.8;
    z-index: 10;
}

.camera-preview-desktop video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
    transform: scaleX(-1); /* Membalik video secara horizontal agar seperti cermin */
}

.camera-preview-desktop canvas {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 15px;
    transform: scaleX(-1); /* Membalik canvas secara horizontal agar seperti cermin */
}

/* Attendance Buttons */
.attendance-btn {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 70px;
    border-radius: 50%;
    text-align: center;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    border: none;
    animation: pulse-btn 2s infinite;
}

.attendance-btn.check-in {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    color: white;
}

.attendance-btn.check-out {
    background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    color: white;
}

.attendance-btn:hover {
    transform: translateX(-50%) translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
    animation: none;
}

.attendance-btn:active {
    transform: translateX(-50%) translateY(0);
}

.attendance-btn i {
    font-size: 28px;
}

.attendance-btn span {
    display: none;
}

.attendance-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.7;
    animation: none;
}

@keyframes pulse-btn {
    0% {
        box-shadow: 0 0 0 0 rgba(28, 200, 138, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(28, 200, 138, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(28, 200, 138, 0);
    }
}

/* Info Section */
.info-section {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 20px;
}

.info-section .info-title {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.info-section .info-title i {
    margin-right: 10px;
    color: #4e73df;
}

.info-section .info-content {
    font-size: 14px;
    color: #6c757d;
}

.info-section .info-content ul {
    padding-left: 20px;
}

.info-section .info-content li {
    margin-bottom: 8px;
}

/* Location Map Button */
#viewMapBtn {
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#viewMapBtn:hover {
    background: linear-gradient(135deg, #258391 0%, #1a6570 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
}

#viewMapBtn:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

#viewMapBtn i {
    margin-right: 8px;
}

/* Location Indicator Styles */
.location-indicator {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    font-weight: 500;
}

.location-indicator i {
    font-size: 18px;
    margin-right: 10px;
}

.location-indicator.waiting {
    background-color: #fff3cd;
    color: #856404;
}

.location-indicator.in-radius {
    background-color: #d4edda;
    color: #155724;
}

.location-indicator.out-radius {
    background-color: #f8d7da;
    color: #721c24;
}



/* Floating Map Button */
#floating-map-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    z-index: 999; /* Sedikit lebih rendah dari tombol absen */
    transition: all 0.3s ease;
    animation: pulse-map 2s infinite;
}

#floating-map-btn i {
    font-size: 24px;
}

#floating-map-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
    animation: none;
}

#floating-map-btn:active {
    transform: translateY(0) scale(0.95);
}

@keyframes pulse-map {
    0% {
        box-shadow: 0 0 0 0 rgba(54, 185, 204, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(54, 185, 204, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(54, 185, 204, 0);
    }
}



/* Map Modal Styles */
.map-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    overflow: auto;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.action-button {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 12px 8px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-button .action-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    color: white;
    font-size: 18px;
}

.action-button .action-icon.primary {
    background-color: #4e73df;
}

.action-button .action-icon.success {
    background-color: #1cc88a;
}

.action-button .action-icon.warning {
    background-color: #f6c23e;
}

.action-button .action-icon.info {
    background-color: #36b9cc;
}

.action-button .action-icon.danger {
    background-color: #e74a3b;
}

.action-button .action-text {
    font-size: 14px;
    color: #343a40;
    font-weight: 500;
}

@media (min-width: 768px) {
    .quick-actions {
        grid-template-columns: repeat(4, 1fr);
    }
}

.map-modal-content {
    position: relative;
    background-color: #fff;
    margin: 10% auto;
    width: 90%;
    max-width: 800px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-50px);}
    to {opacity: 1; transform: translateY(0);}
}

.map-modal-header {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.map-modal-header h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.map-close {
    font-size: 28px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
}

.map-close:hover {
    color: #333;
}

.map-modal-body {
    padding: 0;
}

.map-modal-footer {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e9ecef;
}

.map-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #555;
}

.legend-item img, .legend-item .circle-icon {
    margin-right: 5px;
}

.circle-icon {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: rgba(78, 115, 223, 0.2);
    border: 2px solid #4e73df;
}

#leaflet-map {
    width: 100%;
    height: 400px;
    border-radius: 0 0 15px 15px;
}

/* Distance Info Styles */
.distance-info {
    padding: 6px 8px;
    background: white;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    font-size: 14px;
}

.distance-box {
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 0;
}

.distance-box.in-radius {
    background-color: rgba(212, 237, 218, 0.9);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.distance-box.out-radius {
    background-color: rgba(248, 215, 218, 0.9);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* SweetAlert2 Custom Styles */
.swal2-popup {
    border-radius: 15px;
    padding: 2em;
}

.swal2-title {
    font-size: 1.5em;
    font-weight: 600;
}

.swal2-content {
    font-size: 1em;
}

.swal2-icon {
    margin: 1.5em auto;
}

.swal2-actions {
    margin-top: 1.5em;
}

.swal2-confirm {
    padding: 0.6em 2em;
    font-weight: 600;
}

.swal2-cancel {
    padding: 0.6em 2em;
    font-weight: 600;
}

.swal2-footer {
    margin-top: 1em;
    padding-top: 1em;
    font-size: 0.9em;
}

.swal2-footer a {
    color: #4e73df;
    text-decoration: none;
}

.swal2-footer a:hover {
    text-decoration: underline;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .presensi-header {
        padding: 15px;
    }

    .clock {
        font-size: 36px;
    }

    .camera-preview {
        height: 350px; /* Ukuran tinggi untuk layar kecil */
    }

    /* Adjust buttons for mobile with bottom nav */
    .attendance-btn {
        bottom: 80px !important;
    }

    #floating-map-btn {
        bottom: 80px !important;
        right: 20px !important;
    }

    /* Add extra padding to container to prevent content from being hidden */
    .mobile-presensi-container {
        padding-bottom: 1000px !important;
    }
}

@media (min-width: 992px) {
    .presensi-header {
        padding: 25px;
    }

    .camera-preview {
        height: 500px; /* Ukuran tinggi untuk layar besar */
    }

    /* Adjust buttons for desktop with bottom nav */
    .attendance-btn {
        bottom: 80px !important;
    }

    #floating-map-btn {
        bottom: 80px !important;
        right: 30px !important;
    }
}

/* Tambahan untuk layar tablet */
@media (min-width: 768px) and (max-width: 991px) {
    .camera-preview {
        height: 450px; /* Ukuran tinggi untuk tablet */
    }

    /* Adjust buttons for tablet with bottom nav */
    .attendance-btn {
        bottom: 80px !important;
    }

    #floating-map-btn {
        bottom: 80px !important;
        right: 30px !important;
    }
}
